#!/usr/bin/env python3
"""
Dead Code Analysis Runner for ASK Pipeline

This script runs the dead code analyzer on ask-related files to identify
unused imports, functions, commented code, and other dead code patterns.
"""

import sys
import os
import json
from pathlib import Path

# Add src to Python path to import the analyzer
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from bot.pipeline.commands.ask.cleanup.dead_code_analyzer import DeadCodeAnalyzer

def main():
    """Run dead code analysis on ask-related files"""
    print("Starting dead code analysis for ASK pipeline...")
    
    # Initialize analyzer
    analyzer = DeadCodeAnalyzer(project_root=".")
    
    # Analyze only ask-related files
    include_patterns = [
        "src/bot/pipeline/commands/ask/**/*.py",
        "src/shared/ai_services/*.py",  # Related AI services
        "src/shared/ai_chat/*.py",      # AI chat components
    ]
    
    exclude_patterns = [
        "**/__pycache__/**",
        "**/venv/**",
        "**/.git/**",
        "**/archive/**",  # Exclude archive directories
    ]
    
    # Run analysis
    print(f"Analyzing files matching: {include_patterns}")
    report = analyzer.analyze_project(include_patterns=include_patterns, exclude_patterns=exclude_patterns)
    
    # Export report
    output_file = "dead_code_analysis_report.json"
    analyzer.export_report(output_file)
    
    # Print summary
    print("\n=== Dead Code Analysis Summary ===")
    print(f"Total files analyzed: {report['total_files_analyzed']}")
    print(f"Files with issues: {report['files_with_issues']}")
    print(f"Unused imports: {report['summary']['unused_imports']}")
    print(f"Unused functions: {report['summary']['unused_functions']}")
    print(f"Unused classes: {report['summary']['unused_classes']}")
    print(f"Commented code: {report['summary']['commented_code']}")
    print(f"Debug statements: {report['summary']['debug_statements']}")
    print(f"TODO comments: {report['summary']['todo_comments']}")
    print(f"\nFull report exported to: {output_file}")
    
    # List files with issues
    if report['files_with_issues'] > 0:
        print("\nFiles with dead code issues:")
        for file_path, issues in report['files'].items():
            if any(issues.values()):
                print(f"\n{file_path}:")
                for issue_type, items in issues.items():
                    if items:
                        print(f"  {issue_type}: {len(items)} items")

if __name__ == "__main__":
    main()