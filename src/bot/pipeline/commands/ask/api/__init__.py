"""
API Design and Integration for ASK Pipeline

This module provides comprehensive API design and integration capabilities:

1. API Contracts - Standardized request/response models with validation
2. API Versioning - Version management and backward compatibility
3. Integration Patterns - Event-driven architecture and message queuing
4. Backward Compatibility - Legacy interface support and migration tools

Features:
- Consistent API contracts for all pipeline components
- Request/response validation with Pydantic models
- API versioning strategy for backward compatibility
- Event-driven architecture for component communication
- Message queuing for asynchronous operations
- Integration adapters for external services
- Retry logic and circuit breaker patterns
- Backward compatibility and migration tools
"""

from .contracts import (
    APIVersion,
    RequestStatus,
    ErrorCode,
    IntentType,
    BaseRequest,
    BaseResponse,
    ErrorResponse,
    AskRequest,
    AskResponse,
    IntentAnalysisRequest,
    IntentAnalysisResponse,
    SecurityValidationRequest,
    SecurityValidationResponse,
    RateLimitRequest,
    RateLimitResponse,
    AuthenticationRequest,
    AuthenticationResponse,
    HealthCheckRequest,
    HealthCheckResponse,
    MetricsRequest,
    MetricsResponse,
    ConfigurationRequest,
    ConfigurationResponse,
    FeatureFlagRequest,
    FeatureFlagResponse,
    BatchRequest,
    BatchResponse,
    WebhookRequest,
    WebhookResponse,
    API_CONTRACTS,
    get_contract,
    validate_request,
    validate_response,
    create_error_response
)

from .versioning import (
    VersionStatus,
    VersionInfo,
    VersionCompatibility,
    APIVersionManager,
    version_handler,
    migrate_request,
    migrate_response,
    get_version_manager,
    cleanup_version_manager
)

from .integration import (
    EventType,
    CircuitState,
    RetryStrategy,
    Event,
    RetryConfig,
    CircuitBreakerConfig,
    EventBus,
    MessageQueue,
    RetryManager,
    CircuitBreaker,
    IntegrationAdapter,
    ExternalServiceAdapter,
    IntegrationMonitor,
    get_event_bus,
    get_message_queue,
    get_integration_monitor,
    cleanup_integration
)

from .backward_compatibility import (
    CompatibilityLevel,
    MigrationStatus,
    CompatibilityInfo,
    MigrationPlan,
    BackwardCompatibilityManager,
    LegacyExecutorInterface,
    CompatibilityTestRunner,
    deprecated_interface,
    compatibility_wrapper,
    get_compatibility_manager,
    get_test_runner,
    cleanup_compatibility
)

__all__ = [
    # API Contracts
    'APIVersion',
    'RequestStatus',
    'ErrorCode',
    'IntentType',
    'BaseRequest',
    'BaseResponse',
    'ErrorResponse',
    'AskRequest',
    'AskResponse',
    'IntentAnalysisRequest',
    'IntentAnalysisResponse',
    'SecurityValidationRequest',
    'SecurityValidationResponse',
    'RateLimitRequest',
    'RateLimitResponse',
    'AuthenticationRequest',
    'AuthenticationResponse',
    'HealthCheckRequest',
    'HealthCheckResponse',
    'MetricsRequest',
    'MetricsResponse',
    'ConfigurationRequest',
    'ConfigurationResponse',
    'FeatureFlagRequest',
    'FeatureFlagResponse',
    'BatchRequest',
    'BatchResponse',
    'WebhookRequest',
    'WebhookResponse',
    'API_CONTRACTS',
    'get_contract',
    'validate_request',
    'validate_response',
    'create_error_response',
    
    # API Versioning
    'VersionStatus',
    'VersionInfo',
    'VersionCompatibility',
    'APIVersionManager',
    'version_handler',
    'migrate_request',
    'migrate_response',
    'get_version_manager',
    'cleanup_version_manager',
    
    # Integration Patterns
    'EventType',
    'CircuitState',
    'RetryStrategy',
    'Event',
    'RetryConfig',
    'CircuitBreakerConfig',
    'EventBus',
    'MessageQueue',
    'RetryManager',
    'CircuitBreaker',
    'IntegrationAdapter',
    'ExternalServiceAdapter',
    'IntegrationMonitor',
    'get_event_bus',
    'get_message_queue',
    'get_integration_monitor',
    'cleanup_integration',
    
    # Backward Compatibility
    'CompatibilityLevel',
    'MigrationStatus',
    'CompatibilityInfo',
    'MigrationPlan',
    'BackwardCompatibilityManager',
    'LegacyExecutorInterface',
    'CompatibilityTestRunner',
    'deprecated_interface',
    'compatibility_wrapper',
    'get_compatibility_manager',
    'get_test_runner',
    'cleanup_compatibility'
]
