"""
Audit Logging and Compliance Reporting for ASK Pipeline

Provides comprehensive audit logging and compliance reporting:
- Add comprehensive audit logging for all data access and modifications
- Create compliance reporting and regulatory documentation
- Implement data lineage tracking and provenance
- Add privacy impact assessments and risk analysis
- Create compliance monitoring and alerting
"""

import asyncio
import logging
import json
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import uuid
import hashlib

logger = logging.getLogger(__name__)

class AuditEventType(Enum):
    """Audit event types"""
    DATA_ACCESS = "data_access"
    DATA_MODIFICATION = "data_modification"
    DATA_DELETION = "data_deletion"
    DATA_CREATION = "data_creation"
    USER_LOGIN = "user_login"
    USER_LOGOUT = "user_logout"
    PERMISSION_CHANGE = "permission_change"
    CONFIGURATION_CHANGE = "configuration_change"
    SYSTEM_EVENT = "system_event"
    SECURITY_EVENT = "security_event"

class AuditSeverity(Enum):
    """Audit event severity"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ComplianceStandard(Enum):
    """Compliance standards"""
    GDPR = "gdpr"
    CCPA = "ccpa"
    HIPAA = "hipaa"
    SOX = "sox"
    PCI_DSS = "pci_dss"
    ISO27001 = "iso27001"

@dataclass
class AuditEvent:
    """Audit event record"""
    event_id: str
    event_type: AuditEventType
    severity: AuditSeverity
    timestamp: datetime
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    resource_id: Optional[str] = None
    action: str = ""
    description: str = ""
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    result: str = "success"  # success, failure, error
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    compliance_tags: List[ComplianceStandard] = field(default_factory=list)

@dataclass
class DataLineage:
    """Data lineage record"""
    lineage_id: str
    source_data_id: str
    target_data_id: str
    transformation_type: str
    timestamp: datetime
    user_id: Optional[str] = None
    transformation_details: Dict[str, Any] = field(default_factory=dict)
    compliance_impact: List[ComplianceStandard] = field(default_factory=list)

@dataclass
class PrivacyImpactAssessment:
    """Privacy Impact Assessment record"""
    pia_id: str
    data_processing_id: str
    assessment_date: datetime
    data_categories: List[str] = field(default_factory=list)
    processing_purposes: List[str] = field(default_factory=list)
    legal_basis: str = ""
    data_subjects: int = 0
    risk_level: str = "low"  # low, medium, high, critical
    mitigation_measures: List[str] = field(default_factory=list)
    approval_status: str = "pending"  # pending, approved, rejected
    reviewer_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=list)

@dataclass
class ComplianceReport:
    """Compliance report"""
    report_id: str
    standard: ComplianceStandard
    report_type: str
    generated_at: datetime
    period_start: datetime
    period_end: datetime
    total_events: int
    events_by_type: Dict[str, int] = field(default_factory=dict)
    events_by_severity: Dict[str, int] = field(default_factory=list)
    compliance_score: float = 0.0
    violations: List[Dict[str, Any]] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

class AuditLogger:
    """Audit logging and compliance system"""
    
    def __init__(self):
        self.audit_events: List[AuditEvent] = []
        self.data_lineage: List[DataLineage] = []
        self.privacy_assessments: List[PrivacyImpactAssessment] = []
        self.compliance_reports: List[ComplianceReport] = []
        
        # Audit configuration
        self.retention_days = 2555  # 7 years
        self.batch_size = 1000
        self.batch_timeout = 300  # 5 minutes
        
        # Start audit processing
        self.processing_task: Optional[asyncio.Task] = None
        asyncio.create_task(self.start_audit_processing())
    
    async def start_audit_processing(self):
        """Start audit event processing"""
        if self.processing_task:
            return
        
        self.processing_task = asyncio.create_task(self._audit_processing_loop())
        logger.info("Audit processing started")
    
    async def stop_audit_processing(self):
        """Stop audit event processing"""
        if self.processing_task:
            self.processing_task.cancel()
            self.processing_task = None
        logger.info("Audit processing stopped")
    
    async def _audit_processing_loop(self):
        """Audit event processing loop"""
        while True:
            try:
                await self._process_audit_events()
                await self._cleanup_old_events()
                await asyncio.sleep(60)  # Process every minute
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Audit processing error: {e}")
                await asyncio.sleep(60)
    
    async def _process_audit_events(self):
        """Process audit events"""
        # In a real implementation, this would process events in batches
        # and send them to external audit systems
        pass
    
    async def _cleanup_old_events(self):
        """Cleanup old audit events"""
        cutoff_date = datetime.utcnow() - timedelta(days=self.retention_days)
        
        # Remove old events
        original_count = len(self.audit_events)
        self.audit_events = [
            event for event in self.audit_events
            if event.timestamp > cutoff_date
        ]
        
        removed_count = original_count - len(self.audit_events)
        if removed_count > 0:
            logger.info(f"Cleaned up {removed_count} old audit events")
    
    def log_audit_event(self, event_type: AuditEventType, action: str,
                       description: str, user_id: Optional[str] = None,
                       resource_id: Optional[str] = None, severity: AuditSeverity = AuditSeverity.MEDIUM,
                       result: str = "success", error_message: Optional[str] = None,
                       metadata: Dict[str, Any] = None, compliance_tags: List[ComplianceStandard] = None) -> str:
        """Log an audit event"""
        event_id = str(uuid.uuid4())
        
        event = AuditEvent(
            event_id=event_id,
            event_type=event_type,
            severity=severity,
            timestamp=datetime.utcnow(),
            user_id=user_id,
            action=action,
            description=description,
            resource_id=resource_id,
            result=result,
            error_message=error_message,
            metadata=metadata or {},
            compliance_tags=compliance_tags or []
        )
        
        self.audit_events.append(event)
        
        # Log to standard logger as well
        log_level = {
            AuditSeverity.LOW: logging.INFO,
            AuditSeverity.MEDIUM: logging.WARNING,
            AuditSeverity.HIGH: logging.ERROR,
            AuditSeverity.CRITICAL: logging.CRITICAL
        }.get(severity, logging.INFO)
        
        logger.log(log_level, f"Audit Event: {event_type.value} - {action} - {description}")
        
        return event_id
    
    def log_data_access(self, user_id: str, resource_id: str, data_type: str,
                       access_method: str = "read", result: str = "success") -> str:
        """Log data access event"""
        return self.log_audit_event(
            event_type=AuditEventType.DATA_ACCESS,
            action=f"access_{access_method}",
            description=f"Accessed {data_type} data",
            user_id=user_id,
            resource_id=resource_id,
            severity=AuditSeverity.MEDIUM,
            result=result,
            compliance_tags=[ComplianceStandard.GDPR, ComplianceStandard.CCPA]
        )
    
    def log_data_modification(self, user_id: str, resource_id: str, data_type: str,
                            modification_type: str = "update", result: str = "success") -> str:
        """Log data modification event"""
        return self.log_audit_event(
            event_type=AuditEventType.DATA_MODIFICATION,
            action=f"modify_{modification_type}",
            description=f"Modified {data_type} data",
            user_id=user_id,
            resource_id=resource_id,
            severity=AuditSeverity.HIGH,
            result=result,
            compliance_tags=[ComplianceStandard.GDPR, ComplianceStandard.CCPA]
        )
    
    def log_data_deletion(self, user_id: str, resource_id: str, data_type: str,
                         deletion_reason: str = "user_request", result: str = "success") -> str:
        """Log data deletion event"""
        return self.log_audit_event(
            event_type=AuditEventType.DATA_DELETION,
            action="delete",
            description=f"Deleted {data_type} data - {deletion_reason}",
            user_id=user_id,
            resource_id=resource_id,
            severity=AuditSeverity.HIGH,
            result=result,
            compliance_tags=[ComplianceStandard.GDPR, ComplianceStandard.CCPA]
        )
    
    def log_user_authentication(self, user_id: str, action: str, result: str = "success",
                               ip_address: Optional[str] = None, user_agent: Optional[str] = None) -> str:
        """Log user authentication event"""
        event_type = AuditEventType.USER_LOGIN if action == "login" else AuditEventType.USER_LOGOUT
        
        return self.log_audit_event(
            event_type=event_type,
            action=action,
            description=f"User {action}",
            user_id=user_id,
            severity=AuditSeverity.MEDIUM,
            result=result,
            ip_address=ip_address,
            user_agent=user_agent,
            compliance_tags=[ComplianceStandard.GDPR, ComplianceStandard.CCPA]
        )
    
    def log_security_event(self, event_description: str, severity: AuditSeverity = AuditSeverity.HIGH,
                          user_id: Optional[str] = None, resource_id: Optional[str] = None,
                          metadata: Dict[str, Any] = None) -> str:
        """Log security event"""
        return self.log_audit_event(
            event_type=AuditEventType.SECURITY_EVENT,
            action="security_incident",
            description=event_description,
            user_id=user_id,
            resource_id=resource_id,
            severity=severity,
            result="failure",
            metadata=metadata,
            compliance_tags=[ComplianceStandard.GDPR, ComplianceStandard.CCPA, ComplianceStandard.ISO27001]
        )
    
    def create_data_lineage(self, source_data_id: str, target_data_id: str,
                          transformation_type: str, user_id: Optional[str] = None,
                          transformation_details: Dict[str, Any] = None) -> str:
        """Create data lineage record"""
        lineage_id = str(uuid.uuid4())
        
        lineage = DataLineage(
            lineage_id=lineage_id,
            source_data_id=source_data_id,
            target_data_id=target_data_id,
            transformation_type=transformation_type,
            timestamp=datetime.utcnow(),
            user_id=user_id,
            transformation_details=transformation_details or {},
            compliance_impact=[ComplianceStandard.GDPR, ComplianceStandard.CCPA]
        )
        
        self.data_lineage.append(lineage)
        logger.info(f"Created data lineage: {lineage_id}")
        return lineage_id
    
    def create_privacy_impact_assessment(self, data_processing_id: str,
                                       data_categories: List[str],
                                       processing_purposes: List[str],
                                       legal_basis: str, data_subjects: int) -> str:
        """Create Privacy Impact Assessment"""
        pia_id = str(uuid.uuid4())
        
        # Calculate risk level based on data categories and purposes
        risk_level = self._calculate_pia_risk_level(data_categories, processing_purposes, data_subjects)
        
        pia = PrivacyImpactAssessment(
            pia_id=pia_id,
            data_processing_id=data_processing_id,
            assessment_date=datetime.utcnow(),
            data_categories=data_categories,
            processing_purposes=processing_purposes,
            legal_basis=legal_basis,
            data_subjects=data_subjects,
            risk_level=risk_level,
            mitigation_measures=self._get_mitigation_measures(risk_level)
        )
        
        self.privacy_assessments.append(pia)
        logger.info(f"Created PIA: {pia_id}")
        return pia_id
    
    def _calculate_pia_risk_level(self, data_categories: List[str], 
                                 processing_purposes: List[str], data_subjects: int) -> str:
        """Calculate PIA risk level"""
        risk_score = 0
        
        # Data categories risk
        high_risk_categories = ['health', 'financial', 'biometric', 'genetic']
        medium_risk_categories = ['location', 'behavioral', 'preferences']
        
        for category in data_categories:
            if any(high_risk in category.lower() for high_risk in high_risk_categories):
                risk_score += 3
            elif any(medium_risk in category.lower() for medium_risk in medium_risk_categories):
                risk_score += 2
            else:
                risk_score += 1
        
        # Processing purposes risk
        high_risk_purposes = ['profiling', 'automated_decision', 'marketing', 'research']
        medium_risk_purposes = ['analytics', 'improvement', 'personalization']
        
        for purpose in processing_purposes:
            if any(high_risk in purpose.lower() for high_risk in high_risk_purposes):
                risk_score += 2
            elif any(medium_risk in purpose.lower() for medium_risk in medium_risk_purposes):
                risk_score += 1
        
        # Data subjects count risk
        if data_subjects > 10000:
            risk_score += 2
        elif data_subjects > 1000:
            risk_score += 1
        
        # Determine risk level
        if risk_score >= 8:
            return "critical"
        elif risk_score >= 6:
            return "high"
        elif risk_score >= 4:
            return "medium"
        else:
            return "low"
    
    def _get_mitigation_measures(self, risk_level: str) -> List[str]:
        """Get mitigation measures for risk level"""
        measures = {
            "low": [
                "Document data processing activities",
                "Implement basic access controls"
            ],
            "medium": [
                "Document data processing activities",
                "Implement access controls and logging",
                "Conduct regular privacy reviews",
                "Implement data minimization"
            ],
            "high": [
                "Document data processing activities",
                "Implement strong access controls and logging",
                "Conduct regular privacy reviews",
                "Implement data minimization and purpose limitation",
                "Conduct Data Protection Impact Assessment (DPIA)",
                "Implement privacy by design"
            ],
            "critical": [
                "Document data processing activities",
                "Implement comprehensive access controls and logging",
                "Conduct frequent privacy reviews",
                "Implement data minimization and purpose limitation",
                "Conduct mandatory Data Protection Impact Assessment (DPIA)",
                "Implement privacy by design and default",
                "Obtain explicit consent where required",
                "Implement additional technical and organizational measures"
            ]
        }
        
        return measures.get(risk_level, measures["low"])
    
    def generate_compliance_report(self, standard: ComplianceStandard, 
                                 report_type: str = "monthly") -> ComplianceReport:
        """Generate compliance report for standard"""
        now = datetime.utcnow()
        
        if report_type == "monthly":
            period_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            period_end = now
        elif report_type == "quarterly":
            quarter = (now.month - 1) // 3 + 1
            period_start = now.replace(month=(quarter - 1) * 3 + 1, day=1, hour=0, minute=0, second=0, microsecond=0)
            period_end = now
        else:
            period_start = now - timedelta(days=30)
            period_end = now
        
        # Filter events for the period
        period_events = [
            event for event in self.audit_events
            if period_start <= event.timestamp <= period_end and standard in event.compliance_tags
        ]
        
        # Count events by type and severity
        events_by_type = {}
        events_by_severity = {}
        
        for event in period_events:
            event_type = event.event_type.value
            severity = event.severity.value
            
            events_by_type[event_type] = events_by_type.get(event_type, 0) + 1
            events_by_severity[severity] = events_by_severity.get(severity, 0) + 1
        
        # Calculate compliance score
        compliance_score = self._calculate_compliance_score(period_events, standard)
        
        # Identify violations
        violations = self._identify_compliance_violations(period_events, standard)
        
        # Generate recommendations
        recommendations = self._generate_compliance_recommendations(period_events, standard)
        
        report = ComplianceReport(
            report_id=str(uuid.uuid4()),
            standard=standard,
            report_type=report_type,
            generated_at=now,
            period_start=period_start,
            period_end=period_end,
            total_events=len(period_events),
            events_by_type=events_by_type,
            events_by_severity=events_by_severity,
            compliance_score=compliance_score,
            violations=violations,
            recommendations=recommendations
        )
        
        self.compliance_reports.append(report)
        logger.info(f"Generated compliance report: {report.report_id}")
        return report
    
    def _calculate_compliance_score(self, events: List[AuditEvent], standard: ComplianceStandard) -> float:
        """Calculate compliance score for standard"""
        if not events:
            return 100.0
        
        # Base score
        score = 100.0
        
        # Deduct points for violations
        for event in events:
            if event.result == "failure":
                if event.severity == AuditSeverity.CRITICAL:
                    score -= 10
                elif event.severity == AuditSeverity.HIGH:
                    score -= 5
                elif event.severity == AuditSeverity.MEDIUM:
                    score -= 2
                else:
                    score -= 1
        
        # Ensure score is between 0 and 100
        return max(0.0, min(100.0, score))
    
    def _identify_compliance_violations(self, events: List[AuditEvent], 
                                      standard: ComplianceStandard) -> List[Dict[str, Any]]:
        """Identify compliance violations"""
        violations = []
        
        for event in events:
            if event.result == "failure" and event.severity in [AuditSeverity.HIGH, AuditSeverity.CRITICAL]:
                violation = {
                    "event_id": event.event_id,
                    "timestamp": event.timestamp.isoformat(),
                    "type": event.event_type.value,
                    "severity": event.severity.value,
                    "description": event.description,
                    "error_message": event.error_message,
                    "user_id": event.user_id,
                    "resource_id": event.resource_id
                }
                violations.append(violation)
        
        return violations
    
    def _generate_compliance_recommendations(self, events: List[AuditEvent], 
                                           standard: ComplianceStandard) -> List[str]:
        """Generate compliance recommendations"""
        recommendations = []
        
        # Analyze event patterns
        failure_events = [e for e in events if e.result == "failure"]
        high_severity_events = [e for e in events if e.severity in [AuditSeverity.HIGH, AuditSeverity.CRITICAL]]
        
        if len(failure_events) > len(events) * 0.1:  # More than 10% failures
            recommendations.append("Review and improve system reliability to reduce failure rates")
        
        if len(high_severity_events) > 0:
            recommendations.append("Investigate and address high-severity security events")
        
        # Standard-specific recommendations
        if standard == ComplianceStandard.GDPR:
            recommendations.extend([
                "Ensure all data processing activities are documented",
                "Implement data subject rights management",
                "Conduct regular privacy impact assessments"
            ])
        elif standard == ComplianceStandard.CCPA:
            recommendations.extend([
                "Implement consumer rights management",
                "Ensure transparent data collection practices",
                "Implement opt-out mechanisms"
            ])
        
        return recommendations
    
    def get_audit_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get audit summary for specified hours"""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        recent_events = [e for e in self.audit_events if e.timestamp > cutoff_time]
        
        summary = {
            "period_hours": hours,
            "total_events": len(recent_events),
            "events_by_type": {},
            "events_by_severity": {},
            "events_by_result": {},
            "top_users": {},
            "recent_violations": 0
        }
        
        # Count by various dimensions
        for event in recent_events:
            # By type
            event_type = event.event_type.value
            summary["events_by_type"][event_type] = summary["events_by_type"].get(event_type, 0) + 1
            
            # By severity
            severity = event.severity.value
            summary["events_by_severity"][severity] = summary["events_by_severity"].get(severity, 0) + 1
            
            # By result
            result = event.result
            summary["events_by_result"][result] = summary["events_by_result"].get(result, 0) + 1
            
            # Top users
            if event.user_id:
                summary["top_users"][event.user_id] = summary["top_users"].get(event.user_id, 0) + 1
            
            # Violations
            if event.result == "failure" and event.severity in [AuditSeverity.HIGH, AuditSeverity.CRITICAL]:
                summary["recent_violations"] += 1
        
        return summary
    
    def export_audit_data(self, output_file: str) -> bool:
        """Export audit data to JSON file"""
        try:
            data = {
                "timestamp": datetime.utcnow().isoformat(),
                "audit_events": [
                    {
                        "event_id": event.event_id,
                        "event_type": event.event_type.value,
                        "severity": event.severity.value,
                        "timestamp": event.timestamp.isoformat(),
                        "user_id": event.user_id,
                        "session_id": event.session_id,
                        "resource_id": event.resource_id,
                        "action": event.action,
                        "description": event.description,
                        "ip_address": event.ip_address,
                        "user_agent": event.user_agent,
                        "result": event.result,
                        "error_message": event.error_message,
                        "metadata": event.metadata,
                        "compliance_tags": [tag.value for tag in event.compliance_tags]
                    }
                    for event in self.audit_events
                ],
                "data_lineage": [
                    {
                        "lineage_id": lineage.lineage_id,
                        "source_data_id": lineage.source_data_id,
                        "target_data_id": lineage.target_data_id,
                        "transformation_type": lineage.transformation_type,
                        "timestamp": lineage.timestamp.isoformat(),
                        "user_id": lineage.user_id,
                        "transformation_details": lineage.transformation_details,
                        "compliance_impact": [tag.value for tag in lineage.compliance_impact]
                    }
                    for lineage in self.data_lineage
                ],
                "privacy_assessments": [
                    {
                        "pia_id": pia.pia_id,
                        "data_processing_id": pia.data_processing_id,
                        "assessment_date": pia.assessment_date.isoformat(),
                        "data_categories": pia.data_categories,
                        "processing_purposes": pia.processing_purposes,
                        "legal_basis": pia.legal_basis,
                        "data_subjects": pia.data_subjects,
                        "risk_level": pia.risk_level,
                        "mitigation_measures": pia.mitigation_measures,
                        "approval_status": pia.approval_status,
                        "reviewer_id": pia.reviewer_id,
                        "metadata": pia.metadata
                    }
                    for pia in self.privacy_assessments
                ],
                "audit_summary": self.get_audit_summary()
            }
            
            with open(output_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            
            logger.info(f"Audit data exported: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting audit data: {e}")
            return False

# Global instance
_audit_logger: Optional[AuditLogger] = None

def get_audit_logger() -> AuditLogger:
    """Get global audit logger"""
    global _audit_logger
    if _audit_logger is None:
        _audit_logger = AuditLogger()
    return _audit_logger

def cleanup_audit_logger():
    """Cleanup global audit logger"""
    global _audit_logger
    if _audit_logger:
        _audit_logger = None
