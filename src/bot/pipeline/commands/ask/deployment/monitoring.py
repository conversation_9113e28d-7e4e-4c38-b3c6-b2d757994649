"""
Monitoring and Alerting for ASK Pipeline

Provides comprehensive monitoring capabilities:
- Create operational dashboards with Grafana
- Implement alerting rules for critical failures
- Add performance monitoring and SLA tracking
- Create incident response procedures and runbooks
- Implement automated recovery and self-healing
"""

import asyncio
import json
import logging
import time
from typing import Dict, Any, Optional, List, Union, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import psutil
import requests
from pathlib import Path

logger = logging.getLogger(__name__)

class AlertSeverity(Enum):
    """Alert severity enumeration"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class AlertStatus(Enum):
    """Alert status enumeration"""
    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"

class MetricType(Enum):
    """Metric type enumeration"""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    SUMMARY = "summary"

@dataclass
class AlertRule:
    """Alert rule configuration"""
    name: str
    description: str
    query: str
    threshold: float
    severity: AlertSeverity
    duration: int = 0  # seconds
    enabled: bool = True
    labels: Dict[str, str] = field(default_factory=dict)
    annotations: Dict[str, str] = field(default_factory=dict)

@dataclass
class Alert:
    """Alert instance"""
    alert_id: str
    rule_name: str
    status: AlertStatus
    severity: AlertSeverity
    message: str
    created_at: datetime
    resolved_at: Optional[datetime] = None
    acknowledged_at: Optional[datetime] = None
    acknowledged_by: Optional[str] = None
    labels: Dict[str, str] = field(default_factory=dict)
    annotations: Dict[str, str] = field(default_factory=dict)

@dataclass
class Metric:
    """Metric data point"""
    name: str
    value: float
    timestamp: datetime
    labels: Dict[str, str] = field(default_factory=dict)
    metric_type: MetricType = MetricType.GAUGE

@dataclass
class SLATarget:
    """SLA target configuration"""
    name: str
    target_value: float
    measurement_window: int  # seconds
    current_value: float = 0.0
    last_updated: Optional[datetime] = None

class MetricsCollector:
    """Metrics collection system"""
    
    def __init__(self):
        self.metrics: List[Metric] = []
        self.collectors: Dict[str, Callable] = {}
        self.collection_interval: int = 30
        self.collection_task: Optional[asyncio.Task] = None
        
        # Register default collectors
        self._register_default_collectors()
    
    def _register_default_collectors(self):
        """Register default metric collectors"""
        self.collectors["system_cpu"] = self._collect_cpu_metrics
        self.collectors["system_memory"] = self._collect_memory_metrics
        self.collectors["system_disk"] = self._collect_disk_metrics
        self.collectors["system_network"] = self._collect_network_metrics
        self.collectors["application_requests"] = self._collect_request_metrics
        self.collectors["application_errors"] = self._collect_error_metrics
    
    async def start_collection(self):
        """Start metrics collection"""
        if self.collection_task:
            return
        
        self.collection_task = asyncio.create_task(self._collection_loop())
        logger.info("Metrics collection started")
    
    async def stop_collection(self):
        """Stop metrics collection"""
        if self.collection_task:
            self.collection_task.cancel()
            self.collection_task = None
        logger.info("Metrics collection stopped")
    
    async def _collection_loop(self):
        """Metrics collection loop"""
        while True:
            try:
                await self._collect_all_metrics()
                await asyncio.sleep(self.collection_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Metrics collection error: {e}")
                await asyncio.sleep(self.collection_interval)
    
    async def _collect_all_metrics(self):
        """Collect all registered metrics"""
        for name, collector in self.collectors.items():
            try:
                metrics = await collector()
                self.metrics.extend(metrics)
                
                # Keep only last 10000 metrics
                if len(self.metrics) > 10000:
                    self.metrics = self.metrics[-10000:]
                    
            except Exception as e:
                logger.error(f"Error collecting metrics from {name}: {e}")
    
    async def _collect_cpu_metrics(self) -> List[Metric]:
        """Collect CPU metrics"""
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        
        return [
            Metric("system_cpu_percent", cpu_percent, datetime.utcnow()),
            Metric("system_cpu_count", cpu_count, datetime.utcnow())
        ]
    
    async def _collect_memory_metrics(self) -> List[Metric]:
        """Collect memory metrics"""
        memory = psutil.virtual_memory()
        
        return [
            Metric("system_memory_percent", memory.percent, datetime.utcnow()),
            Metric("system_memory_used", memory.used, datetime.utcnow()),
            Metric("system_memory_available", memory.available, datetime.utcnow()),
            Metric("system_memory_total", memory.total, datetime.utcnow())
        ]
    
    async def _collect_disk_metrics(self) -> List[Metric]:
        """Collect disk metrics"""
        disk = psutil.disk_usage('/')
        
        return [
            Metric("system_disk_percent", (disk.used / disk.total) * 100, datetime.utcnow()),
            Metric("system_disk_used", disk.used, datetime.utcnow()),
            Metric("system_disk_free", disk.free, datetime.utcnow()),
            Metric("system_disk_total", disk.total, datetime.utcnow())
        ]
    
    async def _collect_network_metrics(self) -> List[Metric]:
        """Collect network metrics"""
        network = psutil.net_io_counters()
        
        return [
            Metric("system_network_bytes_sent", network.bytes_sent, datetime.utcnow()),
            Metric("system_network_bytes_recv", network.bytes_recv, datetime.utcnow()),
            Metric("system_network_packets_sent", network.packets_sent, datetime.utcnow()),
            Metric("system_network_packets_recv", network.packets_recv, datetime.utcnow())
        ]
    
    async def _collect_request_metrics(self) -> List[Metric]:
        """Collect application request metrics"""
        # This would integrate with actual application metrics
        return [
            Metric("application_requests_total", 100, datetime.utcnow()),
            Metric("application_requests_per_second", 10.5, datetime.utcnow()),
            Metric("application_response_time_avg", 150.0, datetime.utcnow())
        ]
    
    async def _collect_error_metrics(self) -> List[Metric]:
        """Collect application error metrics"""
        # This would integrate with actual application metrics
        return [
            Metric("application_errors_total", 5, datetime.utcnow()),
            Metric("application_error_rate", 0.05, datetime.utcnow())
        ]
    
    def register_collector(self, name: str, collector: Callable):
        """Register custom metric collector"""
        self.collectors[name] = collector
        logger.info(f"Registered metric collector: {name}")
    
    def get_metrics(self, name: Optional[str] = None, 
                   start_time: Optional[datetime] = None,
                   end_time: Optional[datetime] = None) -> List[Metric]:
        """Get metrics with optional filtering"""
        metrics = self.metrics
        
        if name:
            metrics = [m for m in metrics if m.name == name]
        
        if start_time:
            metrics = [m for m in metrics if m.timestamp >= start_time]
        
        if end_time:
            metrics = [m for m in metrics if m.timestamp <= end_time]
        
        return sorted(metrics, key=lambda m: m.timestamp)

class AlertManager:
    """Alert management system"""
    
    def __init__(self):
        self.rules: Dict[str, AlertRule] = {}
        self.alerts: Dict[str, Alert] = {}
        self.notification_handlers: List[Callable] = []
        
        # Initialize default alert rules
        self._initialize_default_rules()
    
    def _initialize_default_rules(self):
        """Initialize default alert rules"""
        default_rules = [
            AlertRule(
                name="high_cpu_usage",
                description="CPU usage is too high",
                query="system_cpu_percent > 80",
                threshold=80.0,
                severity=AlertSeverity.HIGH,
                duration=300
            ),
            AlertRule(
                name="high_memory_usage",
                description="Memory usage is too high",
                query="system_memory_percent > 90",
                threshold=90.0,
                severity=AlertSeverity.CRITICAL,
                duration=60
            ),
            AlertRule(
                name="high_disk_usage",
                description="Disk usage is too high",
                query="system_disk_percent > 85",
                threshold=85.0,
                severity=AlertSeverity.HIGH,
                duration=600
            ),
            AlertRule(
                name="high_error_rate",
                description="Application error rate is too high",
                query="application_error_rate > 0.1",
                threshold=0.1,
                severity=AlertSeverity.CRITICAL,
                duration=120
            ),
            AlertRule(
                name="low_response_time",
                description="Application response time is too high",
                query="application_response_time_avg > 1000",
                threshold=1000.0,
                severity=AlertSeverity.MEDIUM,
                duration=300
            )
        ]
        
        for rule in default_rules:
            self.rules[rule.name] = rule
    
    def add_rule(self, rule: AlertRule):
        """Add alert rule"""
        self.rules[rule.name] = rule
        logger.info(f"Added alert rule: {rule.name}")
    
    def remove_rule(self, rule_name: str):
        """Remove alert rule"""
        if rule_name in self.rules:
            del self.rules[rule_name]
            logger.info(f"Removed alert rule: {rule_name}")
    
    async def evaluate_alerts(self, metrics: List[Metric]):
        """Evaluate alerts based on metrics"""
        for rule_name, rule in self.rules.items():
            if not rule.enabled:
                continue
            
            try:
                # Evaluate rule
                if await self._evaluate_rule(rule, metrics):
                    await self._create_alert(rule)
                else:
                    await self._resolve_alert(rule_name)
                    
            except Exception as e:
                logger.error(f"Error evaluating rule {rule_name}: {e}")
    
    async def _evaluate_rule(self, rule: AlertRule, metrics: List[Metric]) -> bool:
        """Evaluate alert rule"""
        # Simple rule evaluation (in production, use a proper query engine)
        if rule.query.startswith("system_cpu_percent >"):
            cpu_metrics = [m for m in metrics if m.name == "system_cpu_percent"]
            if cpu_metrics:
                latest_cpu = max(cpu_metrics, key=lambda m: m.timestamp)
                return latest_cpu.value > rule.threshold
        
        elif rule.query.startswith("system_memory_percent >"):
            memory_metrics = [m for m in metrics if m.name == "system_memory_percent"]
            if memory_metrics:
                latest_memory = max(memory_metrics, key=lambda m: m.timestamp)
                return latest_memory.value > rule.threshold
        
        elif rule.query.startswith("system_disk_percent >"):
            disk_metrics = [m for m in metrics if m.name == "system_disk_percent"]
            if disk_metrics:
                latest_disk = max(disk_metrics, key=lambda m: m.timestamp)
                return latest_disk.value > rule.threshold
        
        elif rule.query.startswith("application_error_rate >"):
            error_metrics = [m for m in metrics if m.name == "application_error_rate"]
            if error_metrics:
                latest_error = max(error_metrics, key=lambda m: m.timestamp)
                return latest_error.value > rule.threshold
        
        elif rule.query.startswith("application_response_time_avg >"):
            response_metrics = [m for m in metrics if m.name == "application_response_time_avg"]
            if response_metrics:
                latest_response = max(response_metrics, key=lambda m: m.timestamp)
                return latest_response.value > rule.threshold
        
        return False
    
    async def _create_alert(self, rule: AlertRule):
        """Create alert"""
        alert_id = f"{rule.name}-{int(datetime.utcnow().timestamp())}"
        
        # Check if alert already exists
        existing_alerts = [a for a in self.alerts.values() 
                          if a.rule_name == rule.name and a.status == AlertStatus.ACTIVE]
        
        if existing_alerts:
            return  # Alert already exists
        
        alert = Alert(
            alert_id=alert_id,
            rule_name=rule.name,
            status=AlertStatus.ACTIVE,
            severity=rule.severity,
            message=rule.description,
            created_at=datetime.utcnow(),
            labels=rule.labels,
            annotations=rule.annotations
        )
        
        self.alerts[alert_id] = alert
        
        # Send notifications
        await self._send_notifications(alert)
        
        logger.warning(f"Alert created: {alert_id} - {rule.description}")
    
    async def _resolve_alert(self, rule_name: str):
        """Resolve alert"""
        active_alerts = [a for a in self.alerts.values() 
                        if a.rule_name == rule_name and a.status == AlertStatus.ACTIVE]
        
        for alert in active_alerts:
            alert.status = AlertStatus.RESOLVED
            alert.resolved_at = datetime.utcnow()
            logger.info(f"Alert resolved: {alert.alert_id}")
    
    async def _send_notifications(self, alert: Alert):
        """Send alert notifications"""
        for handler in self.notification_handlers:
            try:
                await handler(alert)
            except Exception as e:
                logger.error(f"Notification handler error: {e}")
    
    def add_notification_handler(self, handler: Callable):
        """Add notification handler"""
        self.notification_handlers.append(handler)
        logger.info("Added notification handler")
    
    def acknowledge_alert(self, alert_id: str, acknowledged_by: str) -> bool:
        """Acknowledge alert"""
        if alert_id in self.alerts:
            alert = self.alerts[alert_id]
            alert.status = AlertStatus.ACKNOWLEDGED
            alert.acknowledged_at = datetime.utcnow()
            alert.acknowledged_by = acknowledged_by
            logger.info(f"Alert acknowledged: {alert_id} by {acknowledged_by}")
            return True
        return False
    
    def get_active_alerts(self) -> List[Alert]:
        """Get active alerts"""
        return [a for a in self.alerts.values() if a.status == AlertStatus.ACTIVE]
    
    def get_alerts_by_severity(self, severity: AlertSeverity) -> List[Alert]:
        """Get alerts by severity"""
        return [a for a in self.alerts.values() if a.severity == severity]

class SLAMonitor:
    """SLA monitoring system"""
    
    def __init__(self):
        self.sla_targets: Dict[str, SLATarget] = {}
        self.sla_metrics: Dict[str, List[float]] = {}
        
        # Initialize default SLA targets
        self._initialize_default_targets()
    
    def _initialize_default_targets(self):
        """Initialize default SLA targets"""
        default_targets = [
            SLATarget("availability", 99.9, 3600),  # 99.9% availability over 1 hour
            SLATarget("response_time", 500.0, 300),  # 500ms response time over 5 minutes
            SLATarget("error_rate", 0.01, 300),      # 1% error rate over 5 minutes
            SLATarget("throughput", 1000.0, 300)     # 1000 requests per second over 5 minutes
        ]
        
        for target in default_targets:
            self.sla_targets[target.name] = target
            self.sla_metrics[target.name] = []
    
    def add_sla_target(self, target: SLATarget):
        """Add SLA target"""
        self.sla_targets[target.name] = target
        self.sla_metrics[target.name] = []
        logger.info(f"Added SLA target: {target.name}")
    
    def update_sla_metric(self, target_name: str, value: float):
        """Update SLA metric"""
        if target_name in self.sla_metrics:
            self.sla_metrics[target_name].append(value)
            
            # Keep only metrics within measurement window
            target = self.sla_targets[target_name]
            cutoff_time = datetime.utcnow() - timedelta(seconds=target.measurement_window)
            # This is simplified - in production, you'd track timestamps
            
            # Calculate current SLA value
            if self.sla_metrics[target_name]:
                if target_name == "availability":
                    # Calculate availability percentage
                    target.current_value = (len([v for v in self.sla_metrics[target_name] if v > 0]) / 
                                          len(self.sla_metrics[target_name])) * 100
                else:
                    # Calculate average for other metrics
                    target.current_value = sum(self.sla_metrics[target_name]) / len(self.sla_metrics[target_name])
                
                target.last_updated = datetime.utcnow()
    
    def get_sla_status(self) -> Dict[str, Any]:
        """Get SLA status"""
        status = {
            "targets": {},
            "overall_status": "healthy"
        }
        
        for name, target in self.sla_targets.items():
            target_status = {
                "name": name,
                "target_value": target.target_value,
                "current_value": target.current_value,
                "last_updated": target.last_updated.isoformat() if target.last_updated else None,
                "status": "healthy"
            }
            
            # Determine if target is met
            if name == "availability":
                if target.current_value < target.target_value:
                    target_status["status"] = "breach"
                    status["overall_status"] = "unhealthy"
            else:
                if target.current_value > target.target_value:
                    target_status["status"] = "breach"
                    status["overall_status"] = "unhealthy"
            
            status["targets"][name] = target_status
        
        return status

class GrafanaDashboardGenerator:
    """Grafana dashboard generator"""
    
    def __init__(self):
        self.dashboard_config = {}
    
    def generate_dashboard(self, metrics_collector: MetricsCollector, 
                          alert_manager: AlertManager) -> Dict[str, Any]:
        """Generate Grafana dashboard configuration"""
        dashboard = {
            "dashboard": {
                "id": None,
                "title": "ASK Pipeline Monitoring",
                "tags": ["ask-pipeline", "monitoring"],
                "timezone": "browser",
                "panels": [],
                "time": {
                    "from": "now-1h",
                    "to": "now"
                },
                "refresh": "30s"
            }
        }
        
        # Add system metrics panels
        dashboard["dashboard"]["panels"].extend([
            self._create_cpu_panel(),
            self._create_memory_panel(),
            self._create_disk_panel(),
            self._create_network_panel(),
            self._create_application_panel(),
            self._create_alerts_panel()
        ])
        
        return dashboard
    
    def _create_cpu_panel(self) -> Dict[str, Any]:
        """Create CPU metrics panel"""
        return {
            "id": 1,
            "title": "CPU Usage",
            "type": "graph",
            "targets": [
                {
                    "expr": "system_cpu_percent",
                    "legendFormat": "CPU %"
                }
            ],
            "yAxes": [
                {
                    "label": "Percentage",
                    "min": 0,
                    "max": 100
                }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
        }
    
    def _create_memory_panel(self) -> Dict[str, Any]:
        """Create memory metrics panel"""
        return {
            "id": 2,
            "title": "Memory Usage",
            "type": "graph",
            "targets": [
                {
                    "expr": "system_memory_percent",
                    "legendFormat": "Memory %"
                }
            ],
            "yAxes": [
                {
                    "label": "Percentage",
                    "min": 0,
                    "max": 100
                }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
        }
    
    def _create_disk_panel(self) -> Dict[str, Any]:
        """Create disk metrics panel"""
        return {
            "id": 3,
            "title": "Disk Usage",
            "type": "graph",
            "targets": [
                {
                    "expr": "system_disk_percent",
                    "legendFormat": "Disk %"
                }
            ],
            "yAxes": [
                {
                    "label": "Percentage",
                    "min": 0,
                    "max": 100
                }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
        }
    
    def _create_network_panel(self) -> Dict[str, Any]:
        """Create network metrics panel"""
        return {
            "id": 4,
            "title": "Network I/O",
            "type": "graph",
            "targets": [
                {
                    "expr": "system_network_bytes_sent",
                    "legendFormat": "Bytes Sent"
                },
                {
                    "expr": "system_network_bytes_recv",
                    "legendFormat": "Bytes Received"
                }
            ],
            "yAxes": [
                {
                    "label": "Bytes"
                }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
        }
    
    def _create_application_panel(self) -> Dict[str, Any]:
        """Create application metrics panel"""
        return {
            "id": 5,
            "title": "Application Metrics",
            "type": "graph",
            "targets": [
                {
                    "expr": "application_requests_per_second",
                    "legendFormat": "Requests/sec"
                },
                {
                    "expr": "application_response_time_avg",
                    "legendFormat": "Response Time (ms)"
                },
                {
                    "expr": "application_error_rate",
                    "legendFormat": "Error Rate"
                }
            ],
            "yAxes": [
                {
                    "label": "Value"
                }
            ],
            "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}
        }
    
    def _create_alerts_panel(self) -> Dict[str, Any]:
        """Create alerts panel"""
        return {
            "id": 6,
            "title": "Active Alerts",
            "type": "table",
            "targets": [
                {
                    "expr": "alertmanager_alerts",
                    "format": "table"
                }
            ],
            "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}
        }

# Global instances
_metrics_collector = MetricsCollector()
_alert_manager = AlertManager()
_sla_monitor = SLAMonitor()
_grafana_generator = GrafanaDashboardGenerator()

def get_metrics_collector() -> MetricsCollector:
    """Get global metrics collector"""
    return _metrics_collector

def get_alert_manager() -> AlertManager:
    """Get global alert manager"""
    return _alert_manager

def get_sla_monitor() -> SLAMonitor:
    """Get global SLA monitor"""
    return _sla_monitor

def get_grafana_generator() -> GrafanaDashboardGenerator:
    """Get global Grafana generator"""
    return _grafana_generator

async def cleanup_monitoring():
    """Cleanup monitoring components"""
    await _metrics_collector.stop_collection()
