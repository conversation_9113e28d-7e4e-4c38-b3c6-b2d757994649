"""
CI/CD Pipeline for ASK Pipeline

Provides comprehensive CI/CD capabilities:
- Implement automated testing pipeline with GitHub Actions
- Add security scanning and vulnerability assessment
- Create automated deployment with staging validation
- Implement blue/green deployment strategy
- Add deployment monitoring and rollback automation
"""

import asyncio
import json
import logging
import subprocess
import tempfile
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
import yaml
import os

logger = logging.getLogger(__name__)

class PipelineStage(Enum):
    """Pipeline stage enumeration"""
    BUILD = "build"
    TEST = "test"
    SECURITY_SCAN = "security_scan"
    DEPLOY_STAGING = "deploy_staging"
    VALIDATE_STAGING = "validate_staging"
    DEPLOY_PRODUCTION = "deploy_production"
    VALIDATE_PRODUCTION = "validate_production"
    CLEANUP = "cleanup"

class DeploymentStrategy(Enum):
    """Deployment strategy enumeration"""
    BLUE_GREEN = "blue_green"
    ROLLING = "rolling"
    CANARY = "canary"
    RECREATE = "recreate"

class PipelineStatus(Enum):
    """Pipeline status enumeration"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class PipelineStep:
    """Pipeline step configuration"""
    name: str
    stage: PipelineStage
    command: str
    timeout: int = 300
    retries: int = 3
    parallel: bool = False
    dependencies: List[str] = field(default_factory=list)
    environment: Dict[str, str] = field(default_factory=dict)
    artifacts: List[str] = field(default_factory=list)

@dataclass
class PipelineExecution:
    """Pipeline execution information"""
    execution_id: str
    pipeline_name: str
    status: PipelineStatus
    started_at: datetime
    completed_at: Optional[datetime] = None
    steps: List[Dict[str, Any]] = field(default_factory=list)
    artifacts: Dict[str, str] = field(default_factory=dict)
    logs: List[str] = field(default_factory=list)

class CICDPipeline:
    """CI/CD pipeline management system"""
    
    def __init__(self, pipeline_name: str = "ask-pipeline"):
        self.pipeline_name = pipeline_name
        self.steps: List[PipelineStep] = []
        self.executions: Dict[str, PipelineExecution] = {}
        self.current_execution: Optional[PipelineExecution] = None
        
        # Initialize default pipeline steps
        self._initialize_default_steps()
    
    def _initialize_default_steps(self):
        """Initialize default pipeline steps"""
        default_steps = [
            PipelineStep(
                name="build",
                stage=PipelineStage.BUILD,
                command="docker build -t ask-pipeline:latest .",
                timeout=600,
                artifacts=["ask-pipeline:latest"]
            ),
            PipelineStep(
                name="unit_tests",
                stage=PipelineStage.TEST,
                command="python -m pytest tests/unit/ -v --cov=src --cov-report=xml",
                timeout=300,
                dependencies=["build"],
                artifacts=["coverage.xml"]
            ),
            PipelineStep(
                name="integration_tests",
                stage=PipelineStage.TEST,
                command="python -m pytest tests/integration/ -v",
                timeout=600,
                dependencies=["build"],
                parallel=True
            ),
            PipelineStep(
                name="security_scan",
                stage=PipelineStage.SECURITY_SCAN,
                command="trivy image ask-pipeline:latest --format json --output security-report.json",
                timeout=300,
                dependencies=["build"],
                artifacts=["security-report.json"]
            ),
            PipelineStep(
                name="deploy_staging",
                stage=PipelineStage.DEPLOY_STAGING,
                command="kubectl apply -f k8s/staging/",
                timeout=300,
                dependencies=["build", "unit_tests", "integration_tests"]
            ),
            PipelineStep(
                name="validate_staging",
                stage=PipelineStage.VALIDATE_STAGING,
                command="python scripts/validate_deployment.py --environment staging",
                timeout=300,
                dependencies=["deploy_staging"]
            ),
            PipelineStep(
                name="deploy_production",
                stage=PipelineStage.DEPLOY_PRODUCTION,
                command="kubectl apply -f k8s/production/",
                timeout=300,
                dependencies=["validate_staging"]
            ),
            PipelineStep(
                name="validate_production",
                stage=PipelineStage.VALIDATE_PRODUCTION,
                command="python scripts/validate_deployment.py --environment production",
                timeout=300,
                dependencies=["deploy_production"]
            )
        ]
        
        self.steps = default_steps
    
    def add_step(self, step: PipelineStep):
        """Add pipeline step"""
        self.steps.append(step)
        logger.info(f"Added pipeline step: {step.name}")
    
    def remove_step(self, step_name: str):
        """Remove pipeline step"""
        self.steps = [step for step in self.steps if step.name != step_name]
        logger.info(f"Removed pipeline step: {step_name}")
    
    async def execute_pipeline(self, execution_id: Optional[str] = None) -> PipelineExecution:
        """Execute the complete pipeline"""
        if execution_id is None:
            execution_id = f"{self.pipeline_name}-{int(datetime.utcnow().timestamp())}"
        
        execution = PipelineExecution(
            execution_id=execution_id,
            pipeline_name=self.pipeline_name,
            status=PipelineStatus.RUNNING,
            started_at=datetime.utcnow()
        )
        
        self.executions[execution_id] = execution
        self.current_execution = execution
        
        try:
            logger.info(f"Starting pipeline execution: {execution_id}")
            
            # Execute steps in dependency order
            await self._execute_steps(execution)
            
            execution.status = PipelineStatus.SUCCESS
            execution.completed_at = datetime.utcnow()
            logger.info(f"Pipeline execution completed successfully: {execution_id}")
            
        except Exception as e:
            execution.status = PipelineStatus.FAILED
            execution.completed_at = datetime.utcnow()
            execution.logs.append(f"Pipeline failed: {e}")
            logger.error(f"Pipeline execution failed: {execution_id} - {e}")
        
        return execution
    
    async def _execute_steps(self, execution: PipelineExecution):
        """Execute pipeline steps"""
        # Group steps by stage
        stages = {}
        for step in self.steps:
            if step.stage not in stages:
                stages[step.stage] = []
            stages[step.stage].append(step)
        
        # Execute stages in order
        for stage in PipelineStage:
            if stage in stages:
                stage_steps = stages[stage]
                
                # Execute parallel steps
                parallel_steps = [step for step in stage_steps if step.parallel]
                sequential_steps = [step for step in stage_steps if not step.parallel]
                
                # Execute parallel steps
                if parallel_steps:
                    await self._execute_parallel_steps(parallel_steps, execution)
                
                # Execute sequential steps
                for step in sequential_steps:
                    await self._execute_step(step, execution)
    
    async def _execute_parallel_steps(self, steps: List[PipelineStep], execution: PipelineExecution):
        """Execute steps in parallel"""
        tasks = []
        for step in steps:
            task = asyncio.create_task(self._execute_step(step, execution))
            tasks.append(task)
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _execute_step(self, step: PipelineStep, execution: PipelineExecution):
        """Execute a single pipeline step"""
        step_info = {
            "name": step.name,
            "stage": step.stage.value,
            "command": step.command,
            "started_at": datetime.utcnow().isoformat(),
            "status": "running"
        }
        
        execution.steps.append(step_info)
        
        try:
            logger.info(f"Executing step: {step.name}")
            
            # Execute command
            result = await self._run_command(step.command, step.timeout, step.environment)
            
            step_info.update({
                "status": "success",
                "completed_at": datetime.utcnow().isoformat(),
                "output": result["output"],
                "return_code": result["return_code"]
            })
            
            # Store artifacts
            for artifact in step.artifacts:
                execution.artifacts[artifact] = f"artifacts/{execution.execution_id}/{artifact}"
            
            logger.info(f"Step completed successfully: {step.name}")
            
        except Exception as e:
            step_info.update({
                "status": "failed",
                "completed_at": datetime.utcnow().isoformat(),
                "error": str(e)
            })
            
            execution.logs.append(f"Step {step.name} failed: {e}")
            logger.error(f"Step failed: {step.name} - {e}")
            
            # Retry if configured
            if step.retries > 0:
                logger.info(f"Retrying step: {step.name}")
                step.retries -= 1
                await asyncio.sleep(5)  # Wait before retry
                await self._execute_step(step, execution)
            else:
                raise e
    
    async def _run_command(self, command: str, timeout: int, environment: Dict[str, str]) -> Dict[str, Any]:
        """Run command with timeout"""
        try:
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                env={**os.environ, **environment}
            )
            
            stdout, stderr = await asyncio.wait_for(
                process.communicate(),
                timeout=timeout
            )
            
            return {
                "output": stdout.decode(),
                "error": stderr.decode(),
                "return_code": process.returncode
            }
            
        except asyncio.TimeoutError:
            process.kill()
            raise Exception(f"Command timed out after {timeout} seconds")
        except Exception as e:
            raise Exception(f"Command execution failed: {e}")
    
    def get_execution(self, execution_id: str) -> Optional[PipelineExecution]:
        """Get pipeline execution by ID"""
        return self.executions.get(execution_id)
    
    def get_execution_status(self, execution_id: str) -> Optional[PipelineStatus]:
        """Get execution status"""
        execution = self.get_execution(execution_id)
        return execution.status if execution else None
    
    def cancel_execution(self, execution_id: str) -> bool:
        """Cancel pipeline execution"""
        execution = self.get_execution(execution_id)
        if execution and execution.status == PipelineStatus.RUNNING:
            execution.status = PipelineStatus.CANCELLED
            execution.completed_at = datetime.utcnow()
            logger.info(f"Pipeline execution cancelled: {execution_id}")
            return True
        return False
    
    def get_pipeline_summary(self) -> Dict[str, Any]:
        """Get pipeline summary"""
        total_executions = len(self.executions)
        successful_executions = len([e for e in self.executions.values() if e.status == PipelineStatus.SUCCESS])
        failed_executions = len([e for e in self.executions.values() if e.status == PipelineStatus.FAILED])
        
        return {
            "pipeline_name": self.pipeline_name,
            "total_steps": len(self.steps),
            "total_executions": total_executions,
            "successful_executions": successful_executions,
            "failed_executions": failed_executions,
            "success_rate": successful_executions / total_executions if total_executions > 0 else 0,
            "current_execution": self.current_execution.execution_id if self.current_execution else None
        }

class GitHubActionsGenerator:
    """GitHub Actions workflow generator"""
    
    def __init__(self, pipeline: CICDPipeline):
        self.pipeline = pipeline
    
    def generate_workflow(self, workflow_name: str = "ci-cd") -> str:
        """Generate GitHub Actions workflow"""
        workflow = {
            "name": workflow_name,
            "on": {
                "push": {
                    "branches": ["main", "develop"]
                },
                "pull_request": {
                    "branches": ["main"]
                }
            },
            "env": {
                "PYTHON_VERSION": "3.11",
                "NODE_VERSION": "18"
            },
            "jobs": {}
        }
        
        # Generate jobs for each stage
        for stage in PipelineStage:
            stage_steps = [step for step in self.pipeline.steps if step.stage == stage]
            if stage_steps:
                workflow["jobs"][stage.value] = self._generate_job(stage, stage_steps)
        
        return yaml.dump(workflow, default_flow_style=False, indent=2)
    
    def _generate_job(self, stage: PipelineStage, steps: List[PipelineStep]) -> Dict[str, Any]:
        """Generate job for a stage"""
        job = {
            "runs-on": "ubuntu-latest",
            "steps": [
                {
                    "name": "Checkout code",
                    "uses": "actions/checkout@v4"
                },
                {
                    "name": "Set up Python",
                    "uses": "actions/setup-python@v4",
                    "with": {
                        "python-version": "${{ env.PYTHON_VERSION }}"
                    }
                },
                {
                    "name": "Install dependencies",
                    "run": "pip install -r requirements.txt"
                }
            ]
        }
        
        # Add stage-specific steps
        for step in steps:
            step_config = {
                "name": step.name,
                "run": step.command
            }
            
            if step.timeout:
                step_config["timeout-minutes"] = step.timeout // 60
            
            if step.environment:
                step_config["env"] = step.environment
            
            job["steps"].append(step_config)
        
        # Add stage-specific configuration
        if stage == PipelineStage.BUILD:
            job["steps"].extend([
                {
                    "name": "Set up Docker Buildx",
                    "uses": "docker/setup-buildx-action@v3"
                },
                {
                    "name": "Login to Container Registry",
                    "uses": "docker/login-action@v3",
                    "with": {
                        "registry": "ghcr.io",
                        "username": "${{ github.actor }}",
                        "password": "${{ secrets.GITHUB_TOKEN }}"
                    }
                }
            ])
        elif stage == PipelineStage.SECURITY_SCAN:
            job["steps"].extend([
                {
                    "name": "Run Trivy vulnerability scanner",
                    "uses": "aquasecurity/trivy-action@master",
                    "with": {
                        "image-ref": "ask-pipeline:latest",
                        "format": "sarif",
                        "output": "trivy-results.sarif"
                    }
                },
                {
                    "name": "Upload Trivy scan results",
                    "uses": "github/codeql-action/upload-sarif@v2",
                    "with": {
                        "sarif_file": "trivy-results.sarif"
                    }
                }
            ])
        elif stage in [PipelineStage.DEPLOY_STAGING, PipelineStage.DEPLOY_PRODUCTION]:
            job["steps"].extend([
                {
                    "name": "Set up kubectl",
                    "uses": "azure/setup-kubectl@v3",
                    "with": {
                        "version": "latest"
                    }
                },
                {
                    "name": "Configure kubectl",
                    "run": "kubectl config set-cluster k8s --server=${{ secrets.KUBE_SERVER }}"
                }
            ])
        
        return job

class BlueGreenDeployment:
    """Blue/Green deployment strategy"""
    
    def __init__(self, service_name: str, namespace: str = "default"):
        self.service_name = service_name
        self.namespace = namespace
        self.blue_version = "blue"
        self.green_version = "green"
        self.current_version = self.blue_version
    
    async def deploy(self, new_version: str) -> bool:
        """Deploy new version using blue/green strategy"""
        try:
            # Determine target version
            target_version = self.green_version if self.current_version == self.blue_version else self.blue_version
            
            logger.info(f"Deploying {new_version} to {target_version} environment")
            
            # Deploy to target environment
            await self._deploy_to_environment(target_version, new_version)
            
            # Validate deployment
            if await self._validate_deployment(target_version):
                # Switch traffic
                await self._switch_traffic(target_version)
                self.current_version = target_version
                
                # Cleanup old version
                await self._cleanup_old_version(self.blue_version if target_version == self.green_version else self.green_version)
                
                logger.info(f"Blue/Green deployment completed. Current version: {self.current_version}")
                return True
            else:
                logger.error(f"Deployment validation failed for {target_version}")
                return False
                
        except Exception as e:
            logger.error(f"Blue/Green deployment failed: {e}")
            return False
    
    async def _deploy_to_environment(self, environment: str, version: str):
        """Deploy to specific environment"""
        # Update deployment configuration
        deployment_config = {
            "apiVersion": "apps/v1",
            "kind": "Deployment",
            "metadata": {
                "name": f"{self.service_name}-{environment}",
                "namespace": self.namespace
            },
            "spec": {
                "replicas": 3,
                "selector": {
                    "matchLabels": {
                        "app": self.service_name,
                        "version": environment
                    }
                },
                "template": {
                    "metadata": {
                        "labels": {
                            "app": self.service_name,
                            "version": environment
                        }
                    },
                    "spec": {
                        "containers": [{
                            "name": self.service_name,
                            "image": f"ask-pipeline:{version}",
                            "ports": [{"containerPort": 8000}]
                        }]
                    }
                }
            }
        }
        
        # Apply deployment
        await self._apply_kubectl_config(deployment_config)
    
    async def _validate_deployment(self, environment: str) -> bool:
        """Validate deployment"""
        try:
            # Check if pods are ready
            result = await self._run_kubectl_command(
                f"get pods -l app={self.service_name},version={environment} -o json"
            )
            
            pods = json.loads(result["output"])
            if not pods.get("items"):
                return False
            
            # Check if all pods are ready
            for pod in pods["items"]:
                if pod["status"]["phase"] != "Running":
                    return False
            
            # Run health checks
            health_check_result = await self._run_kubectl_command(
                f"exec -l app={self.service_name},version={environment} -- curl -f http://localhost:8000/health"
            )
            
            return health_check_result["return_code"] == 0
            
        except Exception as e:
            logger.error(f"Deployment validation failed: {e}")
            return False
    
    async def _switch_traffic(self, target_version: str):
        """Switch traffic to target version"""
        service_config = {
            "apiVersion": "v1",
            "kind": "Service",
            "metadata": {
                "name": self.service_name,
                "namespace": self.namespace
            },
            "spec": {
                "selector": {
                    "app": self.service_name,
                    "version": target_version
                },
                "ports": [{"port": 80, "targetPort": 8000}]
            }
        }
        
        await self._apply_kubectl_config(service_config)
    
    async def _cleanup_old_version(self, old_version: str):
        """Cleanup old version"""
        await self._run_kubectl_command(f"delete deployment {self.service_name}-{old_version}")
    
    async def _apply_kubectl_config(self, config: Dict[str, Any]):
        """Apply kubectl configuration"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config, f)
            config_file = f.name
        
        try:
            await self._run_kubectl_command(f"apply -f {config_file}")
        finally:
            os.unlink(config_file)
    
    async def _run_kubectl_command(self, command: str) -> Dict[str, Any]:
        """Run kubectl command"""
        full_command = f"kubectl {command}"
        return await self._run_command(full_command)
    
    async def _run_command(self, command: str) -> Dict[str, Any]:
        """Run command"""
        process = await asyncio.create_subprocess_shell(
            command,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        return {
            "output": stdout.decode(),
            "error": stderr.decode(),
            "return_code": process.returncode
        }

# Global instances
_cicd_pipeline: Optional[CICDPipeline] = None

def get_cicd_pipeline() -> CICDPipeline:
    """Get global CI/CD pipeline"""
    global _cicd_pipeline
    if _cicd_pipeline is None:
        _cicd_pipeline = CICDPipeline()
    return _cicd_pipeline

def cleanup_cicd_pipeline():
    """Cleanup global CI/CD pipeline"""
    global _cicd_pipeline
    if _cicd_pipeline:
        _cicd_pipeline = None
