"""
Modernization and Best Practices for ASK Pipeline

This module provides comprehensive modernization capabilities:

1. Dependency Management - Automated dependency scanning and updates
2. Modern Python Features - Async context managers, pattern matching, and modern typing
3. Containerization - Docker, Docker Compose, and Kubernetes configurations

Features:
- Automated dependency scanning and vulnerability detection
- Modern Python features and patterns
- Containerization with multi-stage builds
- Security scanning and hardening
- Container orchestration and monitoring
- Supply chain security scanning
"""

from .dependency_manager import (
    DependencyManager,
    DependencyStatus,
    VulnerabilitySeverity,
    DependencyInfo,
    VulnerabilityInfo,
    DependencyUpdate,
    get_dependency_manager,
    cleanup_dependency_manager
)

from .modern_python import (
    AsyncResourceManager,
    ModernDataValidator,
    AsyncStreamProcessor,
    ModernTypeSystem,
    ModernAsyncPatterns,
    ModernErrorHandling,
    ModernDataStructures,
    ResourceType,
    ResourceInfo,
    get_resource_manager,
    get_data_validator,
    get_stream_processor,
    get_type_system,
    get_async_patterns,
    get_error_handling,
    get_data_structures
)

from .containerization import (
    ContainerBuilder,
    DockerComposeGenerator,
    KubernetesGenerator,
    ContainerMonitor,
    ContainerConfig,
    SecurityLevel,
    SecurityScanResult,
    ContainerPlatform,
    get_container_builder,
    get_compose_generator,
    get_k8s_generator,
    get_container_monitor
)

__all__ = [
    # Dependency Management
    'DependencyManager',
    'DependencyStatus',
    'VulnerabilitySeverity',
    'DependencyInfo',
    'VulnerabilityInfo',
    'DependencyUpdate',
    'get_dependency_manager',
    'cleanup_dependency_manager',
    
    # Modern Python Features
    'AsyncResourceManager',
    'ModernDataValidator',
    'AsyncStreamProcessor',
    'ModernTypeSystem',
    'ModernAsyncPatterns',
    'ModernErrorHandling',
    'ModernDataStructures',
    'ResourceType',
    'ResourceInfo',
    'get_resource_manager',
    'get_data_validator',
    'get_stream_processor',
    'get_type_system',
    'get_async_patterns',
    'get_error_handling',
    'get_data_structures',
    
    # Containerization
    'ContainerBuilder',
    'DockerComposeGenerator',
    'KubernetesGenerator',
    'ContainerMonitor',
    'ContainerConfig',
    'SecurityLevel',
    'SecurityScanResult',
    'ContainerPlatform',
    'get_container_builder',
    'get_compose_generator',
    'get_k8s_generator',
    'get_container_monitor'
]
