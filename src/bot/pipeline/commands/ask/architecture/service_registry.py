"""
Service Registry for ASK Pipeline

Provides microservices architecture foundation:
- Create service registry for service discovery and management
- Implement service health monitoring and failover
- Add load balancing and service routing
- Create service mesh communication patterns
- Implement distributed tracing and monitoring
"""

import asyncio
import logging
import json
from typing import Dict, Any, Optional, List, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import uuid
import time

logger = logging.getLogger(__name__)

class ServiceStatus(Enum):
    """Service status enumeration"""
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    STARTING = "starting"
    STOPPING = "stopping"
    UNKNOWN = "unknown"

class ServiceType(Enum):
    """Service type enumeration"""
    API_GATEWAY = "api_gateway"
    AUTHENTICATION = "authentication"
    QUERY_PROCESSOR = "query_processor"
    AI_SERVICE = "ai_service"
    CACHE_SERVICE = "cache_service"
    DATABASE_SERVICE = "database_service"
    NOTIFICATION_SERVICE = "notification_service"
    ANALYTICS_SERVICE = "analytics_service"

@dataclass
class ServiceInstance:
    """Service instance information"""
    instance_id: str
    service_name: str
    service_type: ServiceType
    host: str
    port: int
    status: ServiceStatus
    version: str
    health_check_url: str
    last_heartbeat: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)
    tags: List[str] = field(default_factory=list)
    load_balancer_weight: float = 1.0

@dataclass
class ServiceRoute:
    """Service routing information"""
    route_id: str
    service_name: str
    path_pattern: str
    methods: List[str]
    priority: int
    enabled: bool = True
    rate_limit: Optional[int] = None
    timeout: Optional[int] = None
    retry_count: int = 3
    circuit_breaker: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class LoadBalancerConfig:
    """Load balancer configuration"""
    service_name: str
    algorithm: str  # round_robin, weighted_round_robin, least_connections, ip_hash
    health_check_interval: int = 30
    health_check_timeout: int = 5
    max_retries: int = 3
    retry_delay: int = 1
    enabled: bool = True

class ServiceRegistry:
    """Service registry and discovery system"""
    
    def __init__(self):
        self.services: Dict[str, List[ServiceInstance]] = {}
        self.routes: Dict[str, ServiceRoute] = {}
        self.load_balancers: Dict[str, LoadBalancerConfig] = {}
        self.health_checkers: Dict[str, asyncio.Task] = {}
        
        # Service discovery state
        self.discovery_cache: Dict[str, List[ServiceInstance]] = {}
        self.cache_ttl = 60  # seconds
        self.last_cache_update: Dict[str, datetime] = {}
        
        # Start health monitoring
        self.monitoring_task: Optional[asyncio.Task] = None
        asyncio.create_task(self.start_health_monitoring())
    
    async def start_health_monitoring(self):
        """Start health monitoring for all services"""
        if self.monitoring_task:
            return
        
        self.monitoring_task = asyncio.create_task(self._health_monitoring_loop())
        logger.info("Service health monitoring started")
    
    async def stop_health_monitoring(self):
        """Stop health monitoring"""
        if self.monitoring_task:
            self.monitoring_task.cancel()
            self.monitoring_task = None
        logger.info("Service health monitoring stopped")
    
    async def _health_monitoring_loop(self):
        """Health monitoring loop"""
        while True:
            try:
                await self._check_all_services()
                await asyncio.sleep(30)  # Check every 30 seconds
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Health monitoring error: {e}")
                await asyncio.sleep(60)
    
    async def _check_all_services(self):
        """Check health of all services"""
        for service_name, instances in self.services.items():
            for instance in instances:
                try:
                    await self._check_service_health(instance)
                except Exception as e:
                    logger.error(f"Health check failed for {instance.instance_id}: {e}")
                    instance.status = ServiceStatus.UNHEALTHY
    
    async def _check_service_health(self, instance: ServiceInstance):
        """Check health of a single service instance"""
        try:
            # In a real implementation, this would make an HTTP request
            # to the health check URL
            import aiohttp
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    instance.health_check_url,
                    timeout=aiohttp.ClientTimeout(total=5)
                ) as response:
                    if response.status == 200:
                        instance.status = ServiceStatus.HEALTHY
                        instance.last_heartbeat = datetime.utcnow()
                    else:
                        instance.status = ServiceStatus.UNHEALTHY
        except Exception as e:
            instance.status = ServiceStatus.UNHEALTHY
            logger.warning(f"Health check failed for {instance.instance_id}: {e}")
    
    def register_service(self, service_name: str, service_type: ServiceType,
                        host: str, port: int, version: str = "1.0.0",
                        health_check_url: str = None, metadata: Dict[str, Any] = None,
                        tags: List[str] = None) -> str:
        """Register a new service instance"""
        instance_id = str(uuid.uuid4())
        
        if health_check_url is None:
            health_check_url = f"http://{host}:{port}/health"
        
        instance = ServiceInstance(
            instance_id=instance_id,
            service_name=service_name,
            service_type=service_type,
            host=host,
            port=port,
            status=ServiceStatus.STARTING,
            version=version,
            health_check_url=health_check_url,
            last_heartbeat=datetime.utcnow(),
            metadata=metadata or {},
            tags=tags or []
        )
        
        if service_name not in self.services:
            self.services[service_name] = []
        
        self.services[service_name].append(instance)
        
        # Clear discovery cache
        if service_name in self.discovery_cache:
            del self.discovery_cache[service_name]
        
        logger.info(f"Registered service: {service_name} - {instance_id}")
        return instance_id
    
    def unregister_service(self, instance_id: str) -> bool:
        """Unregister a service instance"""
        for service_name, instances in self.services.items():
            for i, instance in enumerate(instances):
                if instance.instance_id == instance_id:
                    del instances[i]
                    
                    # Clear discovery cache
                    if service_name in self.discovery_cache:
                        del self.discovery_cache[service_name]
                    
                    logger.info(f"Unregistered service: {service_name} - {instance_id}")
                    return True
        
        return False
    
    def get_service_instances(self, service_name: str, 
                            healthy_only: bool = True) -> List[ServiceInstance]:
        """Get service instances for a service name"""
        # Check cache first
        if service_name in self.discovery_cache:
            cache_time = self.last_cache_update.get(service_name, datetime.min)
            if datetime.utcnow() - cache_time < timedelta(seconds=self.cache_ttl):
                instances = self.discovery_cache[service_name]
                if healthy_only:
                    return [i for i in instances if i.status == ServiceStatus.HEALTHY]
                return instances
        
        # Get from registry
        instances = self.services.get(service_name, [])
        
        if healthy_only:
            instances = [i for i in instances if i.status == ServiceStatus.HEALTHY]
        
        # Update cache
        self.discovery_cache[service_name] = instances
        self.last_cache_update[service_name] = datetime.utcnow()
        
        return instances
    
    def get_service_instance(self, service_name: str, 
                           load_balance: bool = True) -> Optional[ServiceInstance]:
        """Get a single service instance with load balancing"""
        instances = self.get_service_instances(service_name, healthy_only=True)
        
        if not instances:
            return None
        
        if not load_balance:
            return instances[0]
        
        # Get load balancer config
        lb_config = self.load_balancers.get(service_name)
        if not lb_config or not lb_config.enabled:
            return instances[0]
        
        # Apply load balancing algorithm
        if lb_config.algorithm == "round_robin":
            return self._round_robin_selection(instances)
        elif lb_config.algorithm == "weighted_round_robin":
            return self._weighted_round_robin_selection(instances)
        elif lb_config.algorithm == "least_connections":
            return self._least_connections_selection(instances)
        elif lb_config.algorithm == "ip_hash":
            return self._ip_hash_selection(instances)
        else:
            return instances[0]
    
    def _round_robin_selection(self, instances: List[ServiceInstance]) -> ServiceInstance:
        """Round robin selection algorithm"""
        # Simple round robin - in practice, you'd use a more sophisticated approach
        import random
        return random.choice(instances)
    
    def _weighted_round_robin_selection(self, instances: List[ServiceInstance]) -> ServiceInstance:
        """Weighted round robin selection algorithm"""
        total_weight = sum(instance.load_balancer_weight for instance in instances)
        if total_weight == 0:
            return instances[0]
        
        import random
        random_weight = random.uniform(0, total_weight)
        current_weight = 0
        
        for instance in instances:
            current_weight += instance.load_balancer_weight
            if random_weight <= current_weight:
                return instance
        
        return instances[-1]
    
    def _least_connections_selection(self, instances: List[ServiceInstance]) -> ServiceInstance:
        """Least connections selection algorithm"""
        # In practice, you'd track active connections per instance
        # For now, return the instance with the lowest load balancer weight
        return min(instances, key=lambda x: x.load_balancer_weight)
    
    def _ip_hash_selection(self, instances: List[ServiceInstance]) -> ServiceInstance:
        """IP hash selection algorithm"""
        # In practice, you'd hash the client IP
        # For now, return a consistent selection
        return instances[0]
    
    def add_service_route(self, service_name: str, path_pattern: str,
                         methods: List[str], priority: int = 0,
                         rate_limit: Optional[int] = None,
                         timeout: Optional[int] = None,
                         retry_count: int = 3,
                         circuit_breaker: bool = True) -> str:
        """Add a service route"""
        route_id = str(uuid.uuid4())
        
        route = ServiceRoute(
            route_id=route_id,
            service_name=service_name,
            path_pattern=path_pattern,
            methods=methods,
            priority=priority,
            rate_limit=rate_limit,
            timeout=timeout,
            retry_count=retry_count,
            circuit_breaker=circuit_breaker
        )
        
        self.routes[route_id] = route
        logger.info(f"Added service route: {route_id} - {path_pattern}")
        return route_id
    
    def remove_service_route(self, route_id: str) -> bool:
        """Remove a service route"""
        if route_id in self.routes:
            del self.routes[route_id]
            logger.info(f"Removed service route: {route_id}")
            return True
        return False
    
    def get_route_for_path(self, path: str, method: str) -> Optional[ServiceRoute]:
        """Get route for a given path and method"""
        matching_routes = []
        
        for route in self.routes.values():
            if not route.enabled:
                continue
            
            if method not in route.methods:
                continue
            
            # Simple pattern matching - in practice, you'd use more sophisticated routing
            if path.startswith(route.path_pattern):
                matching_routes.append(route)
        
        if not matching_routes:
            return None
        
        # Return route with highest priority
        return max(matching_routes, key=lambda x: x.priority)
    
    def configure_load_balancer(self, service_name: str, algorithm: str = "round_robin",
                               health_check_interval: int = 30,
                               health_check_timeout: int = 5,
                               max_retries: int = 3,
                               retry_delay: int = 1) -> bool:
        """Configure load balancer for a service"""
        lb_config = LoadBalancerConfig(
            service_name=service_name,
            algorithm=algorithm,
            health_check_interval=health_check_interval,
            health_check_timeout=health_check_timeout,
            max_retries=max_retries,
            retry_delay=retry_delay
        )
        
        self.load_balancers[service_name] = lb_config
        logger.info(f"Configured load balancer for {service_name}: {algorithm}")
        return True
    
    def get_service_health_summary(self) -> Dict[str, Any]:
        """Get service health summary"""
        summary = {
            "total_services": len(self.services),
            "total_instances": sum(len(instances) for instances in self.services.values()),
            "healthy_instances": 0,
            "unhealthy_instances": 0,
            "services": {}
        }
        
        for service_name, instances in self.services.items():
            healthy_count = len([i for i in instances if i.status == ServiceStatus.HEALTHY])
            unhealthy_count = len([i for i in instances if i.status == ServiceStatus.UNHEALTHY])
            
            summary["healthy_instances"] += healthy_count
            summary["unhealthy_instances"] += unhealthy_count
            
            summary["services"][service_name] = {
                "total_instances": len(instances),
                "healthy_instances": healthy_count,
                "unhealthy_instances": unhealthy_count,
                "instances": [
                    {
                        "instance_id": instance.instance_id,
                        "host": instance.host,
                        "port": instance.port,
                        "status": instance.status.value,
                        "version": instance.version,
                        "last_heartbeat": instance.last_heartbeat.isoformat()
                    }
                    for instance in instances
                ]
            }
        
        return summary
    
    def get_routing_summary(self) -> Dict[str, Any]:
        """Get routing summary"""
        return {
            "total_routes": len(self.routes),
            "routes": [
                {
                    "route_id": route.route_id,
                    "service_name": route.service_name,
                    "path_pattern": route.path_pattern,
                    "methods": route.methods,
                    "priority": route.priority,
                    "enabled": route.enabled,
                    "rate_limit": route.rate_limit,
                    "timeout": route.timeout,
                    "retry_count": route.retry_count,
                    "circuit_breaker": route.circuit_breaker
                }
                for route in self.routes.values()
            ]
        }
    
    def export_service_registry(self, output_file: str) -> bool:
        """Export service registry to JSON file"""
        try:
            data = {
                "timestamp": datetime.utcnow().isoformat(),
                "services": {
                    service_name: [
                        {
                            "instance_id": instance.instance_id,
                            "service_name": instance.service_name,
                            "service_type": instance.service_type.value,
                            "host": instance.host,
                            "port": instance.port,
                            "status": instance.status.value,
                            "version": instance.version,
                            "health_check_url": instance.health_check_url,
                            "last_heartbeat": instance.last_heartbeat.isoformat(),
                            "metadata": instance.metadata,
                            "tags": instance.tags,
                            "load_balancer_weight": instance.load_balancer_weight
                        }
                        for instance in instances
                    ]
                    for service_name, instances in self.services.items()
                },
                "routes": [
                    {
                        "route_id": route.route_id,
                        "service_name": route.service_name,
                        "path_pattern": route.path_pattern,
                        "methods": route.methods,
                        "priority": route.priority,
                        "enabled": route.enabled,
                        "rate_limit": route.rate_limit,
                        "timeout": route.timeout,
                        "retry_count": route.retry_count,
                        "circuit_breaker": route.circuit_breaker,
                        "metadata": route.metadata
                    }
                    for route in self.routes.values()
                ],
                "load_balancers": {
                    service_name: {
                        "algorithm": config.algorithm,
                        "health_check_interval": config.health_check_interval,
                        "health_check_timeout": config.health_check_timeout,
                        "max_retries": config.max_retries,
                        "retry_delay": config.retry_delay,
                        "enabled": config.enabled
                    }
                    for service_name, config in self.load_balancers.items()
                }
            }
            
            with open(output_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
            
            logger.info(f"Service registry exported: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting service registry: {e}")
            return False

# Global instance
_service_registry: Optional[ServiceRegistry] = None

def get_service_registry() -> ServiceRegistry:
    """Get global service registry"""
    global _service_registry
    if _service_registry is None:
        _service_registry = ServiceRegistry()
    return _service_registry

def cleanup_service_registry():
    """Cleanup global service registry"""
    global _service_registry
    if _service_registry:
        _service_registry = None
