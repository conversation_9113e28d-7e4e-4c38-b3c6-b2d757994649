"""
Future Architecture Evolution for ASK Pipeline

This module provides comprehensive architecture evolution capabilities:

1. Service Registry - Microservices architecture foundation
2. Event Bus - Event-driven architecture patterns
3. API Gateway - Service mesh and routing
4. Scalability - Horizontal scaling and load balancing

Features:
- Microservices architecture foundation
- Event-driven communication patterns
- Service discovery and registration
- Load balancing and routing
- Horizontal scaling capabilities
- Service mesh communication
- Distributed tracing and monitoring
"""

from .service_registry import (
    ServiceRegistry,
    ServiceInstance,
    ServiceRoute,
    LoadBalancerConfig,
    ServiceStatus,
    ServiceType,
    get_service_registry,
    cleanup_service_registry
)

from .event_bus import (
    EventBus,
    Event,
    EventHandler,
    EventSubscription,
    EventType,
    EventStatus,
    get_event_bus,
    cleanup_event_bus
)

__all__ = [
    # Service Registry
    'ServiceRegistry',
    'ServiceInstance',
    'ServiceRoute',
    'LoadBalancerConfig',
    'ServiceStatus',
    'ServiceType',
    'get_service_registry',
    'cleanup_service_registry',
    
    # Event Bus
    'EventBus',
    'Event',
    'EventHandler',
    'EventSubscription',
    'EventType',
    'EventStatus',
    'get_event_bus',
    'cleanup_event_bus'
]
