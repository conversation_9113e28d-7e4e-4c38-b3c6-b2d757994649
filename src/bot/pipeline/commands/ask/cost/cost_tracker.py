"""
Cost Tracking and Optimization for ASK Pipeline

Provides comprehensive cost monitoring and optimization:
- Track API costs, compute costs, and storage costs
- Implement cost optimization strategies and recommendations
- Add budget control and alerting
- Create cost efficiency metrics and reporting
- Implement resource usage optimization
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import psutil

logger = logging.getLogger(__name__)

class CostCategory(Enum):
    """Cost category enumeration"""
    API_CALLS = "api_calls"
    COMPUTE = "compute"
    STORAGE = "storage"
    NETWORK = "network"
    MEMORY = "memory"
    DATABASE = "database"

class CostAlertLevel(Enum):
    """Cost alert level enumeration"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

@dataclass
class CostEntry:
    """Individual cost entry"""
    timestamp: datetime
    category: CostCategory
    service: str
    operation: str
    cost: float
    currency: str = "USD"
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class CostBudget:
    """Cost budget configuration"""
    name: str
    category: CostCategory
    monthly_limit: float
    daily_limit: float
    hourly_limit: float
    currency: str = "USD"
    alert_thresholds: Dict[CostAlertLevel, float] = field(default_factory=dict)
    enabled: bool = True

@dataclass
class CostAlert:
    """Cost alert instance"""
    alert_id: str
    budget_name: str
    level: CostAlertLevel
    message: str
    current_cost: float
    limit: float
    timestamp: datetime
    acknowledged: bool = False

@dataclass
class CostOptimization:
    """Cost optimization recommendation"""
    category: CostCategory
    current_cost: float
    potential_savings: float
    recommendation: str
    implementation_effort: str  # low, medium, high
    priority: int  # 1-10
    details: Dict[str, Any] = field(default_factory=dict)

class CostTracker:
    """Cost tracking and optimization system"""
    
    def __init__(self):
        self.cost_entries: List[CostEntry] = []
        self.budgets: Dict[str, CostBudget] = {}
        self.alerts: List[CostAlert] = []
        self.optimizations: List[CostOptimization] = []
        
        # Cost tracking state
        self.daily_costs: Dict[str, float] = {}
        self.hourly_costs: Dict[str, float] = {}
        self.monthly_costs: Dict[str, float] = {}
        
        # Initialize default budgets
        self._initialize_default_budgets()
        
        # Start cost monitoring
        self.monitoring_task: Optional[asyncio.Task] = None
    
    def _initialize_default_budgets(self):
        """Initialize default cost budgets"""
        default_budgets = [
            CostBudget(
                name="API Calls",
                category=CostCategory.API_CALLS,
                monthly_limit=1000.0,
                daily_limit=50.0,
                hourly_limit=5.0,
                alert_thresholds={
                    CostAlertLevel.WARNING: 0.7,
                    CostAlertLevel.CRITICAL: 0.9,
                    CostAlertLevel.EMERGENCY: 1.0
                }
            ),
            CostBudget(
                name="Compute Resources",
                category=CostCategory.COMPUTE,
                monthly_limit=500.0,
                daily_limit=25.0,
                hourly_limit=2.5,
                alert_thresholds={
                    CostAlertLevel.WARNING: 0.8,
                    CostAlertLevel.CRITICAL: 0.95,
                    CostAlertLevel.EMERGENCY: 1.0
                }
            ),
            CostBudget(
                name="Storage",
                category=CostCategory.STORAGE,
                monthly_limit=200.0,
                daily_limit=10.0,
                hourly_limit=1.0,
                alert_thresholds={
                    CostAlertLevel.WARNING: 0.8,
                    CostAlertLevel.CRITICAL: 0.95,
                    CostAlertLevel.EMERGENCY: 1.0
                }
            )
        ]
        
        for budget in default_budgets:
            self.budgets[budget.name] = budget
    
    async def start_monitoring(self):
        """Start cost monitoring"""
        if self.monitoring_task:
            return
        
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Cost monitoring started")
    
    async def stop_monitoring(self):
        """Stop cost monitoring"""
        if self.monitoring_task:
            self.monitoring_task.cancel()
            self.monitoring_task = None
        logger.info("Cost monitoring stopped")
    
    async def _monitoring_loop(self):
        """Cost monitoring loop"""
        while True:
            try:
                await self._check_budget_alerts()
                await self._update_cost_aggregations()
                await self._generate_optimization_recommendations()
                await asyncio.sleep(300)  # Check every 5 minutes
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Cost monitoring error: {e}")
                await asyncio.sleep(60)
    
    def add_cost_entry(self, category: CostCategory, service: str, 
                      operation: str, cost: float, metadata: Dict[str, Any] = None) -> str:
        """Add a cost entry"""
        entry = CostEntry(
            timestamp=datetime.utcnow(),
            category=category,
            service=service,
            operation=operation,
            cost=cost,
            metadata=metadata or {}
        )
        
        self.cost_entries.append(entry)
        
        # Update aggregations
        self._update_cost_aggregations_sync(entry)
        
        logger.debug(f"Added cost entry: {category.value} - {service} - ${cost}")
        return f"cost_{len(self.cost_entries)}"
    
    def _update_cost_aggregations_sync(self, entry: CostEntry):
        """Update cost aggregations synchronously"""
        now = datetime.utcnow()
        date_key = now.strftime("%Y-%m-%d")
        hour_key = now.strftime("%Y-%m-%d-%H")
        month_key = now.strftime("%Y-%m")
        
        # Update daily costs
        if date_key not in self.daily_costs:
            self.daily_costs[date_key] = 0.0
        self.daily_costs[date_key] += entry.cost
        
        # Update hourly costs
        if hour_key not in self.hourly_costs:
            self.hourly_costs[hour_key] = 0.0
        self.hourly_costs[hour_key] += entry.cost
        
        # Update monthly costs
        if month_key not in self.monthly_costs:
            self.monthly_costs[month_key] = 0.0
        self.monthly_costs[month_key] += entry.cost
    
    async def _update_cost_aggregations(self):
        """Update cost aggregations"""
        # This would recalculate aggregations from cost entries
        pass
    
    async def _check_budget_alerts(self):
        """Check budget alerts"""
        now = datetime.utcnow()
        
        for budget_name, budget in self.budgets.items():
            if not budget.enabled:
                continue
            
            # Get current costs for this budget
            current_daily = self._get_current_cost(budget.category, "daily")
            current_hourly = self._get_current_cost(budget.category, "hourly")
            current_monthly = self._get_current_cost(budget.category, "monthly")
            
            # Check daily limit
            if current_daily > budget.daily_limit:
                await self._create_alert(budget, "daily", current_daily, budget.daily_limit)
            
            # Check hourly limit
            if current_hourly > budget.hourly_limit:
                await self._create_alert(budget, "hourly", current_hourly, budget.hourly_limit)
            
            # Check monthly limit
            if current_monthly > budget.monthly_limit:
                await self._create_alert(budget, "monthly", current_monthly, budget.monthly_limit)
    
    def _get_current_cost(self, category: CostCategory, period: str) -> float:
        """Get current cost for category and period"""
        now = datetime.utcnow()
        
        if period == "daily":
            date_key = now.strftime("%Y-%m-%d")
            return self.daily_costs.get(date_key, 0.0)
        elif period == "hourly":
            hour_key = now.strftime("%Y-%m-%d-%H")
            return self.hourly_costs.get(hour_key, 0.0)
        elif period == "monthly":
            month_key = now.strftime("%Y-%m")
            return self.monthly_costs.get(month_key, 0.0)
        
        return 0.0
    
    async def _create_alert(self, budget: CostBudget, period: str, 
                           current_cost: float, limit: float):
        """Create cost alert"""
        # Determine alert level
        ratio = current_cost / limit
        level = CostAlertLevel.INFO
        
        for alert_level, threshold in budget.alert_thresholds.items():
            if ratio >= threshold:
                level = alert_level
        
        # Check if alert already exists
        existing_alert = next(
            (a for a in self.alerts 
             if a.budget_name == budget.name and a.level == level and not a.acknowledged),
            None
        )
        
        if existing_alert:
            return  # Alert already exists
        
        alert = CostAlert(
            alert_id=f"cost_alert_{len(self.alerts) + 1}",
            budget_name=budget.name,
            level=level,
            message=f"Cost limit exceeded for {budget.name} ({period}): ${current_cost:.2f} / ${limit:.2f}",
            current_cost=current_cost,
            limit=limit,
            timestamp=datetime.utcnow()
        )
        
        self.alerts.append(alert)
        logger.warning(f"Cost alert created: {alert.message}")
    
    async def _generate_optimization_recommendations(self):
        """Generate cost optimization recommendations"""
        self.optimizations.clear()
        
        # Analyze API costs
        await self._analyze_api_costs()
        
        # Analyze compute costs
        await self._analyze_compute_costs()
        
        # Analyze storage costs
        await self._analyze_storage_costs()
        
        # Analyze memory usage
        await self._analyze_memory_costs()
    
    async def _analyze_api_costs(self):
        """Analyze API costs and generate recommendations"""
        api_entries = [e for e in self.cost_entries if e.category == CostCategory.API_CALLS]
        
        if not api_entries:
            return
        
        total_api_cost = sum(e.cost for e in api_entries)
        
        # Check for high-frequency API calls
        service_costs = {}
        for entry in api_entries:
            if entry.service not in service_costs:
                service_costs[entry.service] = 0.0
            service_costs[entry.service] += entry.cost
        
        # Find most expensive service
        most_expensive_service = max(service_costs.items(), key=lambda x: x[1])
        
        if most_expensive_service[1] > total_api_cost * 0.5:  # More than 50% of total
            optimization = CostOptimization(
                category=CostCategory.API_CALLS,
                current_cost=total_api_cost,
                potential_savings=total_api_cost * 0.2,  # 20% potential savings
                recommendation=f"Consider caching or batching for {most_expensive_service[0]} API calls",
                implementation_effort="medium",
                priority=7,
                details={
                    "service": most_expensive_service[0],
                    "cost": most_expensive_service[1],
                    "percentage": (most_expensive_service[1] / total_api_cost) * 100
                }
            )
            self.optimizations.append(optimization)
    
    async def _analyze_compute_costs(self):
        """Analyze compute costs and generate recommendations"""
        compute_entries = [e for e in self.cost_entries if e.category == CostCategory.COMPUTE]
        
        if not compute_entries:
            return
        
        total_compute_cost = sum(e.cost for e in compute_entries)
        
        # Check for inefficient resource usage
        cpu_usage = psutil.cpu_percent(interval=1)
        memory_usage = psutil.virtual_memory().percent
        
        if cpu_usage < 30:  # Low CPU usage
            optimization = CostOptimization(
                category=CostCategory.COMPUTE,
                current_cost=total_compute_cost,
                potential_savings=total_compute_cost * 0.3,
                recommendation="Consider downsizing compute resources - CPU usage is low",
                implementation_effort="low",
                priority=5,
                details={
                    "cpu_usage": cpu_usage,
                    "memory_usage": memory_usage
                }
            )
            self.optimizations.append(optimization)
    
    async def _analyze_storage_costs(self):
        """Analyze storage costs and generate recommendations"""
        storage_entries = [e for e in self.cost_entries if e.category == CostCategory.STORAGE]
        
        if not storage_entries:
            return
        
        total_storage_cost = sum(e.cost for e in storage_entries)
        
        # Check for unused storage
        disk_usage = psutil.disk_usage('/')
        usage_percentage = (disk_usage.used / disk_usage.total) * 100
        
        if usage_percentage < 50:  # Less than 50% disk usage
            optimization = CostOptimization(
                category=CostCategory.STORAGE,
                current_cost=total_storage_cost,
                potential_savings=total_storage_cost * 0.4,
                recommendation="Consider reducing storage allocation - current usage is low",
                implementation_effort="medium",
                priority=6,
                details={
                    "usage_percentage": usage_percentage,
                    "total_space": disk_usage.total,
                    "used_space": disk_usage.used
                }
            )
            self.optimizations.append(optimization)
    
    async def _analyze_memory_costs(self):
        """Analyze memory costs and generate recommendations"""
        memory_entries = [e for e in self.cost_entries if e.category == CostCategory.MEMORY]
        
        if not memory_entries:
            return
        
        total_memory_cost = sum(e.cost for e in memory_entries)
        
        # Check memory usage
        memory = psutil.virtual_memory()
        
        if memory.percent > 80:  # High memory usage
            optimization = CostOptimization(
                category=CostCategory.MEMORY,
                current_cost=total_memory_cost,
                potential_savings=total_memory_cost * 0.15,
                recommendation="Consider memory optimization or increasing memory allocation",
                implementation_effort="high",
                priority=8,
                details={
                    "memory_usage_percent": memory.percent,
                    "available_memory": memory.available,
                    "total_memory": memory.total
                }
            )
            self.optimizations.append(optimization)
    
    def get_cost_summary(self, period: str = "daily") -> Dict[str, Any]:
        """Get cost summary for period"""
        now = datetime.utcnow()
        
        if period == "daily":
            date_key = now.strftime("%Y-%m-%d")
            total_cost = self.daily_costs.get(date_key, 0.0)
        elif period == "hourly":
            hour_key = now.strftime("%Y-%m-%d-%H")
            total_cost = self.hourly_costs.get(hour_key, 0.0)
        elif period == "monthly":
            month_key = now.strftime("%Y-%m")
            total_cost = self.monthly_costs.get(month_key, 0.0)
        else:
            total_cost = 0.0
        
        # Calculate costs by category
        category_costs = {}
        for entry in self.cost_entries:
            if self._is_entry_in_period(entry, period):
                category = entry.category.value
                if category not in category_costs:
                    category_costs[category] = 0.0
                category_costs[category] += entry.cost
        
        return {
            "period": period,
            "total_cost": total_cost,
            "category_costs": category_costs,
            "timestamp": now.isoformat()
        }
    
    def _is_entry_in_period(self, entry: CostEntry, period: str) -> bool:
        """Check if entry is in specified period"""
        now = datetime.utcnow()
        
        if period == "daily":
            return entry.timestamp.date() == now.date()
        elif period == "hourly":
            return (entry.timestamp.date() == now.date() and 
                   entry.timestamp.hour == now.hour)
        elif period == "monthly":
            return (entry.timestamp.year == now.year and 
                   entry.timestamp.month == now.month)
        
        return False
    
    def get_optimization_recommendations(self) -> List[CostOptimization]:
        """Get cost optimization recommendations"""
        return sorted(self.optimizations, key=lambda x: x.priority, reverse=True)
    
    def get_active_alerts(self) -> List[CostAlert]:
        """Get active cost alerts"""
        return [a for a in self.alerts if not a.acknowledged]
    
    def acknowledge_alert(self, alert_id: str) -> bool:
        """Acknowledge cost alert"""
        for alert in self.alerts:
            if alert.alert_id == alert_id:
                alert.acknowledged = True
                logger.info(f"Cost alert acknowledged: {alert_id}")
                return True
        return False
    
    def add_budget(self, budget: CostBudget):
        """Add cost budget"""
        self.budgets[budget.name] = budget
        logger.info(f"Added cost budget: {budget.name}")
    
    def remove_budget(self, budget_name: str):
        """Remove cost budget"""
        if budget_name in self.budgets:
            del self.budgets[budget_name]
            logger.info(f"Removed cost budget: {budget_name}")
    
    def export_cost_report(self, output_file: str) -> bool:
        """Export cost report to JSON file"""
        try:
            report = {
                "timestamp": datetime.utcnow().isoformat(),
                "cost_summary": {
                    "daily": self.get_cost_summary("daily"),
                    "hourly": self.get_cost_summary("hourly"),
                    "monthly": self.get_cost_summary("monthly")
                },
                "budgets": {
                    name: {
                        "category": budget.category.value,
                        "monthly_limit": budget.monthly_limit,
                        "daily_limit": budget.daily_limit,
                        "hourly_limit": budget.hourly_limit,
                        "enabled": budget.enabled
                    }
                    for name, budget in self.budgets.items()
                },
                "alerts": [
                    {
                        "alert_id": alert.alert_id,
                        "budget_name": alert.budget_name,
                        "level": alert.level.value,
                        "message": alert.message,
                        "current_cost": alert.current_cost,
                        "limit": alert.limit,
                        "timestamp": alert.timestamp.isoformat(),
                        "acknowledged": alert.acknowledged
                    }
                    for alert in self.alerts
                ],
                "optimizations": [
                    {
                        "category": opt.category.value,
                        "current_cost": opt.current_cost,
                        "potential_savings": opt.potential_savings,
                        "recommendation": opt.recommendation,
                        "implementation_effort": opt.implementation_effort,
                        "priority": opt.priority,
                        "details": opt.details
                    }
                    for opt in self.optimizations
                ]
            }
            
            with open(output_file, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            logger.info(f"Cost report exported: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting cost report: {e}")
            return False

# Global instance
_cost_tracker: Optional[CostTracker] = None

def get_cost_tracker() -> CostTracker:
    """Get global cost tracker"""
    global _cost_tracker
    if _cost_tracker is None:
        _cost_tracker = CostTracker()
    return _cost_tracker

def cleanup_cost_tracker():
    """Cleanup global cost tracker"""
    global _cost_tracker
    if _cost_tracker:
        _cost_tracker = None
