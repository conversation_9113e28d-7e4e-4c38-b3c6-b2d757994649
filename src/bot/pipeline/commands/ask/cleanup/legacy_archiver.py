"""
Legacy Component Archiver for ASK Pipeline

Provides tools for archiving legacy components:
- Move deprecated components to archive directory with documentation
- Create migration guide from old to new implementations
- Add deprecation warnings and sunset timeline
- Implement legacy component monitoring and usage tracking
- Create final cleanup and removal procedures

NOTE: This tool only ANALYZES and PREPARES - it does NOT move or delete anything
"""

import os
import logging
from typing import Dict, Any, Optional, List, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
import json

logger = logging.getLogger(__name__)

@dataclass
class LegacyComponent:
    """Legacy component information"""
    name: str
    file_path: str
    component_type: str  # function, class, module, file
    deprecated_since: datetime
    sunset_date: Optional[datetime] = None
    replacement: Optional[str] = None
    usage_count: int = 0
    last_used: Optional[datetime] = None
    deprecation_warnings: List[str] = field(default_factory=list)
    migration_notes: str = ""

@dataclass
class ArchivePlan:
    """Archive plan for legacy components"""
    component: LegacyComponent
    archive_path: str
    documentation_path: str
    migration_guide_path: str
    monitoring_config: Dict[str, Any] = field(default_factory=dict)

class LegacyArchiver:
    """Legacy component archiving system"""
    
    def __init__(self, project_root: str = ".", archive_dir: str = "archive"):
        self.project_root = Path(project_root)
        self.archive_dir = Path(archive_dir)
        self.legacy_components: Dict[str, LegacyComponent] = {}
        self.archive_plans: List[ArchivePlan] = []
        
        # Create archive directory structure
        self._create_archive_structure()
    
    def _create_archive_structure(self):
        """Create archive directory structure"""
        self.archive_dir.mkdir(exist_ok=True)
        (self.archive_dir / "components").mkdir(exist_ok=True)
        (self.archive_dir / "documentation").mkdir(exist_ok=True)
        (self.archive_dir / "migration_guides").mkdir(exist_ok=True)
        (self.archive_dir / "monitoring").mkdir(exist_ok=True)
    
    def identify_legacy_components(self) -> List[LegacyComponent]:
        """Identify legacy components in the project"""
        logger.info("Identifying legacy components")
        
        # Look for deprecation markers
        self._scan_for_deprecation_markers()
        
        # Look for TODO/FIXME comments
        self._scan_for_todo_comments()
        
        # Look for unused imports
        self._scan_for_unused_imports()
        
        # Look for old patterns
        self._scan_for_old_patterns()
        
        return list(self.legacy_components.values())
    
    def _scan_for_deprecation_markers(self):
        """Scan for deprecation markers"""
        for py_file in self.project_root.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if '@deprecated' in line or 'deprecated' in line.lower():
                        # Extract component information
                        component_name = self._extract_component_name(line, content)
                        if component_name:
                            self._add_legacy_component(
                                name=component_name,
                                file_path=str(py_file),
                                component_type="function",
                                deprecated_since=datetime.utcnow()
                            )
                            
            except Exception as e:
                logger.error(f"Error scanning {py_file}: {e}")
    
    def _scan_for_todo_comments(self):
        """Scan for TODO comments that might indicate legacy code"""
        for py_file in self.project_root.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if 'TODO' in line.upper() and 'remove' in line.lower():
                        component_name = self._extract_component_name(line, content)
                        if component_name:
                            self._add_legacy_component(
                                name=component_name,
                                file_path=str(py_file),
                                component_type="function",
                                deprecated_since=datetime.utcnow()
                            )
                            
            except Exception as e:
                logger.error(f"Error scanning {py_file}: {e}")
    
    def _scan_for_unused_imports(self):
        """Scan for unused imports that might be legacy"""
        # This would integrate with the dead code analyzer
        pass
    
    def _scan_for_old_patterns(self):
        """Scan for old coding patterns"""
        old_patterns = [
            'import imp',
            'from imp import',
            'execfile(',
            'reload(',
            'xrange(',
            'raw_input(',
            'unicode(',
            'basestring',
            'cmp(',
            'reduce(',
        ]
        
        for py_file in self.project_root.rglob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for pattern in old_patterns:
                    if pattern in content:
                        self._add_legacy_component(
                            name=f"Legacy pattern: {pattern}",
                            file_path=str(py_file),
                            component_type="pattern",
                            deprecated_since=datetime.utcnow()
                        )
                        
            except Exception as e:
                logger.error(f"Error scanning {py_file}: {e}")
    
    def _extract_component_name(self, line: str, content: str) -> Optional[str]:
        """Extract component name from line"""
        # Simple extraction - could be improved
        if 'def ' in line:
            return line.split('def ')[1].split('(')[0].strip()
        elif 'class ' in line:
            return line.split('class ')[1].split('(')[0].split(':')[0].strip()
        return None
    
    def _add_legacy_component(self, name: str, file_path: str, 
                            component_type: str, deprecated_since: datetime):
        """Add legacy component to tracking"""
        if name not in self.legacy_components:
            self.legacy_components[name] = LegacyComponent(
                name=name,
                file_path=file_path,
                component_type=component_type,
                deprecated_since=deprecated_since
            )
    
    def create_archive_plan(self, component: LegacyComponent) -> ArchivePlan:
        """Create archive plan for a legacy component"""
        # Generate archive paths
        archive_path = self.archive_dir / "components" / f"{component.name}.py"
        documentation_path = self.archive_dir / "documentation" / f"{component.name}.md"
        migration_guide_path = self.archive_dir / "migration_guides" / f"{component.name}_migration.md"
        
        # Create monitoring config
        monitoring_config = {
            "component_name": component.name,
            "file_path": component.file_path,
            "monitoring_enabled": True,
            "usage_tracking": True,
            "deprecation_warnings": True
        }
        
        plan = ArchivePlan(
            component=component,
            archive_path=str(archive_path),
            documentation_path=str(documentation_path),
            migration_guide_path=str(migration_guide_path),
            monitoring_config=monitoring_config
        )
        
        self.archive_plans.append(plan)
        return plan
    
    def generate_migration_guide(self, component: LegacyComponent) -> str:
        """Generate migration guide for legacy component"""
        guide_content = f"""# Migration Guide: {component.name}

## Overview

This component has been deprecated and should be migrated to the new implementation.

## Deprecation Information

- **Deprecated Since**: {component.deprecated_since.strftime('%Y-%m-%d')}
- **Sunset Date**: {component.sunset_date.strftime('%Y-%m-%d') if component.sunset_date else 'TBD'}
- **Replacement**: {component.replacement or 'TBD'}

## Migration Steps

1. **Identify Usage**: Find all places where this component is used
2. **Update Imports**: Replace old imports with new ones
3. **Update Function Calls**: Update function calls to use new API
4. **Test Changes**: Ensure all functionality works with new implementation
5. **Remove Old Code**: Remove the deprecated component

## Code Examples

### Before (Deprecated)
```python
# Old usage example
from old_module import {component.name}

result = {component.name}(param1, param2)
```

### After (New Implementation)
```python
# New usage example
from new_module import {component.replacement or 'new_function'}

result = {component.replacement or 'new_function'}(param1, param2)
```

## Breaking Changes

{component.migration_notes or 'No breaking changes documented.'}

## Support

If you need help with migration, please:
1. Check the documentation
2. Contact the development team
3. Open an issue on GitHub

---
*Generated on: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        return guide_content
    
    def generate_deprecation_warning(self, component: LegacyComponent) -> str:
        """Generate deprecation warning code"""
        warning_code = f'''"""
Deprecation warning for {component.name}

This component is deprecated and will be removed in a future version.
Please migrate to the new implementation.
"""

import warnings
from datetime import datetime

def {component.name}(*args, **kwargs):
    """Deprecated function - use {component.replacement or 'new_function'} instead"""
    
    # Issue deprecation warning
    warnings.warn(
        f"{component.name} is deprecated and will be removed in a future version. "
        f"Please use {component.replacement or 'new_function'} instead.",
        DeprecationWarning,
        stacklevel=2
    )
    
    # Log usage for monitoring
    logger.warning(f"Deprecated function {component.name} called from {{__file__}}")
    
    # Call replacement function if available
    if {component.replacement or 'new_function'}:
        return {component.replacement or 'new_function'}(*args, **kwargs)
    else:
        raise NotImplementedError(f"{component.name} is deprecated and no replacement is available")
'''
        
        return warning_code
    
    def generate_archive_documentation(self, component: LegacyComponent) -> str:
        """Generate archive documentation"""
        doc_content = f"""# Archived Component: {component.name}

## Component Information

- **Type**: {component.component_type}
- **File Path**: {component.file_path}
- **Deprecated Since**: {component.deprecated_since.strftime('%Y-%m-%d')}
- **Sunset Date**: {component.sunset_date.strftime('%Y-%m-%d') if component.sunset_date else 'TBD'}
- **Replacement**: {component.replacement or 'TBD'}

## Archive Reason

This component has been deprecated due to:
- {component.migration_notes or 'No specific reason documented'}

## Usage Statistics

- **Usage Count**: {component.usage_count}
- **Last Used**: {component.last_used.strftime('%Y-%m-%d') if component.last_used else 'Unknown'}

## Migration Path

See the migration guide for details on how to migrate from this component.

## Archive Date

{datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}

---
*This component is archived and should not be used in new code.*
"""
        
        return doc_content
    
    def export_archive_plan(self, output_file: str) -> bool:
        """Export archive plan to JSON file"""
        try:
            plan_data = {
                "archive_timestamp": datetime.utcnow().isoformat(),
                "legacy_components": [
                    {
                        "name": comp.name,
                        "file_path": comp.file_path,
                        "component_type": comp.component_type,
                        "deprecated_since": comp.deprecated_since.isoformat(),
                        "sunset_date": comp.sunset_date.isoformat() if comp.sunset_date else None,
                        "replacement": comp.replacement,
                        "usage_count": comp.usage_count,
                        "last_used": comp.last_used.isoformat() if comp.last_used else None,
                        "migration_notes": comp.migration_notes
                    }
                    for comp in self.legacy_components.values()
                ],
                "archive_plans": [
                    {
                        "component_name": plan.component.name,
                        "archive_path": plan.archive_path,
                        "documentation_path": plan.documentation_path,
                        "migration_guide_path": plan.migration_guide_path,
                        "monitoring_config": plan.monitoring_config
                    }
                    for plan in self.archive_plans
                ]
            }
            
            with open(output_file, 'w') as f:
                json.dump(plan_data, f, indent=2, default=str)
            
            logger.info(f"Archive plan exported: {output_file}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting archive plan: {e}")
            return False

# Global instance
_legacy_archiver: Optional[LegacyArchiver] = None

def get_legacy_archiver() -> LegacyArchiver:
    """Get global legacy archiver"""
    global _legacy_archiver
    if _legacy_archiver is None:
        _legacy_archiver = LegacyArchiver()
    return _legacy_archiver

def cleanup_legacy_archiver():
    """Cleanup global legacy archiver"""
    global _legacy_archiver
    if _legacy_archiver:
        _legacy_archiver = None
