{"analysis_timestamp": "2025-09-24T01:32:41.476246", "total_files_analyzed": 135, "files_with_issues": 18, "summary": {"unused_imports": 0, "unused_functions": 0, "unused_classes": 0, "commented_code": 13, "debug_statements": 16, "todo_comments": 38}, "files": {"src/bot/pipeline/commands/ask/executor.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/pipeline.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/ask_config.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/tools/mcp_manager.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [{"line_number": 373, "content": "# Base score from priority (lower priority number = higher score)", "suggested_action": "Review if this code should be removed or uncommented"}, {"line_number": 376, "content": "# Performance score (lower error rate = higher score)", "suggested_action": "Review if this code should be removed or uncommented"}], "debug_statements": [], "todo_comments": [{"line_number": 254, "content": "# Note: This would initialize free MCP servers when they're properly installed", "pattern": "NOTE:", "suggested_action": "Address the TODO item or remove if no longer relevant"}]}, "src/bot/pipeline/commands/ask/tools/fallback_handler.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/tools/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/performance/benchmark.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/performance/resource_manager.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/performance/database_optimizer.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/performance/request_batcher.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/performance/smart_cache.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/performance/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/performance/async_optimizer.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/performance/connection_pool.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/config/environment_profiles.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": [{"line_number": 50, "content": "debug: bool", "pattern": "BUG:", "suggested_action": "Address the TODO item or remove if no longer relevant"}]}, "src/bot/pipeline/commands/ask/config/feature_flags.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/config/config_manager.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/config/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/config/secrets_manager.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/security/simplified_security.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/security/test_security_scanner.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/security/security_scanner.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/security/test_auth_manager.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/security/input_validator.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/security/test_input_validator.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/security/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/security/test_rate_limiter.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/security/auth_manager.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/security/rate_limiter.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/errors/fallback_strategy.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/errors/error_manager.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [{"line_number": 287, "content": "# Adjust based on execution time (longer = more severe)", "suggested_action": "Review if this code should be removed or uncommented"}], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/errors/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/audit/audit_logger.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/audit/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/api/integration.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/api/contracts.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/api/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/api/backward_compatibility.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/api/versioning.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/cost/resource_optimizer.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/cost/cost_tracker.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/cost/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/modernization/modern_python.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/modernization/dependency_manager.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/modernization/containerization.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/modernization/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/compliance/audit_logger.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/compliance/data_manager.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/compliance/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/compliance/compliance_logger.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/cleanup/legacy_archiver.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": [{"line_number": 11, "content": "NOTE: This tool only ANALYZES and PREPARES - it does NOT move or delete anything", "pattern": "NOTE:", "suggested_action": "Address the TODO item or remove if no longer relevant"}]}, "src/bot/pipeline/commands/ask/cleanup/test_consolidator.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": [{"line_number": 11, "content": "NOTE: This tool only ANALYZES and REPORTS - it does NOT delete or modify anything", "pattern": "NOTE:", "suggested_action": "Address the TODO item or remove if no longer relevant"}]}, "src/bot/pipeline/commands/ask/cleanup/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": [{"line_number": 18, "content": "NOTE: All tools are ANALYSIS-ONLY and do NOT delete or modify code", "pattern": "NOTE:", "suggested_action": "Address the TODO item or remove if no longer relevant"}]}, "src/bot/pipeline/commands/ask/cleanup/dead_code_analyzer.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [{"line_number": 235, "content": "r'console\\.log\\s*\\(',  # console.log (if any JS mixed in)", "pattern": "console\\.log\\s*\\(", "suggested_action": "Remove or replace with proper logging"}, {"line_number": 236, "content": "r'debugger',  # debugger statements", "pattern": "debugger", "suggested_action": "Remove or replace with proper logging"}, {"line_number": 239, "content": "r'import pdb',  # pdb imports", "pattern": "import pdb", "suggested_action": "Remove or replace with proper logging"}], "todo_comments": [{"line_number": 11, "content": "NOTE: This tool only ANALYZES and REPORTS - it does NOT delete anything", "pattern": "NOTE:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 258, "content": "r'TODO:',", "pattern": "TODO:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 259, "content": "r'FIXME:',", "pattern": "FIXME:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 260, "content": "r'XXX:',", "pattern": "XXX:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 261, "content": "r'HACK:',", "pattern": "HACK:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 262, "content": "r'NOTE:',", "pattern": "NOTE:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 263, "content": "r'BUG:',", "pattern": "BUG:", "suggested_action": "Address the TODO item or remove if no longer relevant"}]}, "src/bot/pipeline/commands/ask/stages/query_analyzer.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/stages/intent_detector.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/stages/data_collector.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/stages/analysis_components.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/stages/simplified_tool_orchestrator.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/stages/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/stages/formatter.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/stages/ai_synthesizer.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/stages/response_generator.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/tests/test_response_generator.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/tests/test_intent_detector.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/tests/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/tests/test_error_handling.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/tests/test_tool_orchestrator.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/deployment/documentation.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/deployment/cicd_pipeline.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/deployment/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/deployment/monitoring.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/core/controller.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/core/stage_executor.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/core/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/core/error_coordinator.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/core/stage_manager.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/observability/metrics.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/observability/logger.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/observability/log_analyzer.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [{"line_number": 123, "content": "print(f\"Error parsing line {line_num} in {file_path}: {e}\")", "pattern": "print\\s*\\(", "suggested_action": "Remove or replace with proper logging"}, {"line_number": 127, "content": "print(f\"Error reading log file {file_path}: {e}\")", "pattern": "print\\s*\\(", "suggested_action": "Remove or replace with proper logging"}], "todo_comments": []}, "src/bot/pipeline/commands/ask/observability/tracer.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/observability/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/observability/health_checker.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [{"line_number": 480, "content": "# In a real implementation, this might:", "suggested_action": "Review if this code should be removed or uncommented"}, {"line_number": 494, "content": "# In a real implementation, this might:", "suggested_action": "Review if this code should be removed or uncommented"}, {"line_number": 508, "content": "# In a real implementation, this might:", "suggested_action": "Review if this code should be removed or uncommented"}], "debug_statements": [{"line_number": 182, "content": "print(f\"✅ Health monitoring started for {len(self.components)} components\")", "pattern": "print\\s*\\(", "suggested_action": "Remove or replace with proper logging"}, {"line_number": 197, "content": "print(\"🛑 Health monitoring stopped\")", "pattern": "print\\s*\\(", "suggested_action": "Remove or replace with proper logging"}, {"line_number": 286, "content": "print(f\"🔄 Attempting recovery for component: {name}\")", "pattern": "print\\s*\\(", "suggested_action": "Remove or replace with proper logging"}, {"line_number": 291, "content": "print(f\"✅ Recovery successful for component: {name}\")", "pattern": "print\\s*\\(", "suggested_action": "Remove or replace with proper logging"}, {"line_number": 293, "content": "print(f\"❌ Recovery failed for component: {name}\")", "pattern": "print\\s*\\(", "suggested_action": "Remove or replace with proper logging"}, {"line_number": 296, "content": "print(f\"❌ Recovery error for component {name}: {e}\")", "pattern": "print\\s*\\(", "suggested_action": "Remove or replace with proper logging"}], "todo_comments": []}, "src/bot/pipeline/commands/ask/architecture/service_registry.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/architecture/event_bus.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/architecture/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/cache/intelligent_cache.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": [{"line_number": 357, "content": "# Note: Can't join daemon thread in __del__", "pattern": "NOTE:", "suggested_action": "Address the TODO item or remove if no longer relevant"}]}, "src/bot/pipeline/commands/ask/cache/unified_cache.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [{"line_number": 146, "content": "# if self.config.cache_warming_enabled:", "suggested_action": "Review if this code should be removed or uncommented"}], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/cache/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/testing/test_infrastructure.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/testing/unit_tests.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [{"line_number": 534, "content": "print(f\"Coverage tracking error: {e}\")", "pattern": "print\\s*\\(", "suggested_action": "Remove or replace with proper logging"}], "todo_comments": []}, "src/bot/pipeline/commands/ask/testing/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/testing/integration_tests.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [{"line_number": 508, "content": "print(f\"Integration coverage tracking error: {e}\")", "pattern": "print\\s*\\(", "suggested_action": "Remove or replace with proper logging"}], "todo_comments": []}, "src/bot/pipeline/commands/ask/testing/security_tests.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [{"line_number": 544, "content": "print(f\"Security coverage tracking error: {e}\")", "pattern": "print\\s*\\(", "suggested_action": "Remove or replace with proper logging"}], "todo_comments": []}, "src/bot/pipeline/commands/ask/testing/performance_tests.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [{"line_number": 588, "content": "print(f\"Performance coverage tracking error: {e}\")", "pattern": "print\\s*\\(", "suggested_action": "Remove or replace with proper logging"}], "todo_comments": []}, "src/bot/pipeline/commands/ask/quality/documentation.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": [{"line_number": 72, "content": "docstring += \"TODO: Add function description\\n\\n\"", "pattern": "TODO:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 81, "content": "docstring += f\"    TODO: Add parameter description\\n\\n\"", "pattern": "TODO:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 89, "content": "docstring += \"    TODO: Add return description\\n\\n\"", "pattern": "TODO:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 94, "content": "docstring += \"TODO: Add exception descriptions\\n\\n\"", "pattern": "TODO:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 99, "content": "docstring += \"TODO: Add usage examples\\n\\n\"", "pattern": "TODO:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 114, "content": "docstring += \"TODO: Add class description\\n\\n\"", "pattern": "TODO:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 123, "content": "docstring += f\"    TODO: Add attribute description\\n\\n\"", "pattern": "TODO:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 133, "content": "docstring += f\"    TODO: Add method description\\n\\n\"", "pattern": "TODO:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 138, "content": "docstring += \"TODO: Add usage examples\\n\\n\"", "pattern": "TODO:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 153, "content": "docstring += \"TODO: Add property description\\n\\n\"", "pattern": "TODO:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 168, "content": "description = module.__doc__ or \"TODO: Add module description\"", "pattern": "TODO:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 204, "content": "docstring = inspect.getdoc(cls) or \"TODO: Add class description\"", "pattern": "TODO:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 219, "content": "'description': inspect.getdoc(prop) or \"TODO: Add property description\"", "pattern": "TODO:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 232, "content": "docstring = inspect.getdoc(func) or \"TODO: Add function description\"", "pattern": "TODO:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 244, "content": "'description': f\"TODO: Add description for {param_name}\"", "pattern": "TODO:", "suggested_action": "Address the TODO item or remove if no longer relevant"}]}, "src/bot/pipeline/commands/ask/quality/code_standards.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/quality/type_safety.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/bot/pipeline/commands/ask/quality/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/query_cache.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/enhanced_symbol_extractor.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/response_synthesizer.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/ai_processor_robust.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [{"line_number": 1687, "content": "print(result)", "pattern": "print\\s*\\(", "suggested_action": "Remove or replace with proper logging"}], "todo_comments": [{"line_number": 522, "content": "# Debug: inspect snapshot indicators before formatting (non-invasive)", "pattern": "BUG:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 733, "content": "logger.info(\"DEBUG: Initializing RobustFinancialAnalyzer - checking pandas\")", "pattern": "BUG:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 736, "content": "logger.info(f\"DEBUG: Pandas available in RobustFinancialAnalyzer - version: {pd.__version__ if hasattr(pd, '__version__') else 'unknown'}\")", "pattern": "BUG:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 738, "content": "logger.error(f\"DEBUG: Pandas error in RobustFinancialAnalyzer: {pd_err}\")", "pattern": "BUG:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 745, "content": "logger.info(\"DEBUG: RobustFinancialAnalyzer init complete\")", "pattern": "BUG:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 922, "content": "# Debug: log the values used to create the MarketSnapshot (non-invasive)", "pattern": "BUG:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 1003, "content": "# NOTE: UnifiedAIProcessor removed - use RobustFinancialAnalyzer directly", "pattern": "NOTE:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 1019, "content": "# Note: context parameter added for compatibility but not used", "pattern": "NOTE:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 1051, "content": "# Debug: log raw market data returned by provider (only when debug enabled)", "pattern": "BUG:", "suggested_action": "Address the TODO item or remove if no longer relevant"}, {"line_number": 1382, "content": "result_text += \"\\n[Note: I have full internet access and can browse Reddit, social media, and any website you need.]\"", "pattern": "NOTE:", "suggested_action": "Address the TODO item or remove if no longer relevant"}]}, "src/shared/ai_services/cross_validation_ai.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/fact_verifier.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/tool_registry.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/local_fallback_ai.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/rate_limit_handler.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/timeout_manager.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/ai_tool_registry.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/intelligent_text_parser.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/ai_chat_processor.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/circuit_breaker.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/enhanced_intent_detector.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/ai_security_detector.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/simple_model_config.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/fast_price_lookup.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/unified_ai_processor.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [{"line_number": 145, "content": "# ==================== PRIMARY INTERFACE METHODS ====================", "suggested_action": "Review if this code should be removed or uncommented"}, {"line_number": 267, "content": "# ==================== SPECIALIZED METHODS ====================", "suggested_action": "Review if this code should be removed or uncommented"}, {"line_number": 297, "content": "# ==================== INTERNAL HELPER METHODS ====================", "suggested_action": "Review if this code should be removed or uncommented"}, {"line_number": 456, "content": "# ==================== FACTORY FUNCTIONS ====================", "suggested_action": "Review if this code should be removed or uncommented"}, {"line_number": 468, "content": "# ==================== BACKWARD COMPATIBILITY ====================", "suggested_action": "Review if this code should be removed or uncommented"}, {"line_number": 492, "content": "# ==================== GLOBAL INSTANCE ====================", "suggested_action": "Review if this code should be removed or uncommented"}], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/simple_query_analyzer.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/smart_model_router.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/query_router.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/enhanced_ai_client.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/ai_service_wrapper.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/openrouter_key.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_services/anti_hallucination_prompt.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_chat/data_fetcher.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_chat/config.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_chat/models.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_chat/fallbacks.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_chat/__init__.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_chat/response_formatter.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_chat/ai_client.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}, "src/shared/ai_chat/processor.py": {"unused_imports": [], "unused_functions": [], "unused_classes": [], "commented_code": [], "debug_statements": [], "todo_comments": []}}, "metrics": {"src/bot/pipeline/commands/ask/executor.py": {"total_lines": 142, "code_lines": 108, "comment_lines": 9, "blank_lines": 25, "function_count": 5, "class_count": 1, "import_count": 6, "complexity_score": 6, "maintainability_index": 107.14000000000001}, "src/bot/pipeline/commands/ask/pipeline.py": {"total_lines": 32, "code_lines": 19, "comment_lines": 5, "blank_lines": 8, "function_count": 1, "class_count": 1, "import_count": 1, "complexity_score": 2, "maintainability_index": 153.23999999999998}, "src/bot/pipeline/commands/ask/ask_config.py": {"total_lines": 200, "code_lines": 146, "comment_lines": 12, "blank_lines": 42, "function_count": 7, "class_count": 6, "import_count": 5, "complexity_score": 19, "maintainability_index": 26.200000000000003}, "src/bot/pipeline/commands/ask/__init__.py": {"total_lines": 31, "code_lines": 24, "comment_lines": 1, "blank_lines": 6, "function_count": 0, "class_count": 0, "import_count": 3, "complexity_score": 1, "maintainability_index": 158.67000000000002}, "src/bot/pipeline/commands/ask/tools/mcp_manager.py": {"total_lines": 712, "code_lines": 549, "comment_lines": 39, "blank_lines": 124, "function_count": 12, "class_count": 5, "import_count": 9, "complexity_score": 44, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/tools/fallback_handler.py": {"total_lines": 191, "code_lines": 153, "comment_lines": 7, "blank_lines": 31, "function_count": 2, "class_count": 2, "import_count": 6, "complexity_score": 12, "maintainability_index": 64.66999999999999}, "src/bot/pipeline/commands/ask/tools/__init__.py": {"total_lines": 40, "code_lines": 31, "comment_lines": 2, "blank_lines": 7, "function_count": 0, "class_count": 0, "import_count": 3, "complexity_score": 1, "maintainability_index": 156.60000000000002}, "src/bot/pipeline/commands/ask/performance/benchmark.py": {"total_lines": 480, "code_lines": 386, "comment_lines": 26, "blank_lines": 68, "function_count": 7, "class_count": 6, "import_count": 12, "complexity_score": 33, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/performance/resource_manager.py": {"total_lines": 405, "code_lines": 323, "comment_lines": 21, "blank_lines": 61, "function_count": 4, "class_count": 6, "import_count": 12, "complexity_score": 40, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/performance/database_optimizer.py": {"total_lines": 478, "code_lines": 392, "comment_lines": 25, "blank_lines": 61, "function_count": 5, "class_count": 6, "import_count": 11, "complexity_score": 47, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/performance/request_batcher.py": {"total_lines": 401, "code_lines": 319, "comment_lines": 20, "blank_lines": 62, "function_count": 5, "class_count": 7, "import_count": 8, "complexity_score": 42, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/performance/smart_cache.py": {"total_lines": 441, "code_lines": 348, "comment_lines": 25, "blank_lines": 68, "function_count": 10, "class_count": 5, "import_count": 13, "complexity_score": 49, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/performance/__init__.py": {"total_lines": 129, "code_lines": 110, "comment_lines": 5, "blank_lines": 14, "function_count": 0, "class_count": 0, "import_count": 5, "complexity_score": 1, "maintainability_index": 136.13}, "src/bot/pipeline/commands/ask/performance/async_optimizer.py": {"total_lines": 387, "code_lines": 324, "comment_lines": 4, "blank_lines": 59, "function_count": 16, "class_count": 6, "import_count": 11, "complexity_score": 31, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/performance/connection_pool.py": {"total_lines": 358, "code_lines": 305, "comment_lines": 8, "blank_lines": 45, "function_count": 7, "class_count": 6, "import_count": 9, "complexity_score": 29, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/config/environment_profiles.py": {"total_lines": 870, "code_lines": 748, "comment_lines": 9, "blank_lines": 113, "function_count": 18, "class_count": 7, "import_count": 12, "complexity_score": 81, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/config/feature_flags.py": {"total_lines": 515, "code_lines": 419, "comment_lines": 16, "blank_lines": 80, "function_count": 21, "class_count": 6, "import_count": 12, "complexity_score": 65, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/config/config_manager.py": {"total_lines": 537, "code_lines": 422, "comment_lines": 25, "blank_lines": 90, "function_count": 25, "class_count": 7, "import_count": 18, "complexity_score": 68, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/config/__init__.py": {"total_lines": 103, "code_lines": 87, "comment_lines": 4, "blank_lines": 12, "function_count": 0, "class_count": 0, "import_count": 4, "complexity_score": 1, "maintainability_index": 142.11}, "src/bot/pipeline/commands/ask/config/secrets_manager.py": {"total_lines": 506, "code_lines": 386, "comment_lines": 37, "blank_lines": 83, "function_count": 19, "class_count": 5, "import_count": 16, "complexity_score": 72, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/security/simplified_security.py": {"total_lines": 294, "code_lines": 218, "comment_lines": 17, "blank_lines": 59, "function_count": 8, "class_count": 2, "import_count": 5, "complexity_score": 25, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/security/test_security_scanner.py": {"total_lines": 234, "code_lines": 198, "comment_lines": 4, "blank_lines": 32, "function_count": 30, "class_count": 1, "import_count": 3, "complexity_score": 1, "maintainability_index": 111.98000000000002}, "src/bot/pipeline/commands/ask/security/security_scanner.py": {"total_lines": 314, "code_lines": 237, "comment_lines": 25, "blank_lines": 52, "function_count": 7, "class_count": 3, "import_count": 5, "complexity_score": 29, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/security/test_auth_manager.py": {"total_lines": 439, "code_lines": 321, "comment_lines": 19, "blank_lines": 99, "function_count": 15, "class_count": 1, "import_count": 6, "complexity_score": 2, "maintainability_index": 59.629999999999995}, "src/bot/pipeline/commands/ask/security/input_validator.py": {"total_lines": 302, "code_lines": 223, "comment_lines": 24, "blank_lines": 55, "function_count": 5, "class_count": 3, "import_count": 6, "complexity_score": 24, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/security/test_input_validator.py": {"total_lines": 298, "code_lines": 228, "comment_lines": 16, "blank_lines": 54, "function_count": 2, "class_count": 2, "import_count": 4, "complexity_score": 10, "maintainability_index": 50.459999999999994}, "src/bot/pipeline/commands/ask/security/__init__.py": {"total_lines": 22, "code_lines": 19, "comment_lines": 0, "blank_lines": 3, "function_count": 0, "class_count": 0, "import_count": 2, "complexity_score": 1, "maintainability_index": 160.74}, "src/bot/pipeline/commands/ask/security/test_rate_limiter.py": {"total_lines": 399, "code_lines": 270, "comment_lines": 49, "blank_lines": 80, "function_count": 6, "class_count": 3, "import_count": 5, "complexity_score": 26, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/security/auth_manager.py": {"total_lines": 271, "code_lines": 211, "comment_lines": 18, "blank_lines": 42, "function_count": 11, "class_count": 2, "import_count": 7, "complexity_score": 31, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/security/rate_limiter.py": {"total_lines": 503, "code_lines": 386, "comment_lines": 36, "blank_lines": 81, "function_count": 2, "class_count": 4, "import_count": 6, "complexity_score": 33, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/errors/fallback_strategy.py": {"total_lines": 440, "code_lines": 371, "comment_lines": 16, "blank_lines": 53, "function_count": 2, "class_count": 3, "import_count": 7, "complexity_score": 25, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/errors/error_manager.py": {"total_lines": 498, "code_lines": 408, "comment_lines": 29, "blank_lines": 61, "function_count": 10, "class_count": 5, "import_count": 6, "complexity_score": 45, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/errors/__init__.py": {"total_lines": 39, "code_lines": 30, "comment_lines": 2, "blank_lines": 7, "function_count": 0, "class_count": 0, "import_count": 2, "complexity_score": 1, "maintainability_index": 156.83}, "src/bot/pipeline/commands/ask/audit/audit_logger.py": {"total_lines": 469, "code_lines": 393, "comment_lines": 7, "blank_lines": 69, "function_count": 21, "class_count": 5, "import_count": 9, "complexity_score": 27, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/audit/__init__.py": {"total_lines": 29, "code_lines": 25, "comment_lines": 0, "blank_lines": 4, "function_count": 0, "class_count": 0, "import_count": 1, "complexity_score": 1, "maintainability_index": 159.13000000000002}, "src/bot/pipeline/commands/ask/api/integration.py": {"total_lines": 595, "code_lines": 474, "comment_lines": 15, "blank_lines": 106, "function_count": 22, "class_count": 13, "import_count": 13, "complexity_score": 65, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/api/contracts.py": {"total_lines": 388, "code_lines": 338, "comment_lines": 1, "blank_lines": 49, "function_count": 9, "class_count": 31, "import_count": 5, "complexity_score": 14, "maintainability_index": 8.959999999999994}, "src/bot/pipeline/commands/ask/api/__init__.py": {"total_lines": 187, "code_lines": 171, "comment_lines": 4, "blank_lines": 12, "function_count": 0, "class_count": 0, "import_count": 4, "complexity_score": 1, "maintainability_index": 122.79}, "src/bot/pipeline/commands/ask/api/backward_compatibility.py": {"total_lines": 516, "code_lines": 411, "comment_lines": 17, "blank_lines": 88, "function_count": 25, "class_count": 7, "import_count": 12, "complexity_score": 35, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/api/versioning.py": {"total_lines": 421, "code_lines": 313, "comment_lines": 30, "blank_lines": 78, "function_count": 26, "class_count": 4, "import_count": 8, "complexity_score": 33, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/cost/resource_optimizer.py": {"total_lines": 617, "code_lines": 501, "comment_lines": 22, "blank_lines": 94, "function_count": 14, "class_count": 7, "import_count": 10, "complexity_score": 43, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/cost/cost_tracker.py": {"total_lines": 584, "code_lines": 469, "comment_lines": 25, "blank_lines": 90, "function_count": 15, "class_count": 7, "import_count": 9, "complexity_score": 52, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/cost/__init__.py": {"total_lines": 67, "code_lines": 57, "comment_lines": 2, "blank_lines": 8, "function_count": 0, "class_count": 0, "import_count": 2, "complexity_score": 1, "maintainability_index": 150.39000000000001}, "src/bot/pipeline/commands/ask/modernization/modern_python.py": {"total_lines": 516, "code_lines": 405, "comment_lines": 20, "blank_lines": 91, "function_count": 34, "class_count": 13, "import_count": 10, "complexity_score": 39, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/modernization/dependency_manager.py": {"total_lines": 566, "code_lines": 452, "comment_lines": 22, "blank_lines": 92, "function_count": 10, "class_count": 6, "import_count": 15, "complexity_score": 65, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/modernization/containerization.py": {"total_lines": 646, "code_lines": 500, "comment_lines": 39, "blank_lines": 107, "function_count": 24, "class_count": 8, "import_count": 11, "complexity_score": 27, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/modernization/__init__.py": {"total_lines": 107, "code_lines": 94, "comment_lines": 3, "blank_lines": 10, "function_count": 0, "class_count": 0, "import_count": 3, "complexity_score": 1, "maintainability_index": 141.19}, "src/bot/pipeline/commands/ask/compliance/audit_logger.py": {"total_lines": 679, "code_lines": 561, "comment_lines": 28, "blank_lines": 90, "function_count": 19, "class_count": 8, "import_count": 9, "complexity_score": 46, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/compliance/data_manager.py": {"total_lines": 638, "code_lines": 526, "comment_lines": 26, "blank_lines": 86, "function_count": 14, "class_count": 8, "import_count": 10, "complexity_score": 55, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/compliance/__init__.py": {"total_lines": 72, "code_lines": 62, "comment_lines": 2, "blank_lines": 8, "function_count": 0, "class_count": 0, "import_count": 2, "complexity_score": 1, "maintainability_index": 149.24}, "src/bot/pipeline/commands/ask/compliance/compliance_logger.py": {"total_lines": 14, "code_lines": 8, "comment_lines": 0, "blank_lines": 6, "function_count": 0, "class_count": 0, "import_count": 1, "complexity_score": 1, "maintainability_index": 162.58}, "src/bot/pipeline/commands/ask/cleanup/legacy_archiver.py": {"total_lines": 398, "code_lines": 289, "comment_lines": 29, "blank_lines": 80, "function_count": 16, "class_count": 3, "import_count": 7, "complexity_score": 37, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/cleanup/test_consolidator.py": {"total_lines": 596, "code_lines": 430, "comment_lines": 51, "blank_lines": 115, "function_count": 24, "class_count": 5, "import_count": 9, "complexity_score": 73, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/cleanup/__init__.py": {"total_lines": 71, "code_lines": 57, "comment_lines": 3, "blank_lines": 11, "function_count": 0, "class_count": 0, "import_count": 3, "complexity_score": 1, "maintainability_index": 149.47}, "src/bot/pipeline/commands/ask/cleanup/dead_code_analyzer.py": {"total_lines": 445, "code_lines": 337, "comment_lines": 36, "blank_lines": 72, "function_count": 22, "class_count": 3, "import_count": 9, "complexity_score": 54, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/stages/query_analyzer.py": {"total_lines": 131, "code_lines": 101, "comment_lines": 6, "blank_lines": 24, "function_count": 1, "class_count": 4, "import_count": 7, "complexity_score": 8, "maintainability_index": 99.27000000000001}, "src/bot/pipeline/commands/ask/stages/intent_detector.py": {"total_lines": 276, "code_lines": 207, "comment_lines": 19, "blank_lines": 50, "function_count": 9, "class_count": 3, "import_count": 9, "complexity_score": 19, "maintainability_index": 8.719999999999999}, "src/bot/pipeline/commands/ask/stages/data_collector.py": {"total_lines": 274, "code_lines": 200, "comment_lines": 20, "blank_lines": 54, "function_count": 2, "class_count": 1, "import_count": 7, "complexity_score": 38, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/stages/analysis_components.py": {"total_lines": 293, "code_lines": 222, "comment_lines": 16, "blank_lines": 55, "function_count": 5, "class_count": 3, "import_count": 8, "complexity_score": 31, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/stages/simplified_tool_orchestrator.py": {"total_lines": 381, "code_lines": 289, "comment_lines": 24, "blank_lines": 68, "function_count": 7, "class_count": 1, "import_count": 9, "complexity_score": 29, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/stages/__init__.py": {"total_lines": 33, "code_lines": 27, "comment_lines": 0, "blank_lines": 6, "function_count": 0, "class_count": 0, "import_count": 4, "complexity_score": 1, "maintainability_index": 158.21}, "src/bot/pipeline/commands/ask/stages/formatter.py": {"total_lines": 333, "code_lines": 240, "comment_lines": 30, "blank_lines": 63, "function_count": 9, "class_count": 2, "import_count": 8, "complexity_score": 48, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/stages/ai_synthesizer.py": {"total_lines": 261, "code_lines": 188, "comment_lines": 21, "blank_lines": 52, "function_count": 4, "class_count": 1, "import_count": 4, "complexity_score": 36, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/stages/response_generator.py": {"total_lines": 411, "code_lines": 316, "comment_lines": 27, "blank_lines": 68, "function_count": 8, "class_count": 3, "import_count": 13, "complexity_score": 35, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/tests/test_response_generator.py": {"total_lines": 347, "code_lines": 276, "comment_lines": 5, "blank_lines": 66, "function_count": 5, "class_count": 1, "import_count": 9, "complexity_score": 14, "maintainability_index": 18.39}, "src/bot/pipeline/commands/ask/tests/test_intent_detector.py": {"total_lines": 243, "code_lines": 183, "comment_lines": 17, "blank_lines": 43, "function_count": 5, "class_count": 1, "import_count": 7, "complexity_score": 20, "maintainability_index": 11.11}, "src/bot/pipeline/commands/ask/tests/__init__.py": {"total_lines": 22, "code_lines": 15, "comment_lines": 2, "blank_lines": 5, "function_count": 0, "class_count": 0, "import_count": 0, "complexity_score": 1, "maintainability_index": 160.74}, "src/bot/pipeline/commands/ask/tests/test_error_handling.py": {"total_lines": 444, "code_lines": 353, "comment_lines": 14, "blank_lines": 77, "function_count": 21, "class_count": 4, "import_count": 7, "complexity_score": 10, "maintainability_index": 16.879999999999995}, "src/bot/pipeline/commands/ask/tests/test_tool_orchestrator.py": {"total_lines": 317, "code_lines": 248, "comment_lines": 17, "blank_lines": 52, "function_count": 5, "class_count": 1, "import_count": 8, "complexity_score": 16, "maintainability_index": 14.89}, "src/bot/pipeline/commands/ask/deployment/documentation.py": {"total_lines": 752, "code_lines": 438, "comment_lines": 95, "blank_lines": 219, "function_count": 13, "class_count": 6, "import_count": 10, "complexity_score": 22, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/deployment/cicd_pipeline.py": {"total_lines": 648, "code_lines": 533, "comment_lines": 24, "blank_lines": 91, "function_count": 14, "class_count": 8, "import_count": 12, "complexity_score": 34, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/deployment/__init__.py": {"total_lines": 104, "code_lines": 91, "comment_lines": 3, "blank_lines": 10, "function_count": 0, "class_count": 0, "import_count": 3, "complexity_score": 1, "maintainability_index": 141.88}, "src/bot/pipeline/commands/ask/deployment/monitoring.py": {"total_lines": 704, "code_lines": 586, "comment_lines": 18, "blank_lines": 100, "function_count": 29, "class_count": 11, "import_count": 11, "complexity_score": 43, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/core/controller.py": {"total_lines": 358, "code_lines": 284, "comment_lines": 26, "blank_lines": 48, "function_count": 4, "class_count": 2, "import_count": 13, "complexity_score": 13, "maintainability_index": 21.059999999999988}, "src/bot/pipeline/commands/ask/core/stage_executor.py": {"total_lines": 471, "code_lines": 420, "comment_lines": 13, "blank_lines": 38, "function_count": 2, "class_count": 1, "import_count": 11, "complexity_score": 37, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/core/__init__.py": {"total_lines": 27, "code_lines": 22, "comment_lines": 0, "blank_lines": 5, "function_count": 0, "class_count": 0, "import_count": 4, "complexity_score": 1, "maintainability_index": 159.59}, "src/bot/pipeline/commands/ask/core/error_coordinator.py": {"total_lines": 418, "code_lines": 346, "comment_lines": 16, "blank_lines": 56, "function_count": 4, "class_count": 2, "import_count": 15, "complexity_score": 12, "maintainability_index": 12.459999999999994}, "src/bot/pipeline/commands/ask/core/stage_manager.py": {"total_lines": 120, "code_lines": 94, "comment_lines": 6, "blank_lines": 20, "function_count": 1, "class_count": 2, "import_count": 9, "complexity_score": 5, "maintainability_index": 117.4}, "src/bot/pipeline/commands/ask/observability/metrics.py": {"total_lines": 587, "code_lines": 474, "comment_lines": 30, "blank_lines": 83, "function_count": 24, "class_count": 6, "import_count": 10, "complexity_score": 39, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/observability/logger.py": {"total_lines": 472, "code_lines": 349, "comment_lines": 40, "blank_lines": 83, "function_count": 24, "class_count": 6, "import_count": 13, "complexity_score": 41, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/observability/log_analyzer.py": {"total_lines": 561, "code_lines": 442, "comment_lines": 39, "blank_lines": 80, "function_count": 17, "class_count": 9, "import_count": 10, "complexity_score": 76, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/observability/tracer.py": {"total_lines": 600, "code_lines": 472, "comment_lines": 31, "blank_lines": 97, "function_count": 28, "class_count": 7, "import_count": 10, "complexity_score": 65, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/observability/__init__.py": {"total_lines": 134, "code_lines": 112, "comment_lines": 5, "blank_lines": 17, "function_count": 0, "class_count": 0, "import_count": 5, "complexity_score": 1, "maintainability_index": 134.98000000000002}, "src/bot/pipeline/commands/ask/observability/health_checker.py": {"total_lines": 607, "code_lines": 464, "comment_lines": 49, "blank_lines": 94, "function_count": 8, "class_count": 5, "import_count": 10, "complexity_score": 44, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/architecture/service_registry.py": {"total_lines": 509, "code_lines": 417, "comment_lines": 19, "blank_lines": 73, "function_count": 18, "class_count": 6, "import_count": 12, "complexity_score": 48, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/architecture/event_bus.py": {"total_lines": 436, "code_lines": 345, "comment_lines": 17, "blank_lines": 74, "function_count": 14, "class_count": 6, "import_count": 9, "complexity_score": 45, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/architecture/__init__.py": {"total_lines": 64, "code_lines": 54, "comment_lines": 2, "blank_lines": 8, "function_count": 0, "class_count": 0, "import_count": 2, "complexity_score": 1, "maintainability_index": 151.08}, "src/bot/pipeline/commands/ask/cache/intelligent_cache.py": {"total_lines": 359, "code_lines": 269, "comment_lines": 24, "blank_lines": 66, "function_count": 18, "class_count": 3, "import_count": 10, "complexity_score": 34, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/cache/unified_cache.py": {"total_lines": 564, "code_lines": 413, "comment_lines": 43, "blank_lines": 108, "function_count": 7, "class_count": 4, "import_count": 13, "complexity_score": 72, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/cache/__init__.py": {"total_lines": 36, "code_lines": 25, "comment_lines": 4, "blank_lines": 7, "function_count": 0, "class_count": 0, "import_count": 2, "complexity_score": 1, "maintainability_index": 157.52}, "src/bot/pipeline/commands/ask/testing/test_infrastructure.py": {"total_lines": 455, "code_lines": 364, "comment_lines": 14, "blank_lines": 77, "function_count": 27, "class_count": 8, "import_count": 13, "complexity_score": 19, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/testing/unit_tests.py": {"total_lines": 538, "code_lines": 368, "comment_lines": 35, "blank_lines": 135, "function_count": 7, "class_count": 5, "import_count": 27, "complexity_score": 25, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/testing/__init__.py": {"total_lines": 132, "code_lines": 113, "comment_lines": 5, "blank_lines": 14, "function_count": 0, "class_count": 0, "import_count": 5, "complexity_score": 1, "maintainability_index": 135.44}, "src/bot/pipeline/commands/ask/testing/integration_tests.py": {"total_lines": 512, "code_lines": 346, "comment_lines": 47, "blank_lines": 119, "function_count": 2, "class_count": 6, "import_count": 20, "complexity_score": 25, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/testing/security_tests.py": {"total_lines": 548, "code_lines": 414, "comment_lines": 27, "blank_lines": 107, "function_count": 5, "class_count": 7, "import_count": 11, "complexity_score": 50, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/testing/performance_tests.py": {"total_lines": 592, "code_lines": 423, "comment_lines": 31, "blank_lines": 138, "function_count": 5, "class_count": 6, "import_count": 22, "complexity_score": 19, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/quality/documentation.py": {"total_lines": 418, "code_lines": 310, "comment_lines": 32, "blank_lines": 76, "function_count": 20, "class_count": 5, "import_count": 7, "complexity_score": 42, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/quality/code_standards.py": {"total_lines": 536, "code_lines": 435, "comment_lines": 22, "blank_lines": 79, "function_count": 25, "class_count": 6, "import_count": 8, "complexity_score": 42, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/quality/type_safety.py": {"total_lines": 355, "code_lines": 260, "comment_lines": 26, "blank_lines": 69, "function_count": 20, "class_count": 5, "import_count": 7, "complexity_score": 73, "maintainability_index": 0}, "src/bot/pipeline/commands/ask/quality/__init__.py": {"total_lines": 91, "code_lines": 78, "comment_lines": 3, "blank_lines": 10, "function_count": 0, "class_count": 0, "import_count": 3, "complexity_score": 1, "maintainability_index": 144.87}, "src/shared/ai_services/query_cache.py": {"total_lines": 171, "code_lines": 133, "comment_lines": 7, "blank_lines": 31, "function_count": 6, "class_count": 2, "import_count": 7, "complexity_score": 17, "maintainability_index": 43.269999999999996}, "src/shared/ai_services/enhanced_symbol_extractor.py": {"total_lines": 477, "code_lines": 369, "comment_lines": 35, "blank_lines": 73, "function_count": 4, "class_count": 3, "import_count": 10, "complexity_score": 55, "maintainability_index": 0}, "src/shared/ai_services/response_synthesizer.py": {"total_lines": 320, "code_lines": 248, "comment_lines": 17, "blank_lines": 55, "function_count": 13, "class_count": 1, "import_count": 4, "complexity_score": 33, "maintainability_index": 0}, "src/shared/ai_services/ai_processor_robust.py": {"total_lines": 1692, "code_lines": 1227, "comment_lines": 120, "blank_lines": 345, "function_count": 44, "class_count": 13, "import_count": 30, "complexity_score": 188, "maintainability_index": 0}, "src/shared/ai_services/cross_validation_ai.py": {"total_lines": 453, "code_lines": 332, "comment_lines": 35, "blank_lines": 86, "function_count": 13, "class_count": 5, "import_count": 10, "complexity_score": 49, "maintainability_index": 0}, "src/shared/ai_services/fact_verifier.py": {"total_lines": 291, "code_lines": 195, "comment_lines": 33, "blank_lines": 63, "function_count": 9, "class_count": 3, "import_count": 5, "complexity_score": 35, "maintainability_index": 0}, "src/shared/ai_services/tool_registry.py": {"total_lines": 383, "code_lines": 311, "comment_lines": 15, "blank_lines": 57, "function_count": 1, "class_count": 1, "import_count": 14, "complexity_score": 24, "maintainability_index": 0}, "src/shared/ai_services/local_fallback_ai.py": {"total_lines": 357, "code_lines": 247, "comment_lines": 41, "blank_lines": 69, "function_count": 12, "class_count": 2, "import_count": 4, "complexity_score": 55, "maintainability_index": 0}, "src/shared/ai_services/rate_limit_handler.py": {"total_lines": 320, "code_lines": 220, "comment_lines": 35, "blank_lines": 65, "function_count": 9, "class_count": 4, "import_count": 8, "complexity_score": 29, "maintainability_index": 0}, "src/shared/ai_services/timeout_manager.py": {"total_lines": 100, "code_lines": 74, "comment_lines": 8, "blank_lines": 18, "function_count": 4, "class_count": 2, "import_count": 5, "complexity_score": 12, "maintainability_index": 85.6}, "src/shared/ai_services/ai_tool_registry.py": {"total_lines": 406, "code_lines": 356, "comment_lines": 7, "blank_lines": 43, "function_count": 11, "class_count": 3, "import_count": 6, "complexity_score": 11, "maintainability_index": 20.419999999999987}, "src/shared/ai_services/intelligent_text_parser.py": {"total_lines": 603, "code_lines": 474, "comment_lines": 37, "blank_lines": 92, "function_count": 6, "class_count": 3, "import_count": 11, "complexity_score": 80, "maintainability_index": 0}, "src/shared/ai_services/ai_chat_processor.py": {"total_lines": 143, "code_lines": 106, "comment_lines": 10, "blank_lines": 27, "function_count": 4, "class_count": 1, "import_count": 5, "complexity_score": 7, "maintainability_index": 101.71}, "src/shared/ai_services/circuit_breaker.py": {"total_lines": 160, "code_lines": 120, "comment_lines": 11, "blank_lines": 29, "function_count": 9, "class_count": 4, "import_count": 7, "complexity_score": 17, "maintainability_index": 45.79999999999999}, "src/shared/ai_services/enhanced_intent_detector.py": {"total_lines": 373, "code_lines": 289, "comment_lines": 25, "blank_lines": 59, "function_count": 4, "class_count": 5, "import_count": 11, "complexity_score": 35, "maintainability_index": 0}, "src/shared/ai_services/ai_security_detector.py": {"total_lines": 338, "code_lines": 269, "comment_lines": 16, "blank_lines": 53, "function_count": 4, "class_count": 4, "import_count": 11, "complexity_score": 21, "maintainability_index": 0}, "src/shared/ai_services/simple_model_config.py": {"total_lines": 166, "code_lines": 129, "comment_lines": 5, "blank_lines": 32, "function_count": 13, "class_count": 2, "import_count": 6, "complexity_score": 11, "maintainability_index": 75.62}, "src/shared/ai_services/fast_price_lookup.py": {"total_lines": 147, "code_lines": 103, "comment_lines": 11, "blank_lines": 33, "function_count": 2, "class_count": 1, "import_count": 8, "complexity_score": 16, "maintainability_index": 53.989999999999995}, "src/shared/ai_services/unified_ai_processor.py": {"total_lines": 499, "code_lines": 370, "comment_lines": 39, "blank_lines": 90, "function_count": 11, "class_count": 5, "import_count": 18, "complexity_score": 30, "maintainability_index": 0}, "src/shared/ai_services/simple_query_analyzer.py": {"total_lines": 233, "code_lines": 174, "comment_lines": 21, "blank_lines": 38, "function_count": 5, "class_count": 5, "import_count": 5, "complexity_score": 21, "maintainability_index": 8.209999999999994}, "src/shared/ai_services/smart_model_router.py": {"total_lines": 680, "code_lines": 510, "comment_lines": 56, "blank_lines": 114, "function_count": 42, "class_count": 5, "import_count": 13, "complexity_score": 61, "maintainability_index": 0}, "src/shared/ai_services/query_router.py": {"total_lines": 385, "code_lines": 303, "comment_lines": 24, "blank_lines": 58, "function_count": 11, "class_count": 4, "import_count": 5, "complexity_score": 29, "maintainability_index": 0}, "src/shared/ai_services/__init__.py": {"total_lines": 4, "code_lines": 3, "comment_lines": 0, "blank_lines": 1, "function_count": 0, "class_count": 0, "import_count": 0, "complexity_score": 1, "maintainability_index": 164.88000000000002}, "src/shared/ai_services/enhanced_ai_client.py": {"total_lines": 321, "code_lines": 251, "comment_lines": 15, "blank_lines": 55, "function_count": 4, "class_count": 1, "import_count": 10, "complexity_score": 16, "maintainability_index": 13.969999999999999}, "src/shared/ai_services/ai_service_wrapper.py": {"total_lines": 295, "code_lines": 223, "comment_lines": 20, "blank_lines": 52, "function_count": 4, "class_count": 1, "import_count": 16, "complexity_score": 28, "maintainability_index": 0}, "src/shared/ai_services/openrouter_key.py": {"total_lines": 9, "code_lines": 6, "comment_lines": 1, "blank_lines": 2, "function_count": 0, "class_count": 0, "import_count": 1, "complexity_score": 1, "maintainability_index": 163.73000000000002}, "src/shared/ai_services/anti_hallucination_prompt.py": {"total_lines": 88, "code_lines": 68, "comment_lines": 2, "blank_lines": 18, "function_count": 1, "class_count": 0, "import_count": 0, "complexity_score": 10, "maintainability_index": 98.75999999999999}, "src/shared/ai_chat/data_fetcher.py": {"total_lines": 356, "code_lines": 270, "comment_lines": 23, "blank_lines": 63, "function_count": 2, "class_count": 1, "import_count": 8, "complexity_score": 54, "maintainability_index": 0}, "src/shared/ai_chat/config.py": {"total_lines": 56, "code_lines": 47, "comment_lines": 5, "blank_lines": 4, "function_count": 1, "class_count": 1, "import_count": 3, "complexity_score": 4, "maintainability_index": 137.32}, "src/shared/ai_chat/models.py": {"total_lines": 36, "code_lines": 24, "comment_lines": 5, "blank_lines": 7, "function_count": 2, "class_count": 1, "import_count": 2, "complexity_score": 4, "maintainability_index": 141.92}, "src/shared/ai_chat/fallbacks.py": {"total_lines": 44, "code_lines": 35, "comment_lines": 0, "blank_lines": 9, "function_count": 3, "class_count": 2, "import_count": 2, "complexity_score": 1, "maintainability_index": 155.68}, "src/shared/ai_chat/__init__.py": {"total_lines": 37, "code_lines": 29, "comment_lines": 2, "blank_lines": 6, "function_count": 0, "class_count": 0, "import_count": 6, "complexity_score": 1, "maintainability_index": 157.29000000000002}, "src/shared/ai_chat/response_formatter.py": {"total_lines": 400, "code_lines": 293, "comment_lines": 38, "blank_lines": 69, "function_count": 14, "class_count": 1, "import_count": 6, "complexity_score": 71, "maintainability_index": 0}, "src/shared/ai_chat/ai_client.py": {"total_lines": 494, "code_lines": 385, "comment_lines": 37, "blank_lines": 72, "function_count": 10, "class_count": 5, "import_count": 21, "complexity_score": 52, "maintainability_index": 0}, "src/shared/ai_chat/processor.py": {"total_lines": 189, "code_lines": 154, "comment_lines": 17, "blank_lines": 18, "function_count": 1, "class_count": 1, "import_count": 4, "complexity_score": 15, "maintainability_index": 49.53}}}