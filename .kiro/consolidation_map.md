# Consolidation Map (Dry-Run Plan)

Root: /home/<USER>/Desktop/tradingview-automatio

## Summary

- tests: 7 locations
- config: 5 locations
- logs: 9 locations
- data: 6 locations
- utils: 6 locations
- security: 4 locations
- duplicate filenames tracked: 3 kinds

## Canonical Targets

- tests: `tests/`
- config: `config/`
- logs: `logs/`
- data: `data/`
- utils: `src/shared/utils/`
- security: `src/security/`
- docs_root: `documentation/`

## Tests Directories (7)

- documentation/testing (1 files)
- src/bot/pipeline/commands/ask/testing (6 files)
- src/bot/pipeline/commands/ask/tests (10 files)
- test_results (38 files)
- tests (500 files)
- tests/test_results (5 files)
- tradingview-ingest/tests (6 files)

### Proposed Dry-Run Commands

```bash
# tests: test_results
mkdir -p tests/
rsync -a --dry-run --info=NAME test_results/ tests/
# tests: src/bot/pipeline/commands/ask/testing
mkdir -p tests/
rsync -a --dry-run --info=NAME src/bot/pipeline/commands/ask/testing/ tests/
# tests: tests/test_results
mkdir -p tests/
rsync -a --dry-run --info=NAME tests/test_results/ tests/
# tests: documentation/testing
mkdir -p tests/
rsync -a --dry-run --info=NAME documentation/testing/ tests/
```

## Config Directories (5)

- config (7 files)
- src/bot/pipeline/commands/ask/config (10 files)
- src/shared/config (3 files)
- src/shared/configuration (6 files)
- tradingview-ingest/config (1 files)

### Proposed Dry-Run Commands

```bash
# config: src/shared/configuration
mkdir -p config/
rsync -a --dry-run --info=NAME src/shared/configuration/ config/
```

## Logs Directories (9)

- logs (229 files)
- nginx/logs (2 files)
- postgres/logs (0 files)
- redis/logs (0 files)
- scripts/logs (3 files)
- src/bot/pipeline/logs (1 files)
- src/logs (2 files)
- tests/logs (7 files)
- tradingview-ingest/logs (1 files)

## Data Directories (6)

- data (29 files)
- scripts/data (1 files)
- src/api/data (49 files)
- src/bot/pipeline/data (1 files)
- src/data (15 files)
- tests/data (0 files)

## Utils Directories (6)

- src/analysis/utils (2 files)
- src/bot/pipeline/utils (9 files)
- src/bot/utils (16 files)
- src/core/prompts/utils (8 files)
- src/shared/utils (14 files)
- src/utils (1 files)

### Proposed Dry-Run Commands

```bash
# utils: src/utils
mkdir -p src/shared/utils/
rsync -a --dry-run --info=NAME src/utils/ src/shared/utils/
# utils: src/bot/utils
mkdir -p src/shared/utils/
rsync -a --dry-run --info=NAME src/bot/utils/ src/shared/utils/
# utils: src/bot/pipeline/utils
mkdir -p src/shared/utils/
rsync -a --dry-run --info=NAME src/bot/pipeline/utils/ src/shared/utils/
# utils: src/analysis/utils
mkdir -p src/shared/utils/
rsync -a --dry-run --info=NAME src/analysis/utils/ src/shared/utils/
# utils: src/core/prompts/utils
mkdir -p src/shared/utils/
rsync -a --dry-run --info=NAME src/core/prompts/utils/ src/shared/utils/
```

## Security Directories (4)

- documentation/security (3 files)
- src/bot/pipeline/commands/ask/security (19 files)
- src/bot/security (4 files)
- src/security (4 files)

### Proposed Dry-Run Commands

```bash
# security: src/bot/security
mkdir -p src/security/
rsync -a --dry-run --info=NAME src/bot/security/ src/security/
# security: src/bot/pipeline/commands/ask/security
mkdir -p src/security/
rsync -a --dry-run --info=NAME src/bot/pipeline/commands/ask/security/ src/security/
# security: documentation/security
mkdir -p src/security/
rsync -a --dry-run --info=NAME documentation/security/ src/security/
```

## Duplicate/Suspect Files

### ngrok.yml (2)

- ngrok.yml
- tradingview-ingest/ngrok.yml
- Recommendation: keep a single canonical config in `config/` and remove others.

### tasks.md (3)

- .kiro/specs/ask-command-audit/tasks.md
- documentation/archive/tasks.md
- tasks.md
- Recommendation: consolidate into `documentation/tasks/`.

### webhook.conf (2)

- nginx/webhook.conf
- tradingview-ingest/nginx/webhook.conf
- Recommendation: merge into `config/webhook/` with env-specific files.

## Root Documentation Files

Found 25 files in repository root:

- AI_MODEL_CONFIGURATION_AUDIT.md
- AI_RELIABILITY_IMPROVEMENTS.md
- ALPHA_VANTAGE_INTEGRATION_GUIDE.md
- ANSWER_QUALITY_AUDIT_SUMMARY.md
- ASK_PIPELINE_AUDIT_REPORT.md
- CLEANUP_SUMMARY.md
- COMPREHENSIVE_UPGRADE_REPORT.md
- DOCKER_BUILD_OPTIMIZATION.md
- MCP_AI_PLANNING_AUDIT_REPORT.md
- MCP_ARCHITECTURE_TRANSFORMATION_SUMMARY.md
- MCP_DOCKER_SETUP.md
- MCP_ECOSYSTEM_SUMMARY.md
- MCP_IMPLEMENTATION_SUCCESS_REPORT.md
- MCP_SERVER_IMPLEMENTATION_SUMMARY.md
- MODEL_STRATEGY_AUDIT.md
- NEW_MODELS_INTEGRATION.md
- OFFICIAL_ALPHAVANTAGE_MCP_SETUP.md
- PROJECT_OVERVIEW.md
- README.md
- REGEX_VS_AI_ANALYSIS.md
- data_provider_quality_analysis.md
- final_quality_report.md
- quality_analysis_report.md
- symbol_extraction_improvements.md
- tasks.md

Recommendation: move to `documentation/` (subfolders: `audit/`, `guides/`, `design/`).

## Risk Assessment and Approach

- Use dry-run moves first; review results.

- Add __init__.py re-export shims for any Python module moves to preserve imports.

- Update CI/test paths after tests/ consolidation.

- Maintain symlinks or log routing for logs/ consolidation initially.
