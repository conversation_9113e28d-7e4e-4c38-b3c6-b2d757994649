#!/usr/bin/env python3
"""
Test MCP Server Tool Execution

Tests actual tool execution in our trading MCP server to validate functionality.
"""

import asyncio
import sys
import os
import json
sys.path.append('.')

async def test_mcp_server_tools():
    """Test MCP server tool execution"""
    print("🧪 Testing MCP Server Tool Execution")
    print("=" * 50)
    
    try:
        # Import MCP server
        from src.mcp_server.trading_mcp_server import TradingMCPServer
        
        print("📦 Creating MCP server instance...")
        server = TradingMCPServer()
        print("✅ MCP server created successfully")
        
        # Test each tool individually
        test_results = {}
        
        # Test 1: Stock Price Tool
        print("\n💰 Testing Stock Price Tool...")
        try:
            result = await server._get_stock_price("AAPL", include_extended=True)
            test_results["get_stock_price"] = {
                "success": "error" not in result,
                "data": result
            }
            if "error" not in result:
                print(f"✅ Stock price retrieved: ${result.get('price', 'N/A')}")
            else:
                print(f"❌ Stock price failed: {result['error']}")
        except Exception as e:
            test_results["get_stock_price"] = {"success": False, "error": str(e)}
            print(f"❌ Stock price exception: {e}")
        
        # Test 2: Technical Analysis Tool
        print("\n📊 Testing Technical Analysis Tool...")
        try:
            result = await server._get_technical_analysis("TSLA", "1mo", ["rsi", "macd", "sma"])
            test_results["get_technical_analysis"] = {
                "success": "error" not in result,
                "data": result
            }
            if "error" not in result:
                print(f"✅ Technical analysis completed for {result.get('symbol', 'N/A')}")
            else:
                print(f"❌ Technical analysis failed: {result['error']}")
        except Exception as e:
            test_results["get_technical_analysis"] = {"success": False, "error": str(e)}
            print(f"❌ Technical analysis exception: {e}")
        
        # Test 3: Market Sentiment Tool
        print("\n📈 Testing Market Sentiment Tool...")
        try:
            result = await server._analyze_market_sentiment(["SPY", "QQQ", "IWM"], include_vix=True)
            test_results["analyze_market_sentiment"] = {
                "success": "error" not in result,
                "data": result
            }
            if "error" not in result:
                print(f"✅ Market sentiment: {result.get('overall_sentiment', 'N/A')}")
            else:
                print(f"❌ Market sentiment failed: {result['error']}")
        except Exception as e:
            test_results["analyze_market_sentiment"] = {"success": False, "error": str(e)}
            print(f"❌ Market sentiment exception: {e}")
        
        # Test 4: Intent Detection Tool
        print("\n🎯 Testing Intent Detection Tool...")
        try:
            result = await server._detect_trading_intent("What's the price of Apple stock?", {})
            test_results["detect_trading_intent"] = {
                "success": "error" not in result,
                "data": result
            }
            if "error" not in result:
                print(f"✅ Intent detected: {result.get('intent', 'N/A')} (confidence: {result.get('confidence', 0):.2f})")
            else:
                print(f"❌ Intent detection failed: {result['error']}")
        except Exception as e:
            test_results["detect_trading_intent"] = {"success": False, "error": str(e)}
            print(f"❌ Intent detection exception: {e}")
        
        # Test 5: Options Data Tool
        print("\n📋 Testing Options Data Tool...")
        try:
            result = await server._get_options_data("MSFT", "next_friday", "both")
            test_results["get_options_data"] = {
                "success": "error" not in result,
                "data": result
            }
            if "error" not in result:
                calls_count = len(result.get("options_chain", {}).get("calls", []))
                puts_count = len(result.get("options_chain", {}).get("puts", []))
                print(f"✅ Options data retrieved: {calls_count} calls, {puts_count} puts")
            else:
                print(f"❌ Options data failed: {result['error']}")
        except Exception as e:
            test_results["get_options_data"] = {"success": False, "error": str(e)}
            print(f"❌ Options data exception: {e}")
        
        # Test 6: Risk Metrics Tool
        print("\n⚠️ Testing Risk Metrics Tool...")
        try:
            positions = [
                {"symbol": "AAPL", "quantity": 100, "entry_price": 150.0},
                {"symbol": "TSLA", "quantity": 50, "entry_price": 200.0}
            ]
            result = await server._calculate_risk_metrics(positions, 50000.0, 0.05)
            test_results["calculate_risk_metrics"] = {
                "success": "error" not in result,
                "data": result
            }
            if "error" not in result:
                sharpe = result.get("sharpe_ratio", 0)
                volatility = result.get("portfolio_volatility", 0)
                print(f"✅ Risk metrics calculated: Sharpe {sharpe:.2f}, Volatility {volatility:.2f}")
            else:
                print(f"❌ Risk metrics failed: {result['error']}")
        except Exception as e:
            test_results["calculate_risk_metrics"] = {"success": False, "error": str(e)}
            print(f"❌ Risk metrics exception: {e}")
        
        # Summary
        print("\n" + "=" * 50)
        print("🏆 MCP SERVER TOOL EXECUTION SUMMARY")
        print("=" * 50)
        
        successful_tools = [tool for tool, result in test_results.items() if result.get("success", False)]
        failed_tools = [tool for tool, result in test_results.items() if not result.get("success", False)]
        
        print(f"✅ Successful Tools: {len(successful_tools)}/6")
        for tool in successful_tools:
            print(f"   ✅ {tool}")
        
        if failed_tools:
            print(f"\n❌ Failed Tools: {len(failed_tools)}/6")
            for tool in failed_tools:
                error = test_results[tool].get("error", "Unknown error")
                print(f"   ❌ {tool}: {error}")
        
        success_rate = len(successful_tools) / len(test_results) * 100
        print(f"\n📊 Overall Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("🎉 MCP server tools are working excellently!")
        elif success_rate >= 60:
            print("✅ MCP server tools are working well with minor issues")
        else:
            print("⚠️ MCP server tools need attention")
        
        return test_results
        
    except Exception as e:
        print(f"❌ MCP server test failed: {e}")
        import traceback
        traceback.print_exc()
        return {}

async def test_mcp_integration_with_ask_pipeline():
    """Test MCP integration with ASK pipeline"""
    print("\n🔗 Testing MCP Integration with ASK Pipeline")
    print("=" * 50)
    
    try:
        # Test ASK pipeline with MCP tools
        from src.bot.pipeline.commands.ask.executor import execute_ask_pipeline
        
        test_queries = [
            "What's the price of Apple stock?",
            "Analyze Tesla technical indicators",
            "How is the market sentiment today?",
            "Show me options for Microsoft"
        ]
        
        integration_results = {}
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n🧪 Test {i}: '{query}'")
            try:
                result = await execute_ask_pipeline(
                    query=query,
                    user_id="test_user_mcp",
                    username="MCPTestUser",
                    correlation_id=f"mcp_test_{i}"
                )
                
                integration_results[f"test_{i}"] = {
                    "query": query,
                    "success": result.success,
                    "intent": result.intent,
                    "tools_used": result.tools_used,
                    "execution_time": result.execution_time
                }
                
                if result.success:
                    print(f"✅ Pipeline executed successfully")
                    print(f"   Intent: {result.intent}")
                    print(f"   Tools: {', '.join(result.tools_used) if result.tools_used else 'None'}")
                    print(f"   Time: {result.execution_time:.2f}s")
                else:
                    print(f"❌ Pipeline failed: {result.error}")
                
            except Exception as e:
                integration_results[f"test_{i}"] = {
                    "query": query,
                    "success": False,
                    "error": str(e)
                }
                print(f"❌ Pipeline exception: {e}")
        
        # Integration summary
        successful_tests = [test for test, result in integration_results.items() if result.get("success", False)]
        
        print(f"\n📊 Integration Test Results: {len(successful_tests)}/{len(test_queries)} successful")
        
        if len(successful_tests) >= 3:
            print("🎉 MCP integration with ASK pipeline is working!")
        else:
            print("⚠️ MCP integration needs improvement")
        
        return integration_results
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return {}

if __name__ == "__main__":
    print("🧪 MCP Server Tool Execution Test Suite")
    print("=" * 60)
    
    async def run_all_tests():
        # Test MCP server tools
        tool_results = await test_mcp_server_tools()
        
        # Test integration with ASK pipeline
        integration_results = await test_mcp_integration_with_ask_pipeline()
        
        print("\n" + "=" * 60)
        print("🏆 COMPLETE MCP SERVER TEST SUMMARY")
        print("=" * 60)
        
        tool_success_rate = len([r for r in tool_results.values() if r.get("success", False)]) / max(len(tool_results), 1) * 100
        integration_success_rate = len([r for r in integration_results.values() if r.get("success", False)]) / max(len(integration_results), 1) * 100
        
        print(f"🛠️  Tool Execution Success Rate: {tool_success_rate:.1f}%")
        print(f"🔗 Pipeline Integration Success Rate: {integration_success_rate:.1f}%")
        
        overall_success = (tool_success_rate + integration_success_rate) / 2
        print(f"🎯 Overall MCP Success Rate: {overall_success:.1f}%")
        
        if overall_success >= 80:
            print("\n🎉 MCP SERVER IS READY FOR PRODUCTION!")
            print("✅ All tools working correctly")
            print("✅ Integration with ASK pipeline successful")
            print("✅ Ready to replace current monolithic approach")
        elif overall_success >= 60:
            print("\n✅ MCP SERVER IS MOSTLY READY")
            print("🔧 Minor issues to address before production")
        else:
            print("\n⚠️ MCP SERVER NEEDS MORE WORK")
            print("🔧 Significant issues to resolve")
    
    asyncio.run(run_all_tests())
