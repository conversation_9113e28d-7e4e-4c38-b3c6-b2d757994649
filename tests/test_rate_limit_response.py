#!/usr/bin/env python3
"""
Test Rate Limit Response Formatting
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv
load_dotenv()

async def test_rate_limit_response():
    """Test the rate limit response formatting"""
    
    print("🧪 Testing Rate Limit Response Formatting...")
    
    try:
        # Import the synthesis method directly
        from src.bot.pipeline.commands.ask.stages.dynamic_tool_orchestrator import DynamicToolOrchestrator
        
        # Create orchestrator instance
        orchestrator = DynamicToolOrchestrator()
        
        # Simulate the exact tool results you showed
        tool_results = {
            "get_comprehensive_analysis": [{
                "success": True,
                "data": {
                    "quote": {
                        "content": [{
                            "type": "text", 
                            "text": '{\n "Information": "We have detected your API key as DDI08KAP03QTQT2B and our standard API rate limit is 25 requests per day. Please subscribe to any of the premium plans at https://www.alphavantage.co/premium/ to instantly remove all daily rate limits."\n}'
                        }]
                    },
                    "historical": {
                        "content": [{
                            "type": "text", 
                            "text": '{\n "Information": "We have detected your API key as DDI08KAP03QTQT2B and our standard API rate limit is 25 requests per day. Please subscribe to any of the premium plans at https://www.alphavantage.co/premium/ to instantly remove all daily rate limits."\n}'
                        }]
                    }
                }
            }]
        }
        
        # Test the synthesis
        query = "What is the current price of Apple stock?"
        result = await orchestrator._synthesize_answer(
            query=query,
            tool_results=tool_results,
            current_answer="",
            correlation_id="test"
        )
        
        print(f"\n📊 Synthesis Result:")
        print(f"   Query: {query}")
        print(f"   Response: {result}")
        print(f"   Length: {len(result)} characters")
        
        # Check if it detected rate limiting
        if "rate limit" in result.lower():
            print("✅ Rate limiting detected and handled properly")
            return True
        else:
            print("❌ Rate limiting not detected properly")
            return False
        
    except Exception as e:
        print(f"❌ Error testing rate limit response: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_rate_limit_response())
    print(f"\n🎯 Test Result: {'✅ Success' if success else '❌ Failed'}")
    sys.exit(0 if success else 1)
