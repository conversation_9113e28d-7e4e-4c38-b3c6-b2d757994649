#!/usr/bin/env python3
"""
Mock Alpha Vantage MCP Server Test
Tests with mock data when rate limits are hit
"""

import asyncio
import json
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

async def test_mock_alphavantage():
    """Test with mock Alpha Vantage data"""
    
    print("🧪 Testing Mock Alpha Vantage MCP Server...")
    
    # Mock data for testing
    mock_quote = {
        "Global Quote": {
            "01. symbol": "AAPL",
            "02. open": "175.00",
            "03. high": "178.50",
            "04. low": "174.20",
            "05. price": "177.25",
            "06. volume": "45000000",
            "07. latest trading day": "2024-09-21",
            "08. previous close": "175.80",
            "09. change": "1.45",
            "10. change percent": "0.82%"
        }
    }
    
    mock_rsi = {
        "Meta Data": {
            "1: Symbol": "AAPL",
            "2: Indicator": "Relative Strength Index (RSI)",
            "3: Last Refreshed": "2024-09-21",
            "4: Interval": "daily",
            "5: Time Period": 14,
            "6: Series Type": "close"
        },
        "Technical Analysis: RSI": {
            "2024-09-21": {
                "RSI": "65.42"
            },
            "2024-09-20": {
                "RSI": "63.18"
            }
        }
    }
    
    print("✅ Mock Data Loaded")
    print(f"📈 AAPL Quote: ${mock_quote['Global Quote']['05. price']} ({mock_quote['Global Quote']['10. change percent']})")
    print(f"📊 AAPL RSI: {mock_rsi['Technical Analysis: RSI']['2024-09-21']['RSI']}")
    
    # Test MCP client configuration
    try:
        from src.shared.mcp.mcp_client_config import mcp_config, MCPServerType
        
        print("\n📋 MCP Configuration:")
        alphavantage_server = mcp_config.get_server_by_type(MCPServerType.ALPHAVANTAGE)
        if alphavantage_server:
            print(f"  Server: {alphavantage_server.name}")
            print(f"  URL: {alphavantage_server.url}")
            print(f"  Enabled: {alphavantage_server.enabled}")
            print(f"  Tools: {len(alphavantage_server.tools)}")
            print(f"  Sample tools: {alphavantage_server.tools[:5]}")
        
        print("\n✅ Mock Alpha Vantage MCP Server test completed!")
        print("💡 Note: This uses mock data. Real data requires API calls.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_mock_alphavantage())
    sys.exit(0 if success else 1)
