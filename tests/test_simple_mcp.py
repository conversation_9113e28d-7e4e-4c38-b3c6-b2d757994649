#!/usr/bin/env python3
"""
Simple MCP Test
Test individual MCP servers directly
"""

import asyncio
import httpx
import json

async def test_mcp_server(port: int, name: str):
    """Test a single MCP server"""
    try:
        async with httpx.AsyncClient(timeout=5.0) as client:
            # Test health endpoint
            health_response = await client.get(f"http://localhost:{port}/health")
            print(f"✅ {name} (port {port}): Health check passed - {health_response.status_code}")
            
            # Test MCP tools/list
            mcp_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/list"
            }
            
            tools_response = await client.post(f"http://localhost:{port}/mcp", json=mcp_request)
            if tools_response.status_code == 200:
                tools_data = tools_response.json()
                tools = tools_data.get("result", {}).get("tools", [])
                print(f"   📋 Available tools: {len(tools)}")
                for tool in tools[:3]:  # Show first 3 tools
                    print(f"      - {tool.get('name', 'Unknown')}: {tool.get('description', 'No description')}")
            else:
                print(f"   ❌ Tools list failed: {tools_response.status_code}")
                
    except Exception as e:
        print(f"❌ {name} (port {port}): {e}")

async def main():
    """Test all MCP servers"""
    print("🧪 Testing MCP Servers...")
    
    servers = [
        (3001, "DuckDuckGo"),
        (3002, "ArXiv"), 
        (3004, "Fetch"),
        (3005, "Time")
    ]
    
    for port, name in servers:
        await test_mcp_server(port, name)
        print()

if __name__ == "__main__":
    asyncio.run(main())
