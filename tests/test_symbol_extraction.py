#!/usr/bin/env python3
"""
Test symbol extraction to see what's happening
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_symbol_extraction():
    """Test different symbol extraction methods"""
    
    test_queries = [
        "What is AAPL?",
        "Tell me about Apple stock",
        "I want to find a good stock to buy calls on next week",
        "Should I buy $TSLA?",
        "How is Microsoft doing?",
        "NVDA analysis please"
    ]
    
    print("🔍 SYMBOL EXTRACTION TEST")
    print("=" * 50)
    
    for query in test_queries:
        print(f"\n📝 Query: '{query}'")
        
        # Test 1: AI-powered extraction
        try:
            from src.shared.utils.symbol_extraction import extract_symbols_from_query
            ai_symbols = extract_symbols_from_query(query)
            print(f"   🤖 AI extraction: {ai_symbols}")
        except Exception as e:
            print(f"   ❌ AI extraction failed: {e}")
        
        # Test 2: Enhanced symbol extractor
        try:
            from src.shared.ai_services.enhanced_symbol_extractor import enhanced_symbol_extractor
            enhanced_results = await enhanced_symbol_extractor.extract_symbols_simple(query, use_ai=True)
            print(f"   🔧 Enhanced extraction: {enhanced_results}")
        except Exception as e:
            print(f"   ❌ Enhanced extraction failed: {e}")
        
        # Test 3: Unified symbol extractor
        try:
            from src.shared.utils.symbol_extraction import UnifiedSymbolExtractor
            unified_extractor = UnifiedSymbolExtractor(enable_ai=True, enable_validation=True)
            unified_symbols = unified_extractor.extract_symbols_simple(query)
            print(f"   🎯 Unified extraction: {unified_symbols}")
        except Exception as e:
            print(f"   ❌ Unified extraction failed: {e}")
        
        # Test 4: Basic regex (what was happening before)
        import re
        regex_symbols = re.findall(r'\b[A-Z]{2,5}\b', query.upper())
        print(f"   📝 Basic regex: {regex_symbols}")

if __name__ == "__main__":
    asyncio.run(test_symbol_extraction())
