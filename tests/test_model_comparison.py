#!/usr/bin/env python3
"""
Test different AI models for trading responses
"""

import asyncio
import sys
sys.path.insert(0, 'src')

async def test_models():
    """Test different AI models for trading knowledge"""
    from shared.ai_chat.ai_client import AIClientWrapper
    
    print("🧪 Testing Different AI Models for Trading Knowledge")
    print("=" * 70)
    
    # Test question
    question = "What are the support and resistance levels for AAPL? Provide specific price levels and technical analysis."
    
    # Models to test (from the config)
    models_to_test = [
        {
            "name": "Kimi K2 (Quick)",
            "model_id": "moonshotai/kimi-k2-0905",
            "description": "Fast model, good for simple tasks"
        },

        {
            "name": "Qwen Plus Thinking", 
            "model_id": "qwen/qwen-plus-2025-07-28:thinking", 
            "description": "Advanced reasoning model"
        },
        {
            "name": "DeepCogito V2",
            "model_id": "deepcogito/cogito-v2-preview-deepseek-671b",
            "description": "Financial analysis specialist"
        }
    ]
    
    for i, model_info in enumerate(models_to_test, 1):
        print(f"\n{'='*20} MODEL {i}/4: {model_info['name']} {'='*20}")
        print(f"Description: {model_info['description']}")
        print(f"Question: {question}")
        print("-" * 60)
        
        try:
            # Create AI client with specific model
            client = AIClientWrapper()
            client.model = model_info['model_id']
            client._initialize_client()
            
            # Test prompt
            prompt = f"""
You are a professional trading analyst. Answer this question with specific, actionable trading insights:

{question}

Provide:
1. Specific support and resistance price levels
2. Technical analysis reasoning
3. Current market context
4. Trading recommendations

Be specific, accurate, and professional.
"""
            
            print("🤖 Calling AI model...")
            response = await client.generate_response(prompt)
            
            if response:
                print(f"✅ Response Length: {len(response)} chars")
                print(f"📝 Response:\n{response}")
                
                # Quick quality check
                quality_indicators = [
                    "support", "resistance", "AAPL", "price", "technical", 
                    "analysis", "level", "trading", "market"
                ]
                quality_score = sum(1 for indicator in quality_indicators if indicator.lower() in response.lower())
                print(f"📊 Quality Score: {quality_score}/{len(quality_indicators)} indicators found")
                
            else:
                print("❌ No response received")
                
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print("\n" + "="*70)

if __name__ == "__main__":
    asyncio.run(test_models())
