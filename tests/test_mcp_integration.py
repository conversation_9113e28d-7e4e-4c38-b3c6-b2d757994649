#!/usr/bin/env python3
"""
Test Alpha Vantage MCP Integration
Tests the hybrid MCP provider and enhanced pipeline.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_mcp_client():
    """Test the Alpha Vantage MCP client directly."""
    print("🧪 Testing Alpha Vantage MCP Client...")
    
    try:
        from src.shared.data_providers.alpha_vantage_mcp import AlphaVantageMCPClient
        
        # Initialize client
        client = AlphaVantageMCPClient()
        
        if not client.is_configured:
            print("❌ MCP client not configured - API key missing")
            return False
        
        print("✅ MCP client initialized")
        
        # Test basic functionality
        print("\n📊 Testing global quote...")
        quote = await client.get_global_quote("AAPL")
        print(f"Quote result: {quote}")
        
        print("\n📈 Testing technical analysis...")
        technical = await client.get_technical_analysis("AAPL", ["RSI", "MACD"])
        print(f"Technical analysis: {technical}")
        
        print("\n📰 Testing news sentiment...")
        sentiment = await client.get_news_sentiment("AAPL")
        print(f"News sentiment: {sentiment}")
        
        await client.close()
        return True
        
    except Exception as e:
        print(f"❌ MCP client test failed: {e}")
        return False

async def test_hybrid_provider():
    """Test the hybrid MCP provider."""
    print("\n🧪 Testing Hybrid MCP Provider...")
    
    try:
        from src.shared.data_providers.hybrid_mcp_provider import HybridMCPProvider
        
        # Initialize provider
        provider = HybridMCPProvider()
        print("✅ Hybrid provider initialized")
        
        # Test current price
        print("\n💰 Testing current price...")
        price = await provider.get_current_price("AAPL")
        print(f"Price data: {price}")
        
        # Test historical data
        print("\n📊 Testing historical data...")
        hist = await provider.get_historical_data("AAPL", "1mo")
        print(f"Historical data success: {hist.get('success', False)}")
        
        # Test technical indicators
        print("\n📈 Testing technical indicators...")
        technical = await provider.get_technical_indicators("AAPL")
        print(f"Technical indicators success: {technical.get('success', False)}")
        
        # Test AI optimized data
        print("\n🤖 Testing AI optimized data...")
        ai_data = await provider.get_ai_optimized_data("AAPL")
        print(f"AI data keys: {list(ai_data.keys())}")
        
        await provider.close()
        return True
        
    except Exception as e:
        print(f"❌ Hybrid provider test failed: {e}")
        return False

async def test_enhanced_pipeline():
    """Test the enhanced MCP pipeline."""
    print("\n🧪 Testing Enhanced MCP Pipeline...")
    
    try:
        from src.bot.pipeline.commands.ask.enhanced_mcp_pipeline import MCPEnhancedAskPipeline
        
        # Initialize pipeline
        pipeline = MCPEnhancedAskPipeline()
        print("✅ Enhanced pipeline initialized")
        
        # Test queries
        test_queries = [
            "What's the current price of AAPL?",
            "Give me technical analysis for AAPL including RSI and MACD",
            "What's the news sentiment for AAPL?",
            "Analyze NVDA's performance and give me support/resistance levels"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n🔍 Test Query {i}: {query}")
            
            result = await pipeline.process_query(query)
            
            print(f"✅ Success: {result.success}")
            print(f"📊 MCP Used: {result.mcp_used}")
            print(f"🔄 Fallback Used: {result.fallback_used}")
            print(f"⭐ Data Quality: {result.data_quality:.2f}")
            print(f"⏱️ Processing Time: {result.processing_time:.2f}s")
            print(f"📝 Response Length: {len(result.response)} chars")
            
            if result.error:
                print(f"❌ Error: {result.error}")
            
            # Show first 200 chars of response
            print(f"💬 Response Preview: {result.response[:200]}...")
        
        await pipeline.close()
        return True
        
    except Exception as e:
        print(f"❌ Enhanced pipeline test failed: {e}")
        return False

async def test_comparison():
    """Compare MCP vs traditional approach."""
    print("\n🧪 Comparing MCP vs Traditional Approach...")
    
    try:
        from src.shared.data_providers.hybrid_mcp_provider import HybridMCPProvider
        from src.shared.data_providers.aggregator import DataProviderAggregator
        
        # Initialize both approaches
        mcp_provider = HybridMCPProvider()
        traditional_provider = DataProviderAggregator()
        
        symbol = "AAPL"
        
        # Test MCP approach
        print(f"\n🤖 MCP Approach for {symbol}:")
        start_time = time.time()
        mcp_data = await mcp_provider.get_ai_optimized_data(symbol)
        mcp_time = time.time() - start_time
        print(f"⏱️ Time: {mcp_time:.2f}s")
        print(f"📊 Data keys: {list(mcp_data.keys())}")
        print(f"🔧 MCP used: {mcp_data.get('source') == 'alpha_vantage_mcp'}")
        
        # Test traditional approach
        print(f"\n🔄 Traditional Approach for {symbol}:")
        start_time = time.time()
        traditional_price = await traditional_provider.get_ticker(symbol)
        traditional_hist = await traditional_provider.get_history(symbol, "1mo")
        traditional_time = time.time() - start_time
        print(f"⏱️ Time: {traditional_time:.2f}s")
        print(f"📊 Price success: {traditional_price.get('success', False)}")
        print(f"📊 Historical success: {traditional_hist.get('success', False)}")
        
        # Compare
        print(f"\n📈 Comparison:")
        print(f"🤖 MCP Time: {mcp_time:.2f}s")
        print(f"🔄 Traditional Time: {traditional_time:.2f}s")
        print(f"⚡ Speed difference: {((mcp_time - traditional_time) / traditional_time * 100):+.1f}%")
        
        await mcp_provider.close()
        return True
        
    except Exception as e:
        print(f"❌ Comparison test failed: {e}")
        return False

async def main():
    """Run all MCP integration tests."""
    print("🚀 Starting Alpha Vantage MCP Integration Tests")
    print("=" * 60)
    
    # Check API key
    api_key = os.getenv('ALPHA_VANTAGE_API_KEY')
    if not api_key:
        print("⚠️ ALPHA_VANTAGE_API_KEY not found in environment")
        print("   Some tests may fail or use fallback providers")
    else:
        print(f"✅ API key found: {api_key[:8]}...")
    
    print("\n" + "=" * 60)
    
    # Run tests
    tests = [
        ("MCP Client", test_mcp_client),
        ("Hybrid Provider", test_hybrid_provider),
        ("Enhanced Pipeline", test_enhanced_pipeline),
        ("MCP vs Traditional", test_comparison)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} Test...")
        try:
            success = await test_func()
            results[test_name] = success
            print(f"✅ {test_name} Test: {'PASSED' if success else 'FAILED'}")
        except Exception as e:
            print(f"❌ {test_name} Test: FAILED - {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name:20} {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! MCP integration is working correctly.")
    else:
        print("⚠️ Some tests failed. Check the logs above for details.")
    
    print("\n🚀 MCP Integration Test Complete!")

if __name__ == "__main__":
    import time
    asyncio.run(main())
