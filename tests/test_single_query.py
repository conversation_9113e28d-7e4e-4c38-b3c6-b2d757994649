#!/usr/bin/env python3
"""
Test a single query to see symbol extraction
"""

import asyncio
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_single_query():
    """Test a single query that showed duplication"""
    
    print("🔍 TESTING SINGLE QUERY FOR DUPLICATION")
    print("=" * 50)
    
    # Import the AI processor
    from src.shared.ai_services.ai_processor_robust import RobustFinancialAnalyzer
    
    # Create analyzer instance
    analyzer = RobustFinancialAnalyzer()
    
    # Test the exact query that showed QQQ duplication
    query = "Give me a trading recommendation"
    
    print(f"Query: '{query}'")
    print("-" * 50)
    
    try:
        # Get the response
        response = await analyzer.answer_general_question(query)
        
        print("RESPONSE:")
        print(response)
        print("\n" + "=" * 50)
        
        # Extract symbols the same way as the test
        import re
        symbols = re.findall(r'\b([A-Z]{2,5})\b', response)
        # Filter out common words that aren't stock symbols
        stock_symbols = [s for s in symbols if s in ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'AMD', 'META', 'SPY', 'QQQ', 'JPM', 'BAC', 'JNJ', 'PFE', 'XOM', 'CVX', 'PLTR', 'SNOW']]
        
        print(f"All extracted symbols: {symbols}")
        print(f"Filtered stock symbols: {stock_symbols}")
        
        # Count occurrences
        from collections import Counter
        symbol_counts = Counter(stock_symbols)
        
        print(f"\nSymbol frequency:")
        for symbol, count in symbol_counts.most_common():
            print(f"  {symbol}: {count} times")
            if count > 2:
                print(f"    ⚠️  Potential duplication issue")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_single_query())
