#!/usr/bin/env python3
"""
Test to verify that NVDA is no longer hardcoded as the default response
"""

import asyncio
import sys
import os
from collections import Counter

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_nvda_bias_fix():
    """Test that the system doesn't always default to NVDA"""
    
    print("🧪 TESTING NVDA BIAS FIX")
    print("=" * 50)
    
    # Import the AI processor
    from src.shared.ai_services.ai_processor_robust import RobustFinancialAnalyzer
    
    # Create analyzer instance
    analyzer = RobustFinancialAnalyzer()
    
    # Test fallback responses multiple times to see if they vary
    test_queries = [
        "Hello",
        "What's the weather?",
        "Can you help with trading?",
        "What time is it?",
        "Play some music"
    ]
    
    print("📝 Testing Fallback Response Variation")
    print("-" * 40)
    
    all_symbols_mentioned = []
    
    for query in test_queries:
        print(f"\nQuery: '{query}'")
        
        # Test multiple times to see variation
        symbols_in_responses = []
        for i in range(5):
            response = analyzer._generate_fallback_response(query)
            
            # Extract symbols mentioned in response
            import re
            symbols = re.findall(r'\$([A-Z]+)', response)
            symbols_in_responses.extend(symbols)
            all_symbols_mentioned.extend(symbols)
        
        # Count symbol frequency for this query
        symbol_counts = Counter(symbols_in_responses)
        print(f"  Symbols mentioned: {dict(symbol_counts)}")
    
    print(f"\n🎯 OVERALL SYMBOL DISTRIBUTION")
    print("-" * 40)
    
    total_symbol_counts = Counter(all_symbols_mentioned)
    total_mentions = sum(total_symbol_counts.values())
    
    for symbol, count in total_symbol_counts.most_common():
        percentage = (count / total_mentions) * 100
        print(f"  {symbol}: {count} mentions ({percentage:.1f}%)")
    
    # Check if NVDA is no longer dominating
    nvda_percentage = (total_symbol_counts.get('NVDA', 0) / total_mentions) * 100 if total_mentions > 0 else 0
    
    print(f"\n📊 BIAS ANALYSIS")
    print("-" * 40)
    
    if nvda_percentage > 50:
        print(f"❌ NVDA still dominates with {nvda_percentage:.1f}% of mentions")
        print("   The bias fix may not be working properly")
    elif nvda_percentage > 25:
        print(f"⚠️  NVDA has {nvda_percentage:.1f}% of mentions (moderate bias)")
        print("   Some bias reduction achieved but could be better")
    else:
        print(f"✅ NVDA has only {nvda_percentage:.1f}% of mentions (good distribution)")
        print("   Bias successfully reduced!")
    
    # Test the stock selection for general queries
    print(f"\n📝 Testing Stock Selection for General Queries")
    print("-" * 40)
    
    # Test the random stock selection multiple times
    selected_stocks = []
    for i in range(10):
        # Simulate the stock selection logic
        import random
        stock_pool = ["AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "NVDA", "AMD", "META"]
        symbols = random.sample(stock_pool, min(4, len(stock_pool)))
        selected_stocks.extend(symbols)
    
    selection_counts = Counter(selected_stocks)
    total_selections = sum(selection_counts.values())
    
    print("Stock selection distribution:")
    for stock, count in selection_counts.most_common():
        percentage = (count / total_selections) * 100
        print(f"  {stock}: {count} selections ({percentage:.1f}%)")
    
    # Check if selection is reasonably distributed
    max_percentage = max((count / total_selections) * 100 for count in selection_counts.values())
    min_percentage = min((count / total_selections) * 100 for count in selection_counts.values())
    
    print(f"\nSelection balance: {min_percentage:.1f}% - {max_percentage:.1f}%")
    
    if max_percentage - min_percentage < 20:
        print("✅ Stock selection is well balanced")
    else:
        print("⚠️  Stock selection shows some imbalance")

if __name__ == "__main__":
    asyncio.run(test_nvda_bias_fix())
