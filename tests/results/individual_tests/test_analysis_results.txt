[1m============================= test session starts ==============================[0m
platform linux -- Python 3.10.18, pytest-7.4.3, pluggy-1.6.0 -- /usr/local/bin/python
cachedir: .pytest_cache
rootdir: /app
configfile: pytest.ini
plugins: cov-4.1.0, asyncio-0.23.2, anyio-3.7.1
asyncio: mode=strict
[1mcollecting ... [0m{"timestamp": "2025-08-24T19:01:09.480175", "level": "INFO", "logger": "src.shared.data_providers.aggregator", "message": "Initialized data provider: alpha_vantage", "module": "aggregator", "function": "_initialize_providers", "line": 66, "process_id": 235, "thread_id": 127517798280064}
{"timestamp": "2025-08-24T19:01:09.480938", "level": "INFO", "logger": "src.shared.data_providers.aggregator", "message": "Initialized data provider: yfinance", "module": "aggregator", "function": "_initialize_providers", "line": 66, "process_id": 235, "thread_id": 127517798280064}
{"timestamp": "2025-08-24T19:01:09.496211", "level": "INFO", "logger": "src.data.cache.manager", "message": "\ud83d\uddc4\ufe0f Cache manager initialized with memory backend", "module": "manager", "function": "__init__", "line": 215, "process_id": 235, "thread_id": 127517798280064}
Testing full analysis pipeline...
{"timestamp": "2025-08-24T19:01:09.536902", "level": "INFO", "logger": "src.analysis.orchestration.analysis_orchestrator", "message": "Starting analysis for AAPL", "module": "analysis_orchestrator", "function": "analyze_stock", "line": 27, "process_id": 235, "thread_id": 127517798280064}
{"timestamp": "2025-08-24T19:01:09.537725", "level": "DEBUG", "logger": "src.data.providers.manager", "message": "\ud83d\udd04 Trying provider yfinance for AAPL", "module": "manager", "function": "get_market_data", "line": 171, "process_id": 235, "thread_id": 127517798280064}
yfinance: Note: 'Ticker.info' dict is now fixed & improved, 'fast_info' is no longer faster
{"timestamp": "2025-08-24T19:01:10.139083", "level": "WARNING", "logger": "src.shared.data_providers.yfinance_provider", "message": "Error getting fast_info for AAPL: 'currentTradingPeriod'", "module": "yfinance_provider", "function": "get_ticker", "line": 85, "process_id": 235, "thread_id": 127517798280064}
AAPL: No data found for this date range, symbol may be delisted
{"timestamp": "2025-08-24T19:01:10.244631", "level": "ERROR", "logger": "src.shared.data_providers.yfinance_provider", "message": "Error fetching from Yahoo Finance for AAPL: yfinance error: No data available for AAPL", "module": "yfinance_provider", "function": "get_ticker", "line": 120, "process_id": 235, "thread_id": 127517798280064}
{"timestamp": "2025-08-24T19:01:10.245065", "level": "ERROR", "logger": "src.shared.data_providers.yfinance_provider", "message": "Error getting market data for AAPL: yfinance error: yfinance error: No data available for AAPL", "module": "yfinance_provider", "function": "get_market_data", "line": 199, "process_id": 235, "thread_id": 127517798280064}
{"timestamp": "2025-08-24T19:01:10.245308", "level": "WARNING", "logger": "src.data.providers.manager", "message": "\u26a0\ufe0f Provider yfinance failed for AAPL: yfinance error: yfinance error: yfinance error: No data available for AAPL", "module": "manager", "function": "get_market_data", "line": 187, "process_id": 235, "thread_id": 127517798280064}
{"timestamp": "2025-08-24T19:01:10.245967", "level": "WARNING", "logger": "src.core.exceptions", "message": "Medium severity error: All data providers failed for symbol AAPL. Last error: yfinance error: yfinance error: yfinance error: No data available for AAPL", "module": "exceptions", "function": "_log_exception", "line": 99, "process_id": 235, "thread_id": 127517798280064}
{"timestamp": "2025-08-24T19:01:10.247221", "level": "WARNING", "logger": "src.analysis.orchestration.analysis_orchestrator", "message": "Failed to get quote for AAPL: All data providers failed for symbol AAPL. Last error: yfinance error: yfinance error: yfinance error: No data available for AAPL", "module": "analysis_orchestrator", "function": "analyze_stock", "line": 74, "process_id": 235, "thread_id": 127517798280064}
{"timestamp": "2025-08-24T19:01:10.260978", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Yahoo Finance provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 630, "process_id": 235, "thread_id": 127517798280064}
{"timestamp": "2025-08-24T19:01:10.261184", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Polygon provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 636, "process_id": 235, "thread_id": 127517798280064}
{"timestamp": "2025-08-24T19:01:10.261298", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Finnhub provider initialized", "module": "data_source_manager", "function": "_initialize_providers", "line": 644, "process_id": 235, "thread_id": 127517798280064}
{"timestamp": "2025-08-24T19:01:10.261384", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Initialized 3 data providers", "module": "data_source_manager", "function": "_initialize_providers", "line": 650, "process_id": 235, "thread_id": 127517798280064}
{"timestamp": "2025-08-24T19:01:10.261587", "level": "DEBUG", "logger": "src.api.data.providers.data_source_manager", "message": "Polygon historical data failed for AAPL: 'RealPolygonProvider' object has no attribute 'get_historical_data'", "module": "data_source_manager", "function": "fetch_historical_data", "line": 770, "process_id": 235, "thread_id": 127517798280064}
{"timestamp": "2025-08-24T19:01:10.261687", "level": "WARNING", "logger": "src.api.data.providers.data_source_manager", "message": "\u26a0\ufe0f No historical data available for AAPL, creating synthetic data for analysis", "module": "data_source_manager", "function": "fetch_historical_data", "line": 784, "process_id": 235, "thread_id": 127517798280064}
{"timestamp": "2025-08-24T19:01:10.261789", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\ud83d\udd0d Provider priority for AAPL: ['polygon', 'finnhub', 'yahoo']", "module": "data_source_manager", "function": "_fetch_from_providers", "line": 1069, "process_id": 235, "thread_id": 127517798280064}
{"timestamp": "2025-08-24T19:01:10.902483", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 polygon provided usable data for AAPL", "module": "data_source_manager", "function": "_fetch_from_providers", "line": 1109, "process_id": 235, "thread_id": 127517798280064}
{"timestamp": "2025-08-24T19:01:10.903022", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\ud83d\ude80 High-priority provider polygon succeeded - returning data immediately", "module": "data_source_manager", "function": "_fetch_from_providers", "line": 1113, "process_id": 235, "thread_id": 127517798280064}
{"timestamp": "2025-08-24T19:01:10.904008", "level": "INFO", "logger": "src.api.data.providers.data_source_manager", "message": "\u2705 Generated 30 synthetic historical data points for AAPL", "module": "data_source_manager", "function": "fetch_historical_data", "line": 820, "process_id": 235, "thread_id": 127517798280064}
{"timestamp": "2025-08-24T19:01:10.904291", "level": "INFO", "logger": "src.analysis.orchestration.analysis_orchestrator", "message": "Skipping fundamental data for AAPL - not implemented yet", "module": "analysis_orchestrator", "function": "analyze_stock", "line": 123, "process_id": 235, "thread_id": 127517798280064}
{"timestamp": "2025-08-24T19:01:10.904745", "level": "INFO", "logger": "src.analysis.orchestration.analysis_orchestrator", "message": "Completed analysis for AAPL: HOLD (50%)", "module": "analysis_orchestrator", "function": "analyze_stock", "line": 155, "process_id": 235, "thread_id": 127517798280064}
✅ Analysis successful!
Symbol: AAPL
Recommendation: HOLD
Confidence: 50%
Data sources: []
Analysis quality: LOW
Quote available: False
Historical data available: False
Technical indicators available: False
Fundamental data available: False
Risk assessment available: False
[1m
collecting 0 items                                                             [0m[1m
collected 1 item                                                               [0m

test_analysis.py::test_analysis [33mSKIPPED[0m (async def function and no async
plugin installed (see warnings))[33m                                         [100%][0m

[33m=============================== warnings summary ===============================[0m
test_analysis.py::test_analysis
  /usr/local/lib/python3.10/site-packages/_pytest/python.py:183: PytestUnhandledCoroutineWarning: async def functions are not natively supported and have been skipped.
  You need to install a suitable plugin for your async framework, for example:
    - anyio
    - pytest-asyncio
    - pytest-tornasync
    - pytest-trio
    - pytest-twisted
    warnings.warn(PytestUnhandledCoroutineWarning(msg.format(nodeid)))

-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html

---------- coverage: platform linux, python 3.10.18-final-0 ----------
Name                                                           Stmts   Miss  Cover   Missing
--------------------------------------------------------------------------------------------
src/__init__.py                                                    0      0   100%
src/analysis/__init__.py                                           2      0   100%
src/analysis/ai/__init__.py                                        0      0   100%
src/analysis/ai/calculators/__init__.py                            0      0   100%
src/analysis/ai/calculators/sentiment_calculator.py               38     38     0%   1-102
src/analysis/ai/enhancement_strategy.py                           45     18    60%   38, 105-130, 156, 208-237, 246
src/analysis/ai/recommendation_engine.py                         266    230    14%   53-101, 110-156, 160-210, 214-238, 242-276, 280-305, 309-316, 327-344, 350-367, 371-383, 387-445
src/analysis/fundamental/__init__.py                               0      0   100%
src/analysis/fundamental/calculators/__init__.py                   0      0   100%
src/analysis/fundamental/calculators/growth_calculator.py        114    114     0%   1-283
src/analysis/fundamental/calculators/pe_calculator.py             62     62     0%   1-175
src/analysis/fundamental/metrics.py                              105     81    23%   15-43, 48-55, 60-69, 74-83, 88-97, 102-111, 116-123, 128-135, 140-149
src/analysis/orchestration/__init__.py                             0      0   100%
src/analysis/orchestration/analysis_orchestrator.py              204    106    48%   52-72, 88-118, 124-125, 129-132, 158-160, 168-179, 183-191, 195-198, 208-209, 222-229, 243-255, 265-280, 290-299, 324, 337, 339
src/analysis/orchestration/enhancement_strategy.py                48     48     0%   1-199
src/analysis/risk/__init__.py                                      0      0   100%
src/analysis/risk/assessment.py                                   93     81    13%   15-41, 51-69, 73-112, 116-136, 140-166
src/analysis/risk/calculators/__init__.py                          0      0   100%
src/analysis/risk/calculators/beta_calculator.py                  95     95     0%   1-216
src/analysis/risk/calculators/volatility_calculator.py            80     80     0%   1-178
src/analysis/technical/__init__.py                                 0      0   100%
src/analysis/technical/calculators/__init__.py                     0      0   100%
src/analysis/technical/calculators/macd_calculator.py            100    100     0%   1-235
src/analysis/technical/calculators/rsi_calculator.py              79     79     0%   1-194
src/analysis/technical/indicators.py                             106     85    20%   16-40, 50-77, 85-100, 109-133, 142-161, 166-180, 185-215
src/analysis/utils/__init__.py                                     0      0   100%
src/analysis/utils/data_validators.py                            124    124     0%   1-236
src/api/__init__.py                                                0      0   100%
src/api/analytics/__init__.py                                      0      0   100%
src/api/config.py                                                 29     29     0%   8-68
src/api/data/__init__.py                                           0      0   100%
src/api/data/cache.py                                             85     85     0%   1-263
src/api/data/market_data_service.py                               62     62     0%   1-210
src/api/data/providers/__init__.py                                 0      0   100%
src/api/data/providers/base.py                                    91     54    41%   53-57, 70, 90, 103-111, 124-127, 133-135, 139-146, 167-169, 181-194, 213-225
src/api/data/providers/data_source_manager.py                    650    362    44%   80, 86-88, 114-117, 131-133, 164-165, 174-181, 185, 212, 216, 228-229, 262, 266-270, 287-346, 350-361, 371-419, 436-438, 460, 484-487, 500-551, 563-585, 590-600, 637-638, 647-648, 652-663, 691-692, 705-716, 720-739, 766-768, 774-780, 823-825, 829-907, 916-941, 945-967, 975-977, 981-990, 994-1009, 1016-1030, 1040-1041, 1066-1067, 1074-1075, 1085-1086, 1116-1142, 1149-1151, 1162-1189, 1212, 1229, 1233, 1244-1245, 1249-1250, 1254-1267, 1275-1276, 1280-1281
src/api/data/providers/finnhub.py                                 54     41    24%   34-38, 54-80, 99-149, 162-184
src/api/data/providers/polygon.py                                 83     83     0%   1-235
src/api/main.py                                                   41     41     0%   1-117
src/api/middleware/__init__.py                                     0      0   100%
src/api/middleware/security.py                                    75     75     0%   1-172
src/api/routes/__init__.py                                         0      0   100%
src/api/routes/analytics.py                                      104    104     0%   1-237
src/api/routes/auth.py                                            73     73     0%   1-238
src/api/routes/feedback.py                                        26     26     0%   1-68
src/api/routes/health.py                                          31     31     0%   1-71
src/api/routes/market_data.py                                     92     92     0%   1-274
src/api/routes/metrics.py                                         20     20     0%   1-43
src/api/webhooks/__init__.py                                       0      0   100%
src/bot/__init__.py                                                0      0   100%
src/bot/client.py                                                315    315     0%   6-647
src/bot/commands/__init__.py                                       1      1     0%   6
src/bot/commands/analyze.py                                      120    120     0%   6-256
src/bot/events/__init__.py                                         0      0   100%
src/bot/pipeline/__init__.py                                       0      0   100%
src/bot/pipeline/commands/__init__.py                              0      0   100%
src/bot/pipeline/commands/analyze/__init__.py                      0      0   100%
src/bot/pipeline/commands/analyze/pipeline.py                    128    128     0%   7-303
src/bot/pipeline/commands/analyze/stages/__init__.py               0      0   100%
src/bot/pipeline/commands/analyze/stages/report_template.py      104    104     0%   9-255
src/bot/pipeline/commands/ask/__init__.py                          0      0   100%
src/bot/pipeline/commands/ask/config.py                          285    285     0%   13-526
src/bot/pipeline/commands/ask/pipeline.py                        158    158     0%   17-335
src/bot/pipeline/commands/ask/stages/__init__.py                   5      5     0%   8-13
src/bot/pipeline/commands/ask/stages/ai_cache.py                 135    135     0%   6-254
src/bot/pipeline/commands/ask/stages/ai_chat_processor.py        847    847     0%   1-1771
src/bot/pipeline/commands/ask/stages/ask_sections.py             484    484     0%   30-964
src/bot/pipeline/commands/ask/stages/depth_style_analyzer.py     267    267     0%   8-567
src/bot/pipeline/commands/ask/stages/discord_formatter.py        148    148     0%   8-373
src/bot/pipeline/commands/ask/stages/models.py                    35     35     0%   6-76
src/bot/pipeline/commands/ask/stages/pipeline_sections.py        192    192     0%   7-305
src/bot/pipeline/commands/ask/stages/prompts.py                    7      7     0%   7-382
src/bot/pipeline/commands/ask/stages/query_analyzer.py           231    231     0%   7-459
src/bot/pipeline/commands/ask/stages/quick_commands.py           177    177     0%   8-398
src/bot/pipeline/commands/ask/stages/response_audit.py           126    126     0%   1-273
src/bot/pipeline/commands/ask/stages/response_templates.py       297    297     0%   7-749
src/bot/pipeline/commands/ask/stages/response_validator.py       139    139     0%   7-281
src/bot/pipeline/commands/ask/stages/symbol_validator.py          88     88     0%   8-176
src/bot/pipeline/commands/ask/test_modular_system.py              28     28     0%   7-59
src/bot/pipeline/commands/watchlist/__init__.py                    0      0   100%
src/bot/pipeline/commands/watchlist/stages/__init__.py             0      0   100%
src/bot/pipeline/core/__init__.py                                  0      0   100%
src/bot/pipeline/core/context_manager.py                         120    120     0%   8-231
src/bot/pipeline/core/pipeline_engine.py                         170    170     0%   8-321
src/bot/pipeline/monitoring/__init__.py                            0      0   100%
src/bot/pipeline/shared/__init__.py                                0      0   100%
src/bot/pipeline/shared/data_collectors/__init__.py                0      0   100%
src/bot/pipeline/shared/formatters/__init__.py                     0      0   100%
src/bot/pipeline/shared/validators/__init__.py                     0      0   100%
src/bot/pipeline/test_pipeline.py                                 58     58     0%   8-128
src/bot/pipeline_framework.py                                     63     63     0%   1-204
src/bot/utils/__init__.py                                          0      0   100%
src/core/__init__.py                                               9      1    89%   43
src/core/advanced_security.py                                     75     75     0%   1-250
src/core/config.py                                                69     69     0%   8-118
src/core/config_manager.py                                        57      9    84%   128-129, 155, 159, 163, 166, 185-186, 198
src/core/enums/__init__.py                                         0      0   100%
src/core/enums/stock_analysis.py                                  30     30     0%   1-69
src/core/exceptions.py                                           189     79    58%   95, 97, 101, 105, 109, 126-130, 144-150, 164-170, 182, 194, 210, 226, 238-242, 255-259, 271-275, 288-292, 304, 316, 328-332, 345, 357, 370-374, 388, 395, 402, 415, 427-431, 464-487, 496, 510, 518, 526
src/core/feedback_mechanism.py                                   233    233     0%   1-526
src/core/formatting/__init__.py                                    4      4     0%   1-5
src/core/formatting/analysis_template.py                         172    172     0%   6-295
src/core/formatting/response_templates.py                         43     43     0%   1-230
src/core/formatting/technical_analysis.py                         82     82     0%   8-195
src/core/formatting/text_formatting.py                            41     41     0%   1-121
src/core/logger.py                                               125     58    54%   24, 32-58, 71-72, 81-85, 97, 114, 131, 154-162, 188, 201-214, 259-261, 268, 275-329, 341, 345
src/core/metrics_tracker.py                                       77     77     0%   1-239
src/core/monitoring.py                                           189    120    37%   55-80, 88-97, 102-112, 117-127, 132-142, 147-157, 170-191, 207, 231-247, 251-270, 274, 320-328, 352-394, 398-408, 417, 431-439
src/core/pipeline_engine.py                                       80     45    44%   44, 48, 76-79, 92-133, 156-160, 172, 185-222, 237, 258-260, 269
src/core/response_generator.py                                    80     80     0%   1-222
src/core/risk_management/__init__.py                               2      2     0%   8-16
src/core/risk_management/compliance_framework.py                 129    129     0%   1-459
src/core/secrets.py                                               49     49     0%   1-170
src/core/security.py                                             107    107     0%   1-296
src/core/utils.py                                                 87     87     0%   1-236
src/core/validation/__init__.py                                    2      2     0%   2-4
src/core/validation/financial_validator.py                       251    251     0%   1-473
src/data/__init__.py                                               5      0   100%
src/data/cache/__init__.py                                         2      0   100%
src/data/cache/manager.py                                        261    183    30%   55, 60, 64-65, 78-88, 92-101, 105-109, 113-114, 118-123, 127-139, 143-146, 159-161, 166, 171, 176, 181, 185, 206, 219, 223-245, 249-264, 268-282, 286-297, 301-302, 306-308, 322-335, 339-349, 360-385, 389-418, 424-454, 458-472
src/data/models/__init__.py                                        3      0   100%
src/data/models/indicators.py                                     26      1    96%   45
src/data/models/stock_data.py                                     84      0   100%
src/data/providers/__init__.py                                     7      0   100%
src/data/providers/base.py                                       145     75    48%   72, 79-81, 85-98, 110-120, 124, 142-182, 186-206, 210-212, 225, 229, 245-248, 254, 259, 263, 268, 271
src/data/providers/config.py                                      56     56     0%   8-149
src/data/providers/manager.py                                    142     83    42%   64-70, 74-84, 88-89, 93-95, 99, 112-119, 159-160, 165, 182-183, 226-253, 257-284, 288-315, 319-321, 325-332
src/database/__init__.py                                           0      0   100%
src/database/config.py                                            28     28     0%   8-60
src/database/connection.py                                        98     98     0%   1-176
src/database/migrations/__init__.py                                0      0   100%
src/database/migrations/env.py                                    33     33     0%   1-98
src/database/models/__init__.py                                    5      5     0%   1-6
src/database/models/alerts.py                                     37     37     0%   1-86
src/database/models/analysis.py                                   35     35     0%   1-83
src/database/models/interactions.py                               29     29     0%   1-73
src/database/models/market_data.py                                30     30     0%   1-63
src/database/models/users.py                                      28     28     0%   1-67
src/database/supabase_client.py                                   89     89     0%   1-233
src/modules/__init__.py                                            0      0   100%
src/modules/crypto/__init__.py                                     0      0   100%
src/modules/forex/__init__.py                                      0      0   100%
src/modules/options_flow/__init__.py                               0      0   100%
src/modules/trading_signals/__init__.py                            2      2     0%   9-19
src/modules/trading_signals/signals.py                           119    119     0%   1-255
src/modules/watchlist/__init__.py                                  2      2     0%   7-9
src/modules/watchlist/watchlist_manager.py                       118    118     0%   1-299
src/shared/data_providers/__init__.py                              6      0   100%
src/shared/data_providers/aggregator.py                           84     53    37%   60-61, 68-72, 81, 94-120, 136-162, 174-180
src/shared/data_providers/alpha_vantage.py                       133    103    23%   55-62, 66, 70, 74, 90-158, 172-268, 272-277
src/shared/data_providers/base_provider.py                        20      2    90%   35, 39
src/shared/data_providers/polygon_provider.py                    115     95    17%   24-32, 36-37, 41, 45-74, 78-96, 100-105, 109-161, 165-170, 174-202, 206, 215-226
src/shared/data_providers/yfinance_provider.py                   107     53    50%   27-32, 37, 45, 53-54, 62, 75-83, 89, 93-118, 125-154, 158-173, 182-184
src/shared/error_handling/__init__.py                              4      0   100%
src/shared/error_handling/fallback.py                            120     83    31%   19-26, 56-78, 85-100, 104-110, 134-187, 193, 207-235
src/shared/error_handling/logging.py                             134     70    48%   20-26, 49, 51, 53, 57, 59, 61, 65-66, 101, 148, 171-183, 187, 195-199, 202-212, 215-245, 254-290, 300-315
src/shared/error_handling/retry.py                               165    130    21%   20-28, 52-64, 68-73, 78-93, 97-115, 137-195, 217-272, 276, 299-338, 356-417
src/templates/__init__.py                                          0      0   100%
src/templates/analysis_response.py                               110    110     0%   1-247
src/templates/ask.py                                             291    291     0%   9-751
src/templates/core.py                                             30     30     0%   5-137
--------------------------------------------------------------------------------------------
TOTAL                                                          12690  11163    12%

[33m======================== [33m[1m1 skipped[0m, [33m[1m1 warning[0m[33m in 3.31s[0m[33m =========================[0m
