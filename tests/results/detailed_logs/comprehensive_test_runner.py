"""
Comprehensive Test Runner with Detailed Logging
==============================================

This test runner provides complete visibility into all test operations,
capturing every action, data flow, and result with detailed logging.
"""

import pytest
import sys
import os
import time
import traceback
from datetime import datetime
from typing import Dict, Any, List, Optional
import importlib
import inspect

# Add the test logger to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
from comprehensive_test_logger import (
    test_logger, log_function_calls, log_operation, log_test_data,
    log_assertion_result, log_import_attempt, log_performance_metric,
    log_error_with_context
)

class ComprehensiveTestRunner:
    """
    Test runner with comprehensive logging and monitoring
    """
    
    def __init__(self):
        self.test_results = {}
        self.test_start_time = time.time()
        self.import_errors = []
        self.setup_errors = []
        self.test_errors = []
        
    def run_all_tests(self):
        """Run all tests with comprehensive logging"""
        
        with log_operation("COMPREHENSIVE_TEST_RUN", {"start_time": datetime.now().isoformat()}):
            
            # Log test runner initialization
            test_logger.log_test_start("COMPREHENSIVE_TEST_RUN", "ComprehensiveTestRunner")
            
            try:
                # Step 1: Environment validation
                self.validate_environment()
                
                # Step 2: Import validation
                self.validate_imports()
                
                # Step 3: Run unit tests
                self.run_unit_tests()
                
                # Step 4: Run integration tests
                self.run_integration_tests()
                
                # Step 5: Run comprehensive tests
                self.run_comprehensive_tests()
                
                # Step 6: Generate final report
                self.generate_final_report()
                
            except Exception as e:
                log_error_with_context(e, {"phase": "test_runner", "error_type": "critical"})
                raise
            finally:
                # Generate summary
                summary = test_logger.generate_summary_report()
                return summary
    
    def validate_environment(self):
        """Validate test environment with detailed logging"""
        
        with log_operation("ENVIRONMENT_VALIDATION") as op_id:
            
            test_logger.log_operation("ENVIRONMENT_VALIDATION", {
                "python_version": sys.version,
                "python_executable": sys.executable,
                "working_directory": os.getcwd(),
                "environment_variables": dict(os.environ)
            }, op_id)
            
            # Check Python version
            python_version = sys.version_info
            log_assertion_result(
                "python_version_check",
                ">= 3.8",
                f"{python_version.major}.{python_version.minor}",
                python_version >= (3, 8),
                "ENVIRONMENT_VALIDATION"
            )
            
            # Check required directories
            required_dirs = ['src', 'tests', 'test_results']
            for dir_name in required_dirs:
                exists = os.path.exists(dir_name)
                log_assertion_result(
                    "directory_exists",
                    True,
                    exists,
                    exists,
                    f"ENVIRONMENT_VALIDATION_{dir_name}"
                )
            
            # Check log directory
            log_dir_exists = os.path.exists("test_results/detailed_logs")
            log_assertion_result(
                "log_directory_exists",
                True,
                log_dir_exists,
                log_dir_exists,
                "ENVIRONMENT_VALIDATION"
            )
            
            test_logger.log_operation("ENVIRONMENT_VALIDATION_COMPLETE", {
                "status": "SUCCESS",
                "python_version": f"{python_version.major}.{python_version.minor}.{python_version.micro}",
                "required_directories": {d: os.path.exists(d) for d in required_dirs}
            }, op_id)
    
    def validate_imports(self):
        """Validate all critical imports with detailed logging"""
        
        with log_operation("IMPORT_VALIDATION") as op_id:
            
            critical_modules = [
                'pytest',
                'pytest_asyncio',
                'pytest_cov',
                'sqlalchemy',
                'discord',
                'openai',
                'yfinance',
                'supabase',
                'redis',
                'tenacity',
                'structlog',
                'psutil'
            ]
            
            test_logger.log_operation("IMPORT_VALIDATION_START", {
                "modules_to_check": critical_modules,
                "total_modules": len(critical_modules)
            }, op_id)
            
            for module_name in critical_modules:
                try:
                    start_time = time.time()
                    module = importlib.import_module(module_name)
                    duration = time.time() - start_time
                    
                    log_import_attempt(module_name, True)
                    log_performance_metric(f"import_{module_name}", duration)
                    
                    # Log module details
                    test_logger.log_operation("MODULE_IMPORTED", {
                        "module_name": module_name,
                        "module_version": getattr(module, '__version__', 'unknown'),
                        "module_file": getattr(module, '__file__', 'unknown'),
                        "import_duration": duration
                    }, op_id)
                    
                except ImportError as e:
                    log_import_attempt(module_name, False, e)
                    self.import_errors.append({
                        'module': module_name,
                        'error': str(e),
                        'timestamp': datetime.now().isoformat()
                    })
                    
                    log_error_with_context(e, {
                        'module': module_name,
                        'error_type': 'ImportError'
                    })
            
            # Log import validation results
            successful_imports = len(critical_modules) - len(self.import_errors)
            test_logger.log_operation("IMPORT_VALIDATION_COMPLETE", {
                "successful_imports": successful_imports,
                "failed_imports": len(self.import_errors),
                "total_modules": len(critical_modules),
                "success_rate": successful_imports / len(critical_modules) * 100
            }, op_id)
    
    def run_unit_tests(self):
        """Run unit tests with comprehensive logging"""
        
        with log_operation("UNIT_TESTS") as op_id:
            
            test_logger.log_operation("UNIT_TESTS_START", {
                "test_directory": "tests/unit",
                "expected_tests": ["test_ml_sentiment_analyzer", "test_options_greeks_calculator", "test_strategy_calculator"]
            }, op_id)
            
            try:
                # Run pytest with detailed output
                import subprocess
                
                start_time = time.time()
                result = subprocess.run([
                    sys.executable, '-m', 'pytest', 
                    'tests/unit', 
                    '-v', 
                    '--tb=long',
                    '--capture=no',
                    '--log-cli-level=DEBUG',
                    '--log-cli-format=%(asctime)s [%(levelname)8s] %(name)s: %(message)s'
                ], capture_output=True, text=True, cwd=os.getcwd())
                
                duration = time.time() - start_time
                
                # Log test execution details
                test_logger.log_operation("UNIT_TESTS_EXECUTION", {
                    "command": "pytest tests/unit -v --tb=long",
                    "return_code": result.returncode,
                    "duration": duration,
                    "stdout_length": len(result.stdout),
                    "stderr_length": len(result.stderr)
                }, op_id)
                
                # Log stdout and stderr
                if result.stdout:
                    test_logger.log_data_flow("PYTEST", "STDOUT", result.stdout, "UNIT_TESTS_OUTPUT")
                
                if result.stderr:
                    test_logger.log_data_flow("PYTEST", "STDERR", result.stderr, "UNIT_TESTS_ERRORS")
                
                # Parse test results
                self.parse_pytest_output(result.stdout, "UNIT_TESTS")
                
                log_performance_metric("unit_tests_execution", duration, {
                    "return_code": result.returncode,
                    "tests_run": result.stdout.count("PASSED") + result.stdout.count("FAILED"),
                    "tests_passed": result.stdout.count("PASSED"),
                    "tests_failed": result.stdout.count("FAILED")
                })
                
            except Exception as e:
                log_error_with_context(e, {
                    'test_type': 'unit_tests',
                    'operation_id': op_id
                })
                self.test_errors.append({
                    'test_type': 'unit_tests',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                })
    
    def run_integration_tests(self):
        """Run integration tests with comprehensive logging"""
        
        with log_operation("INTEGRATION_TESTS") as op_id:
            
            test_logger.log_operation("INTEGRATION_TESTS_START", {
                "test_directory": "tests/integration",
                "expected_tests": ["test_alpha_vantage_provider", "test_market_data_service", "test_polygon_provider"]
            }, op_id)
            
            try:
                import subprocess
                
                start_time = time.time()
                result = subprocess.run([
                    sys.executable, '-m', 'pytest', 
                    'tests/integration', 
                    '-v', 
                    '--tb=long',
                    '--capture=no',
                    '--log-cli-level=DEBUG'
                ], capture_output=True, text=True, cwd=os.getcwd())
                
                duration = time.time() - start_time
                
                test_logger.log_operation("INTEGRATION_TESTS_EXECUTION", {
                    "command": "pytest tests/integration -v --tb=long",
                    "return_code": result.returncode,
                    "duration": duration
                }, op_id)
                
                if result.stdout:
                    test_logger.log_data_flow("PYTEST", "STDOUT", result.stdout, "INTEGRATION_TESTS_OUTPUT")
                
                if result.stderr:
                    test_logger.log_data_flow("PYTEST", "STDERR", result.stderr, "INTEGRATION_TESTS_ERRORS")
                
                self.parse_pytest_output(result.stdout, "INTEGRATION_TESTS")
                
                log_performance_metric("integration_tests_execution", duration, {
                    "return_code": result.returncode
                })
                
            except Exception as e:
                log_error_with_context(e, {
                    'test_type': 'integration_tests',
                    'operation_id': op_id
                })
                self.test_errors.append({
                    'test_type': 'integration_tests',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                })
    
    def run_comprehensive_tests(self):
        """Run comprehensive tests with detailed logging"""
        
        with log_operation("COMPREHENSIVE_TESTS") as op_id:
            
            test_logger.log_operation("COMPREHENSIVE_TESTS_START", {
                "test_file": "tests/test_comprehensive.py",
                "expected_tests": ["TestRateLimiter", "TestErrorHandler", "TestWatchlistManager", "TestPerformanceOptimizer"]
            }, op_id)
            
            try:
                import subprocess
                
                start_time = time.time()
                result = subprocess.run([
                    sys.executable, '-m', 'pytest', 
                    'tests/test_comprehensive.py', 
                    '-v', 
                    '--tb=long',
                    '--capture=no',
                    '--log-cli-level=DEBUG'
                ], capture_output=True, text=True, cwd=os.getcwd())
                
                duration = time.time() - start_time
                
                test_logger.log_operation("COMPREHENSIVE_TESTS_EXECUTION", {
                    "command": "pytest tests/test_comprehensive.py -v --tb=long",
                    "return_code": result.returncode,
                    "duration": duration
                }, op_id)
                
                if result.stdout:
                    test_logger.log_data_flow("PYTEST", "STDOUT", result.stdout, "COMPREHENSIVE_TESTS_OUTPUT")
                
                if result.stderr:
                    test_logger.log_data_flow("PYTEST", "STDERR", result.stderr, "COMPREHENSIVE_TESTS_ERRORS")
                
                self.parse_pytest_output(result.stdout, "COMPREHENSIVE_TESTS")
                
                log_performance_metric("comprehensive_tests_execution", duration, {
                    "return_code": result.returncode
                })
                
            except Exception as e:
                log_error_with_context(e, {
                    'test_type': 'comprehensive_tests',
                    'operation_id': op_id
                })
                self.test_errors.append({
                    'test_type': 'comprehensive_tests',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                })
    
    def parse_pytest_output(self, output: str, test_type: str):
        """Parse pytest output and log detailed results"""
        
        with log_operation(f"PARSE_PYTEST_OUTPUT_{test_type}") as op_id:
            
            lines = output.split('\n')
            test_results = []
            current_test = None
            
            for line in lines:
                if '::' in line and ('PASSED' in line or 'FAILED' in line or 'ERROR' in line):
                    # Parse test result line
                    parts = line.split('::')
                    if len(parts) >= 2:
                        test_file = parts[0].strip()
                        test_name = parts[1].strip()
                        
                        if 'PASSED' in line:
                            status = 'PASSED'
                        elif 'FAILED' in line:
                            status = 'FAILED'
                        elif 'ERROR' in line:
                            status = 'ERROR'
                        else:
                            status = 'UNKNOWN'
                        
                        test_result = {
                            'test_file': test_file,
                            'test_name': test_name,
                            'status': status,
                            'line': line.strip()
                        }
                        
                        test_results.append(test_result)
                        
                        # Log individual test result
                        test_logger.log_operation("INDIVIDUAL_TEST_RESULT", {
                            "test_type": test_type,
                            "test_file": test_file,
                            "test_name": test_name,
                            "status": status,
                            "full_line": line.strip()
                        }, op_id)
            
            # Log summary
            passed_count = len([r for r in test_results if r['status'] == 'PASSED'])
            failed_count = len([r for r in test_results if r['status'] == 'FAILED'])
            error_count = len([r for r in test_results if r['status'] == 'ERROR'])
            
            test_logger.log_operation(f"PYTEST_OUTPUT_PARSED_{test_type}", {
                "total_tests": len(test_results),
                "passed": passed_count,
                "failed": failed_count,
                "errors": error_count,
                "success_rate": (passed_count / len(test_results) * 100) if test_results else 0,
                "test_results": test_results
            }, op_id)
            
            # Store results
            self.test_results[test_type] = {
                'total': len(test_results),
                'passed': passed_count,
                'failed': failed_count,
                'errors': error_count,
                'results': test_results
            }
    
    def generate_final_report(self):
        """Generate comprehensive final report"""
        
        with log_operation("GENERATE_FINAL_REPORT") as op_id:
            
            total_duration = time.time() - self.test_start_time
            
            report = {
                'session_id': test_logger.session_id,
                'total_duration_seconds': total_duration,
                'test_results': self.test_results,
                'import_errors': self.import_errors,
                'test_errors': self.test_errors,
                'summary': {
                    'total_tests': sum(r['total'] for r in self.test_results.values()),
                    'total_passed': sum(r['passed'] for r in self.test_results.values()),
                    'total_failed': sum(r['failed'] for r in self.test_results.values()),
                    'total_errors': sum(r['errors'] for r in self.test_results.values()),
                    'overall_success_rate': 0
                },
                'timestamp': datetime.now().isoformat()
            }
            
            # Calculate overall success rate
            if report['summary']['total_tests'] > 0:
                report['summary']['overall_success_rate'] = (
                    report['summary']['total_passed'] / report['summary']['total_tests'] * 100
                )
            
            # Log final report
            test_logger.log_operation("FINAL_REPORT_GENERATED", report, op_id)
            
            # Write report to file
            report_file = f"test_results/detailed_logs/final_test_report_{test_logger.session_id}.json"
            with open(report_file, 'w') as f:
                import json
                json.dump(report, f, indent=2, default=str)
            
            test_logger.log_data_flow("TEST_RUNNER", "REPORT_FILE", report, "FINAL_REPORT")
            
            return report

def main():
    """Main entry point for comprehensive test runner"""
    
    print("🚀 Starting Comprehensive Test Runner with Detailed Logging...")
    print(f"📁 Log directory: test_results/detailed_logs")
    print(f"�� Session ID: {test_logger.session_id}")
    print("=" * 80)
    
    runner = ComprehensiveTestRunner()
    
    try:
        summary = runner.run_all_tests()
        
        print("\n" + "=" * 80)
        print("📊 COMPREHENSIVE TEST RUN COMPLETE")
        print("=" * 80)
        print(f"🆔 Session ID: {test_logger.session_id}")
        print(f"⏱️  Total Duration: {summary['total_duration_seconds']:.3f} seconds")
        print(f"🔧 Total Operations: {summary['total_operations']}")
        print(f"💥 Total Errors: {summary['error_summary']['total_errors']}")
        print(f"📊 Data Transfers: {summary['data_flow_summary']['total_data_transfers']}")
        print(f"💾 Peak Memory Usage: {summary['memory_usage_summary']['peak_usage_mb']:.2f} MB")
        print(f"📁 Detailed logs saved to: test_results/detailed_logs/")
        
        return summary
        
    except Exception as e:
        log_error_with_context(e, {"phase": "main", "error_type": "critical"})
        print(f"💥 CRITICAL ERROR: {e}")
        raise

if __name__ == "__main__":
    main()
