"""
Comprehensive Test Logger
========================

This module provides detailed logging for all test operations, capturing:
- Every action taken
- Order of operations
- Input/output data
- Function calls and returns
- Error conditions
- Data transformations
- Forward/backward data flow
- Performance metrics
- Memory usage
- Network calls
- Database operations
"""

import logging
import json
import time
import traceback
import sys
import os
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from functools import wraps
import inspect
import psutil
import threading
from contextlib import contextmanager

class ComprehensiveTestLogger:
    """
    Comprehensive logging system for test operations
    """
    
    def __init__(self, log_dir: str = "test_results/detailed_logs"):
        self.log_dir = log_dir
        self.session_id = f"test_session_{int(time.time())}"
        self.operation_counter = 0
        self.test_start_time = time.time()
        
        # Create log directory
        os.makedirs(log_dir, exist_ok=True)
        
        # Setup loggers
        self.setup_loggers()
        
        # Performance tracking
        self.performance_metrics = {}
        self.memory_usage = []
        self.operation_times = {}
        
        # Data flow tracking
        self.data_flow = []
        self.function_calls = []
        self.imports = []
        self.errors = []
        
        # Thread safety
        self.lock = threading.Lock()
        
        self.log_info("ComprehensiveTestLogger initialized", {
            "session_id": self.session_id,
            "log_dir": log_dir
        })
    
    def setup_loggers(self):
        """Setup logging configuration"""
        # Main logger
        self.logger = logging.getLogger(f"comprehensive_test_{self.session_id}")
        self.logger.setLevel(logging.DEBUG)
        
        # Remove existing handlers
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_format = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_format)
        self.logger.addHandler(console_handler)
        
        # File handler for detailed logs
        file_handler = logging.FileHandler(
            os.path.join(self.log_dir, f"detailed_log_{self.session_id}.log")
        )
        file_handler.setLevel(logging.DEBUG)
        file_format = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        file_handler.setFormatter(file_format)
        self.logger.addHandler(file_handler)
    
    def log_info(self, message: str, data: Dict[str, Any] = None):
        """Log info message with optional data"""
        with self.lock:
            self.operation_counter += 1
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "operation_id": f"OP_{self.operation_counter:06d}",
                "level": "INFO",
                "message": message,
                "data": data or {}
            }
            
            self.logger.info(f"[{log_entry['operation_id']}] {message}")
            if data:
                self.logger.debug(f"Data: {json.dumps(data, indent=2)}")
            
            # Save to data flow
            self.data_flow.append(log_entry)
            
            # Save to JSON file
            self._save_operation_json(log_entry)
    
    def log_error(self, error: Union[str, Exception], context: Dict[str, Any] = None):
        """Log error with context"""
        with self.lock:
            self.operation_counter += 1
            error_message = str(error) if isinstance(error, Exception) else error
            error_type = type(error).__name__ if isinstance(error, Exception) else "Error"
            
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "operation_id": f"OP_{self.operation_counter:06d}",
                "level": "ERROR",
                "message": error_message,
                "error_type": error_type,
                "context": context or {},
                "traceback": traceback.format_exc() if isinstance(error, Exception) else None
            }
            
            self.logger.error(f"[{log_entry['operation_id']}] {error_message}")
            if context:
                self.logger.debug(f"Context: {json.dumps(context, indent=2)}")
            
            # Save to data flow and errors
            self.data_flow.append(log_entry)
            self.errors.append(log_entry)
            
            # Save to JSON file
            self._save_operation_json(log_entry)
    
    def log_operation(self, operation: str, input_data: Any = None, output_data: Any = None, 
                     duration: float = None, metadata: Dict[str, Any] = None):
        """Log a complete operation with input/output"""
        with self.lock:
            self.operation_counter += 1
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "operation_id": f"OP_{self.operation_counter:06d}",
                "level": "DEBUG",
                "operation": operation,
                "input_data": self._serialize_data(input_data),
                "output_data": self._serialize_data(output_data),
                "duration": duration,
                "metadata": metadata or {}
            }
            
            self.logger.debug(f"[{log_entry['operation_id']}] {operation}")
            if duration is not None:
                self.logger.debug(f"Duration: {duration:.4f}s")
            
            # Save to data flow
            self.data_flow.append(log_entry)
            
            # Save to JSON file
            self._save_operation_json(log_entry)
    
    def log_function_call(self, func_name: str, args: tuple, kwargs: dict, 
                         result: Any = None, duration: float = None):
        """Log function call with arguments and result"""
        with self.lock:
            self.operation_counter += 1
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "operation_id": f"OP_{self.operation_counter:06d}",
                "level": "DEBUG",
                "function": func_name,
                "args": self._serialize_data(args),
                "kwargs": self._serialize_data(kwargs),
                "result": self._serialize_data(result),
                "duration": duration
            }
            
            self.logger.debug(f"[{log_entry['operation_id']}] Function: {func_name}")
            
            # Save to data flow and function calls
            self.data_flow.append(log_entry)
            self.function_calls.append(log_entry)
            
            # Save to JSON file
            self._save_operation_json(log_entry)
    
    def log_test_data(self, test_name: str, test_data: Any, test_type: str = "input"):
        """Log test data (input/output/expected)"""
        with self.lock:
            self.operation_counter += 1
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "operation_id": f"OP_{self.operation_counter:06d}",
                "level": "DEBUG",
                "test_name": test_name,
                "test_type": test_type,
                "test_data": self._serialize_data(test_data)
            }
            
            self.logger.debug(f"[{log_entry['operation_id']}] Test Data: {test_name} ({test_type})")
            
            # Save to data flow
            self.data_flow.append(log_entry)
            
            # Save to JSON file
            self._save_operation_json(log_entry)
    
    def log_assertion_result(self, assertion: str, result: bool, expected: Any = None, 
                           actual: Any = None, message: str = None):
        """Log assertion results"""
        with self.lock:
            self.operation_counter += 1
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "operation_id": f"OP_{self.operation_counter:06d}",
                "level": "DEBUG" if result else "ERROR",
                "assertion": assertion,
                "result": result,
                "expected": self._serialize_data(expected),
                "actual": self._serialize_data(actual),
                "message": message
            }
            
            status = "PASS" if result else "FAIL"
            self.logger.debug(f"[{log_entry['operation_id']}] Assertion {status}: {assertion}")
            
            # Save to data flow
            self.data_flow.append(log_entry)
            
            # Save to JSON file
            self._save_operation_json(log_entry)
    
    def log_import(self, module_name: str, success: bool, error: Exception = None):
        """Log import attempts"""
        with self.lock:
            self.operation_counter += 1
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "operation_id": f"OP_{self.operation_counter:06d}",
                "level": "INFO" if success else "ERROR",
                "module": module_name,
                "success": success,
                "error": str(error) if error else None
            }
            
            status = "SUCCESS" if success else "FAILED"
            self.logger.info(f"[{log_entry['operation_id']}] Import {status}: {module_name}")
            
            # Save to data flow and imports
            self.data_flow.append(log_entry)
            self.imports.append(log_entry)
            
            # Save to JSON file
            self._save_operation_json(log_entry)
    
    def log_performance(self, operation: str, duration: float, metadata: Dict[str, Any] = None):
        """Log performance metrics"""
        with self.lock:
            self.operation_counter += 1
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "operation_id": f"OP_{self.operation_counter:06d}",
                "level": "INFO",
                "operation": operation,
                "duration": duration,
                "metadata": metadata or {}
            }
            
            self.logger.info(f"[{log_entry['operation_id']}] Performance: {operation} - {duration:.4f}s")
            
            # Track performance metrics
            if operation not in self.performance_metrics:
                self.performance_metrics[operation] = []
            self.performance_metrics[operation].append(duration)
            
            # Save to data flow
            self.data_flow.append(log_entry)
            
            # Save to JSON file
            self._save_operation_json(log_entry)
    
    def log_memory_usage(self):
        """Log current memory usage"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            with self.lock:
                self.operation_counter += 1
                log_entry = {
                    "timestamp": datetime.now().isoformat(),
                    "operation_id": f"OP_{self.operation_counter:06d}",
                    "level": "DEBUG",
                    "memory_mb": memory_mb,
                    "memory_percent": process.memory_percent()
                }
                
                self.logger.debug(f"[{log_entry['operation_id']}] Memory: {memory_mb:.2f}MB")
                
                # Track memory usage
                self.memory_usage.append({
                    "timestamp": time.time(),
                    "memory_mb": memory_mb
                })
                
                # Save to data flow
                self.data_flow.append(log_entry)
                
                # Save to JSON file
                self._save_operation_json(log_entry)
                
        except Exception as e:
            self.log_error(f"Failed to log memory usage: {e}")
    
    def _serialize_data(self, data: Any) -> Any:
        """Safely serialize data for JSON storage"""
        try:
            if data is None:
                return None
            elif isinstance(data, (str, int, float, bool)):
                return data
            elif isinstance(data, (list, tuple)):
                return [self._serialize_data(item) for item in data]
            elif isinstance(data, dict):
                return {key: self._serialize_data(value) for key, value in data.items()}
            else:
                return str(data)
        except Exception:
            return f"<unserializable: {type(data).__name__}>"
    
    def _save_operation_json(self, log_entry: Dict[str, Any]):
        """Save individual operation to JSON file"""
        try:
            filename = f"data_flow_data_{log_entry['operation_id']}.json"
            filepath = os.path.join(self.log_dir, filename)
            
            with open(filepath, 'w') as f:
                json.dump(log_entry, f, indent=2, default=str)
                
        except Exception as e:
            self.logger.error(f"Failed to save operation JSON: {e}")
    
    def get_session_summary(self) -> Dict[str, Any]:
        """Get comprehensive session summary"""
        total_duration = time.time() - self.test_start_time
        
        # Calculate performance stats
        performance_stats = {}
        for operation, durations in self.performance_metrics.items():
            if durations:
                performance_stats[operation] = {
                    "count": len(durations),
                    "total": sum(durations),
                    "average": sum(durations) / len(durations),
                    "min": min(durations),
                    "max": max(durations)
                }
        
        # Calculate memory stats
        memory_stats = {}
        if self.memory_usage:
            memory_values = [entry["memory_mb"] for entry in self.memory_usage]
            memory_stats = {
                "peak_mb": max(memory_values),
                "average_mb": sum(memory_values) / len(memory_values),
                "min_mb": min(memory_values),
                "samples": len(memory_values)
            }
        
        return {
            "session_id": self.session_id,
            "total_operations": self.operation_counter,
            "total_duration": total_duration,
            "performance_stats": performance_stats,
            "memory_stats": memory_stats,
            "error_count": len(self.errors),
            "import_count": len(self.imports),
            "function_call_count": len(self.function_calls),
            "data_flow_entries": len(self.data_flow)
        }
    
    def save_session_summary(self):
        """Save comprehensive session summary"""
        try:
            summary = self.get_session_summary()
            filename = f"test_summary_{self.session_id}.json"
            filepath = os.path.join(self.log_dir, filename)
            
            with open(filepath, 'w') as f:
                json.dump(summary, f, indent=2, default=str)
            
            self.log_info("Session summary saved", {"filename": filename})
            
        except Exception as e:
            self.log_error(f"Failed to save session summary: {e}")

# Global logger instance
test_logger = ComprehensiveTestLogger()

# Convenience functions
def log_function_calls(func):
    """Decorator to log function calls"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        test_logger.log_function_call(func.__name__, args, kwargs)
        
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            test_logger.log_function_call(func.__name__, args, kwargs, result, duration)
            return result
        except Exception as e:
            duration = time.time() - start_time
            test_logger.log_error(f"Function {func.__name__} failed: {e}")
            raise
    
    return wrapper

def log_operation(operation: str, input_data: Any = None, output_data: Any = None, 
                 duration: float = None, metadata: Dict[str, Any] = None):
    """Log a complete operation"""
    return test_logger.log_operation(operation, input_data, output_data, duration, metadata)

def log_test_data(test_name: str, test_data: Any, test_type: str = "input"):
    """Log test data"""
    return test_logger.log_test_data(test_name, test_data, test_type)

def log_assertion_result(assertion: str, result: bool, expected: Any = None, 
                        actual: Any = None, message: str = None):
    """Log assertion results"""
    return test_logger.log_assertion_result(assertion, result, expected, actual, message)

def log_import_attempt(module_name: str, success: bool, error: Exception = None):
    """Log import attempts"""
    return test_logger.log_import(module_name, success, error)

def log_performance_metric(operation: str, duration: float, metadata: Dict[str, Any] = None):
    """Log performance metrics - FIXED: Now accepts metadata parameter"""
    return test_logger.log_performance(operation, duration, metadata)

def log_error_with_context(error: Union[str, Exception], context: Dict[str, Any] = None):
    """Log errors with context"""
    return test_logger.log_error(error, context)

# Export main logger instance
__all__ = [
    'test_logger', 'log_function_calls', 'log_operation', 'log_test_data',
    'log_assertion_result', 'log_import_attempt', 'log_performance_metric',
    'log_error_with_context', 'ComprehensiveTestLogger'
]
