"""
Pytest Plugin for Comprehensive Test Logging
============================================

This pytest plugin automatically logs every aspect of test execution,
providing complete visibility into test operations, data flow, and results.
"""

import pytest
import sys
import os
import time
import traceback
import inspect
from datetime import datetime
from typing import Any, Dict, List, Optional

# Add the test logger to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
from comprehensive_test_logger import (
    test_logger, log_function_calls, log_operation, log_test_data,
    log_assertion_result, log_import_attempt, log_performance_metric,
    log_error_with_context
)

class ComprehensiveTestLoggingPlugin:
    """
    Pytest plugin for comprehensive test logging
    """
    
    def __init__(self):
        self.test_start_times = {}
        self.test_results = {}
        self.fixture_usage = {}
        self.assertion_results = []
        self.function_calls = []
        
    def pytest_configure(self, config):
        """Called after command line options have been parsed"""
        test_logger.log_operation("PYTEST_CONFIGURE", {
            "config_options": dict(config.option.__dict__),
            "test_paths": config.args,
            "python_version": sys.version
        })
    
    def pytest_sessionstart(self, session):
        """Called after the Session object has been created"""
        test_logger.log_operation("PYTEST_SESSION_START", {
            "session_id": id(session),
            "test_paths": session.config.args,
            "start_time": datetime.now().isoformat()
        })
    
    def pytest_collection_modifyitems(self, session, config, items):
        """Called after collection has been performed"""
        test_logger.log_operation("PYTEST_COLLECTION_MODIFY", {
            "total_items": len(items),
            "items": [{
                "name": item.name,
                "nodeid": item.nodeid,
                "location": str(item.location),
                "keywords": list(item.keywords)
            } for item in items]
        })
    
    def pytest_runtest_setup(self, item):
        """Called to perform the setup phase for a test item"""
        test_name = item.nodeid
        test_logger.log_test_start(test_name, item.cls.__name__ if item.cls else None)
        
        # Log test setup details
        test_logger.log_operation("TEST_SETUP", {
            "test_name": test_name,
            "test_class": item.cls.__name__ if item.cls else None,
            "test_function": item.function.__name__ if item.function else None,
            "fixtures": list(item.fixturenames),
            "keywords": list(item.keywords)
        })
        
        # Store start time
        self.test_start_times[test_name] = time.time()
    
    def pytest_runtest_call(self, item):
        """Called to execute the test item"""
        test_name = item.nodeid
        
        with log_operation(f"TEST_EXECUTION_{test_name}"):
            test_logger.log_operation("TEST_CALL_START", {
                "test_name": test_name,
                "test_function": item.function.__name__ if item.function else None
            })
    
    def pytest_runtest_teardown(self, item):
        """Called to perform the teardown phase for a test item"""
        test_name = item.nodeid
        
        test_logger.log_operation("TEST_TEARDOWN", {
            "test_name": test_name,
            "teardown_time": datetime.now().isoformat()
        })
    
    def pytest_runtest_logreport(self, report):
        """Process a test setup/call/teardown report"""
        test_name = report.nodeid
        
        # Calculate duration
        duration = 0
        if test_name in self.test_start_times:
            duration = time.time() - self.test_start_times[test_name]
            del self.test_start_times[test_name]
        
        # Log test result
        result_status = report.outcome.upper()
        test_logger.log_test_end(test_name, result_status, duration, f"OP_{len(self.test_results):06d}")
        
        # Store test result
        self.test_results[test_name] = {
            'outcome': report.outcome,
            'duration': duration,
            'when': report.when,
            'longrepr': str(report.longrepr) if report.longrepr else None,
            'sections': report.sections,
            'timestamp': datetime.now().isoformat()
        }
        
        # Log detailed test result
        test_logger.log_operation("TEST_RESULT_DETAILED", {
            "test_name": test_name,
            "outcome": report.outcome,
            "duration": duration,
            "when": report.when,
            "longrepr": str(report.longrepr) if report.longrepr else None,
            "sections": report.sections
        })
        
        # Log performance metrics
        log_performance_metric(f"test_{test_name}", duration, {
            "outcome": report.outcome,
            "when": report.when
        })
    
    def pytest_runtest_logstart(self, nodeid, location):
        """Called at the start of running a single test item"""
        test_logger.log_operation("TEST_LOG_START", {
            "nodeid": nodeid,
            "location": str(location)
        })
    
    def pytest_runtest_logfinish(self, nodeid, location):
        """Called at the end of running a single test item"""
        test_logger.log_operation("TEST_LOG_FINISH", {
            "nodeid": nodeid,
            "location": str(location)
        })
    
    def pytest_fixture_setup(self, fixturedef, request):
        """Called when a fixture is being set up"""
        test_logger.log_operation("FIXTURE_SETUP", {
            "fixture_name": fixturedef.argname,
            "fixture_scope": fixturedef.scope,
            "request_nodeid": request.node.nodeid,
            "fixture_params": getattr(fixturedef, 'params', None)
        })
        
        # Track fixture usage
        if fixturedef.argname not in self.fixture_usage:
            self.fixture_usage[fixturedef.argname] = []
        self.fixture_usage[fixturedef.argname].append({
            'test': request.node.nodeid,
            'timestamp': datetime.now().isoformat()
        })
    
    def pytest_fixture_post_finalizer(self, fixturedef, request):
        """Called after a fixture has been finalized"""
        test_logger.log_operation("FIXTURE_TEARDOWN", {
            "fixture_name": fixturedef.argname,
            "fixture_scope": fixturedef.scope,
            "request_nodeid": request.node.nodeid
        })
    
    def pytest_exception_interact(self, node, call, report):
        """Called when an exception was raised which can potentially be interactively handled"""
        test_logger.log_operation("EXCEPTION_INTERACT", {
            "test_name": node.nodeid,
            "exception_type": type(call.excinfo.value).__name__ if call.excinfo else None,
            "exception_value": str(call.excinfo.value) if call.excinfo else None,
            "traceback": traceback.format_tb(call.excinfo.tb) if call.excinfo else None
        })
        
        if call.excinfo:
            log_error_with_context(call.excinfo.value, {
                'test_name': node.nodeid,
                'exception_type': 'pytest_exception'
            })
    
    def pytest_sessionfinish(self, session, exitstatus):
        """Called after whole test run finished"""
        test_logger.log_operation("PYTEST_SESSION_FINISH", {
            "session_id": id(session),
            "exitstatus": exitstatus,
            "test_results": self.test_results,
            "fixture_usage": self.fixture_usage,
            "end_time": datetime.now().isoformat()
        })
        
        # Generate session summary
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results.values() if r['outcome'] == 'passed'])
        failed_tests = len([r for r in self.test_results.values() if r['outcome'] == 'failed'])
        error_tests = len([r for r in self.test_results.values() if r['outcome'] == 'error'])
        
        test_logger.log_operation("SESSION_SUMMARY", {
            "total_tests": total_tests,
            "passed": passed_tests,
            "failed": failed_tests,
            "errors": error_tests,
            "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0,
            "exitstatus": exitstatus
        })

# Custom assertion logging
class AssertionLogger:
    """Custom assertion logger for detailed assertion tracking"""
    
    @staticmethod
    def log_assertion(assertion_type: str, expected: Any, actual: Any, result: bool, test_name: str = None):
        """Log assertion details"""
        return log_assertion_result(assertion_type, expected, actual, result, test_name)

# Monkey patch assert methods to add logging
def patch_assert_methods():
    """Patch assert methods to add automatic logging"""
    
    # Get the original assert methods
    original_assert = __builtins__['assert']
    
    def logged_assert(condition, message=None):
        """Logged version of assert"""
        if not condition:
            # Log failed assertion
            log_assertion_result(
                "assert",
                True,
                False,
                False,
                inspect.currentframe().f_back.f_code.co_name
            )
            if message:
                log_error_with_context(f"Assertion failed: {message}", {
                    'assertion_type': 'assert',
                    'test_function': inspect.currentframe().f_back.f_code.co_name
                })
            raise AssertionError(message)
        else:
            # Log successful assertion
            log_assertion_result(
                "assert",
                True,
                True,
                True,
                inspect.currentframe().f_back.f_code.co_name
            )
    
    # Replace assert in builtins
    __builtins__['assert'] = logged_assert

# Function call logging decorator
def log_all_function_calls(func):
    """Decorator to log all function calls with detailed information"""
    @pytest.fixture(autouse=True)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        operation_id = None
        error = None
        result = None
        
        try:
            # Log function call start
            operation_id = test_logger.log_function_call(
                func.__name__, args, kwargs, operation="START"
            )
            
            # Execute function
            result = func(*args, **kwargs)
            
            # Log function call success
            duration = time.time() - start_time
            test_logger.log_function_call(
                func.__name__, args, kwargs, result, duration, operation="SUCCESS"
            )
            
            return result
            
        except Exception as e:
            error = e
            duration = time.time() - start_time
            
            # Log function call error
            test_logger.log_function_call(
                func.__name__, args, kwargs, result, duration, error, operation="ERROR"
            )
            
            raise
    
    return wrapper

# Data flow logging utilities
def log_test_data_flow(source: str, destination: str, data: Any, operation: str = "DATA_TRANSFER"):
    """Log data flow between test components"""
    return test_logger.log_data_flow(source, destination, data, operation)

def log_import_attempt(module_name: str, success: bool, error: Exception = None):
    """Log import attempts during tests"""
    return test_logger.log_import(module_name, success, error)

def log_performance_metric(operation: str, duration: float, **kwargs):
    """Log performance metrics during tests"""
    return test_logger.log_performance(operation, duration, **kwargs)

def log_error_with_context(error: Exception, context: Dict[str, Any] = None):
    """Log errors with context during tests"""
    return test_logger.log_error(error, context)

# Export the plugin
def pytest_configure(config):
    """Configure pytest with comprehensive logging plugin"""
    plugin = ComprehensiveTestLoggingPlugin()
    config.pluginmanager.register(plugin, "comprehensive_test_logging")
    
    # Patch assertion methods
    patch_assert_methods()
    
    test_logger.log_operation("PYTEST_PLUGIN_LOADED", {
        "plugin_name": "comprehensive_test_logging",
        "config_options": dict(config.option.__dict__)
    })

# Export utilities
__all__ = [
    'ComprehensiveTestLoggingPlugin', 'AssertionLogger', 'log_test_data_flow',
    'log_import_attempt', 'log_performance_metric', 'log_error_with_context',
    'log_all_function_calls', 'patch_assert_methods'
]
