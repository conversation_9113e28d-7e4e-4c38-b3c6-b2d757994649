{"session_id": "test_session_1757706366", "total_duration_seconds": 16.310325384140015, "total_operations": 58, "performance_summary": {"ENVIRONMENT_VALIDATION": {"operation_id": "OP_000009", "operation_type": "PERFORMANCE", "operation": "ENVIRONMENT_VALIDATION", "duration_seconds": 0.0008103847503662109, "memory_usage_mb": null, "timestamp": "2025-09-12T15:46:06.458067", "session_id": "test_session_1757706366"}, "import_pytest": {"operation_id": "OP_000012", "operation_type": "PERFORMANCE", "operation": "import_pytest", "duration_seconds": 5.9604644775390625e-06, "memory_usage_mb": null, "timestamp": "2025-09-12T15:46:06.458307", "session_id": "test_session_1757706366"}, "import_pytest_asyncio": {"operation_id": "OP_000014", "operation_type": "PERFORMANCE", "operation": "import_pytest_asyncio", "duration_seconds": 0.019376039505004883, "memory_usage_mb": null, "timestamp": "2025-09-12T15:46:06.478147", "session_id": "test_session_1757706366"}, "import_pytest_cov": {"operation_id": "OP_000016", "operation_type": "PERFORMANCE", "operation": "import_pytest_cov", "duration_seconds": 0.0005042552947998047, "memory_usage_mb": null, "timestamp": "2025-09-12T15:46:06.479217", "session_id": "test_session_1757706366"}, "import_sqlalchemy": {"operation_id": "OP_000018", "operation_type": "PERFORMANCE", "operation": "import_sqlalchemy", "duration_seconds": 0.09835648536682129, "memory_usage_mb": null, "timestamp": "2025-09-12T15:46:06.578046", "session_id": "test_session_1757706366"}, "import_discord": {"operation_id": "OP_000020", "operation_type": "PERFORMANCE", "operation": "import_discord", "duration_seconds": 0.16336631774902344, "memory_usage_mb": null, "timestamp": "2025-09-12T15:46:06.741853", "session_id": "test_session_1757706366"}, "import_openai": {"operation_id": "OP_000022", "operation_type": "PERFORMANCE", "operation": "import_openai", "duration_seconds": 0.24932456016540527, "memory_usage_mb": null, "timestamp": "2025-09-12T15:46:06.991565", "session_id": "test_session_1757706366"}, "import_yfinance": {"operation_id": "OP_000024", "operation_type": "PERFORMANCE", "operation": "import_yfinance", "duration_seconds": 0.38013219833374023, "memory_usage_mb": null, "timestamp": "2025-09-12T15:46:07.372009", "session_id": "test_session_1757706366"}, "import_supabase": {"operation_id": "OP_000026", "operation_type": "PERFORMANCE", "operation": "import_supabase", "duration_seconds": 0.06906580924987793, "memory_usage_mb": null, "timestamp": "2025-09-12T15:46:07.441370", "session_id": "test_session_1757706366"}, "import_redis": {"operation_id": "OP_000028", "operation_type": "PERFORMANCE", "operation": "import_redis", "duration_seconds": 0.023688077926635742, "memory_usage_mb": null, "timestamp": "2025-09-12T15:46:07.465351", "session_id": "test_session_1757706366"}, "import_tenacity": {"operation_id": "OP_000030", "operation_type": "PERFORMANCE", "operation": "import_tenacity", "duration_seconds": 0.00428009033203125, "memory_usage_mb": null, "timestamp": "2025-09-12T15:46:07.469909", "session_id": "test_session_1757706366"}, "import_structlog": {"operation_id": "OP_000032", "operation_type": "PERFORMANCE", "operation": "import_structlog", "duration_seconds": 0.008207559585571289, "memory_usage_mb": null, "timestamp": "2025-09-12T15:46:07.478586", "session_id": "test_session_1757706366"}, "import_psutil": {"operation_id": "OP_000034", "operation_type": "PERFORMANCE", "operation": "import_psutil", "duration_seconds": 3.814697265625e-06, "memory_usage_mb": null, "timestamp": "2025-09-12T15:46:07.479707", "session_id": "test_session_1757706366"}, "IMPORT_VALIDATION": {"operation_id": "OP_000035", "operation_type": "PERFORMANCE", "operation": "IMPORT_VALIDATION", "duration_seconds": 1.0217335224151611, "memory_usage_mb": null, "timestamp": "2025-09-12T15:46:07.479872", "session_id": "test_session_1757706366"}, "PARSE_PYTEST_OUTPUT_UNIT_TESTS": {"operation_id": "OP_000040", "operation_type": "PERFORMANCE", "operation": "PARSE_PYTEST_OUTPUT_UNIT_TESTS", "duration_seconds": 0.001142740249633789, "memory_usage_mb": null, "timestamp": "2025-09-12T15:46:11.609573", "session_id": "test_session_1757706366"}, "UNIT_TESTS": {"operation_id": "OP_000042", "operation_type": "PERFORMANCE", "operation": "UNIT_TESTS", "duration_seconds": 4.130159616470337, "memory_usage_mb": null, "timestamp": "2025-09-12T15:46:11.610104", "session_id": "test_session_1757706366"}, "PARSE_PYTEST_OUTPUT_INTEGRATION_TESTS": {"operation_id": "OP_000046", "operation_type": "PERFORMANCE", "operation": "PARSE_PYTEST_OUTPUT_INTEGRATION_TESTS", "duration_seconds": 0.00016927719116210938, "memory_usage_mb": null, "timestamp": "2025-09-12T15:46:15.937421", "session_id": "test_session_1757706366"}, "INTEGRATION_TESTS": {"operation_id": "OP_000048", "operation_type": "PERFORMANCE", "operation": "INTEGRATION_TESTS", "duration_seconds": 4.327500820159912, "memory_usage_mb": null, "timestamp": "2025-09-12T15:46:15.937661", "session_id": "test_session_1757706366"}, "PARSE_PYTEST_OUTPUT_COMPREHENSIVE_TESTS": {"operation_id": "OP_000053", "operation_type": "PERFORMANCE", "operation": "PARSE_PYTEST_OUTPUT_COMPREHENSIVE_TESTS", "duration_seconds": 0.0006971359252929688, "memory_usage_mb": null, "timestamp": "2025-09-12T15:46:22.764806", "session_id": "test_session_1757706366"}, "COMPREHENSIVE_TESTS": {"operation_id": "OP_000055", "operation_type": "PERFORMANCE", "operation": "COMPREHENSIVE_TESTS", "duration_seconds": 6.827324867248535, "memory_usage_mb": null, "timestamp": "2025-09-12T15:46:22.765045", "session_id": "test_session_1757706366"}, "GENERATE_FINAL_REPORT": {"operation_id": "OP_000058", "operation_type": "PERFORMANCE", "operation": "GENERATE_FINAL_REPORT", "duration_seconds": 0.000993967056274414, "memory_usage_mb": null, "timestamp": "2025-09-12T15:46:22.766095", "session_id": "test_session_1757706366"}}, "memory_usage_summary": {"peak_usage_mb": 8556.18359375, "average_usage_mb": 8474.291360294117, "samples": 17}, "error_summary": {"total_errors": 3, "error_types": ["TypeError"], "errors": [{"operation_id": "OP_000041", "operation_type": "ERROR", "error_type": "TypeError", "error_message": "log_performance_metric() takes 2 positional arguments but 3 were given", "error_traceback": "Traceback (most recent call last):\n  File \"/home/<USER>/Desktop/tradingview-automatio/test_results/detailed_logs/comprehensive_test_runner.py\", line 234, in run_unit_tests\n    log_performance_metric(\"unit_tests_execution\", duration, {\nTypeError: log_performance_metric() takes 2 positional arguments but 3 were given\n", "context": {"test_type": "unit_tests", "operation_id": "OP_000036"}, "timestamp": "2025-09-12T15:46:11.610010", "session_id": "test_session_1757706366"}, {"operation_id": "OP_000047", "operation_type": "ERROR", "error_type": "TypeError", "error_message": "log_performance_metric() takes 2 positional arguments but 3 were given", "error_traceback": "Traceback (most recent call last):\n  File \"/home/<USER>/Desktop/tradingview-automatio/test_results/detailed_logs/comprehensive_test_runner.py\", line 291, in run_integration_tests\n    log_performance_metric(\"integration_tests_execution\", duration, {\nTypeError: log_performance_metric() takes 2 positional arguments but 3 were given\n", "context": {"test_type": "integration_tests", "operation_id": "OP_000043"}, "timestamp": "2025-09-12T15:46:15.937589", "session_id": "test_session_1757706366"}, {"operation_id": "OP_000054", "operation_type": "ERROR", "error_type": "TypeError", "error_message": "log_performance_metric() takes 2 positional arguments but 3 were given", "error_traceback": "Traceback (most recent call last):\n  File \"/home/<USER>/Desktop/tradingview-automatio/test_results/detailed_logs/comprehensive_test_runner.py\", line 345, in run_comprehensive_tests\n    log_performance_metric(\"comprehensive_tests_execution\", duration, {\nTypeError: log_performance_metric() takes 2 positional arguments but 3 were given\n", "context": {"test_type": "comprehensive_tests", "operation_id": "OP_000049"}, "timestamp": "2025-09-12T15:46:22.764973", "session_id": "test_session_1757706366"}]}, "data_flow_summary": {"total_data_transfers": 0, "data_flow": []}}