{"operation_id": "OP_000050", "data": "============================= test session starts ==============================\nplatform linux -- Python 3.12.3, pytest-8.4.2, pluggy-1.6.0 -- /home/<USER>/Desktop/tradingview-automatio/venv/bin/python\ncachedir: .pytest_cache\nrootdir: /home/<USER>/Desktop/tradingview-automatio\nconfigfile: pytest.ini\nplugins: asyncio-1.2.0, cov-7.0.0, anyio-4.10.0\nasyncio: mode=Mode.AUTO, debug=False, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ncollecting ... \n----------------------------- live log collection ------------------------------\nWARNING  src.core.config_manager:config_manager.py:417 Environment variable DATABASE_URL not found for substitution\ncollected 16 items\n\ntests/test_comprehensive.py::TestRateLimiter::test_rate_limiter_basic_functionality \n-------------------------------- live log setup --------------------------------\n2025-09-12 15:46:17,456 - asyncio - DEBUG - Using selector: EpollSelector\n2025-09-12 15:46:17,456 - asyncio - DEBUG - Using selector: EpollSelector\n-------------------------------- live log call ---------------------------------\n2025-09-12 15:46:17,457 - src.bot.utils.rate_limiter - WARNING - User test_user_123 is approaching rate limit: 4/5\n2025-09-12 15:46:17,457 - src.bot.utils.rate_limiter - WARNING - User test_user_123 is approaching rate limit: 5/5\nPASSED\ntests/test_comprehensive.py::TestRateLimiter::test_rate_limiter_concurrency \n-------------------------------- live log setup --------------------------------\n2025-09-12 15:46:17,460 - asyncio - DEBUG - Using selector: EpollSelector\n-------------------------------- live log call ---------------------------------\n2025-09-12 15:46:17,460 - src.bot.utils.rate_limiter - WARNING - User concurrent_test_user is approaching rate limit: 8/10\n2025-09-12 15:46:17,461 - src.bot.utils.rate_limiter - WARNING - User concurrent_test_user is approaching rate limit: 9/10\n2025-09-12 15:46:17,461 - src.bot.utils.rate_limiter - WARNING - User concurrent_test_user is approaching rate limit: 10/10\nPASSED\ntests/test_comprehensive.py::TestErrorHandler::test_error_categorization \n-------------------------------- live log call ---------------------------------\n2025-09-12 15:46:17,462 - src.bot.utils.error_handler - WARNING - Error 1757706377462 [validation]: Test error\nPASSED\ntests/test_comprehensive.py::TestErrorHandler::test_user_friendly_messages PASSED\ntests/test_comprehensive.py::TestWatchlistManager::test_watchlist_initialization ERROR\ntests/test_comprehensive.py::TestWatchlistManager::test_create_watchlist ERROR\ntests/test_comprehensive.py::TestPerformanceOptimizer::test_intelligent_cache \n-------------------------------- live log setup --------------------------------\n2025-09-12 15:46:17,623 - asyncio - DEBUG - Using selector: EpollSelector\n-------------------------------- live log call ---------------------------------\n2025-09-12 15:46:17,624 - src.bot.pipeline.performance_optimizer - DEBUG - Cache set for key ffc7b8ce75ffdddc\n2025-09-12 15:46:17,624 - src.bot.pipeline.performance_optimizer - DEBUG - Cache hit for key ffc7b8ce75ffdddc\n2025-09-12 15:46:17,624 - src.bot.pipeline.performance_optimizer - DEBUG - Cache set for key 6d1be40021136dc0\nPASSED\ntests/test_comprehensive.py::TestPerformanceOptimizer::test_cache_eviction \n-------------------------------- live log setup --------------------------------\n2025-09-12 15:46:18,729 - asyncio - DEBUG - Using selector: EpollSelector\n-------------------------------- live log call ---------------------------------\n2025-09-12 15:46:18,731 - src.bot.pipeline.performance_optimizer - DEBUG - Cache set for key 5441a2e50d82f68e\n2025-09-12 15:46:18,731 - src.bot.pipeline.performance_optimizer - DEBUG - Cache set for key d2976e1e56ee1e41\n2025-09-12 15:46:18,732 - src.bot.pipeline.performance_optimizer - DEBUG - Cache set for key 465eb8aa348962a1\n2025-09-12 15:46:18,732 - src.bot.pipeline.performance_optimizer - DEBUG - Cache hit for key 5441a2e50d82f68e\n2025-09-12 15:46:18,732 - src.bot.pipeline.performance_optimizer - DEBUG - Cache set for key f7dc5f4cd9d7e128\n2025-09-12 15:46:18,733 - src.bot.pipeline.performance_optimizer - DEBUG - Cache hit for key 5441a2e50d82f68e\n2025-09-12 15:46:18,733 - src.bot.pipeline.performance_optimizer - DEBUG - Cache hit for key 465eb8aa348962a1\n2025-09-12 15:46:18,733 - src.bot.pipeline.performance_optimizer - DEBUG - Cache hit for key f7dc5f4cd9d7e128\nPASSED\ntests/test_comprehensive.py::TestDiscordUX::test_interactive_embeds PASSED\ntests/test_comprehensive.py::TestHealthMonitor::test_health_checker \n-------------------------------- live log setup --------------------------------\n2025-09-12 15:46:18,739 - asyncio - DEBUG - Using selector: EpollSelector\nPASSED\ntests/test_comprehensive.py::TestAdvancedSecurity::test_input_validation FAILED\ntests/test_comprehensive.py::TestAdvancedSecurity::test_symbol_validation PASSED\ntests/test_comprehensive.py::TestAdvancedSecurity::test_rate_limiting_security \n-------------------------------- live log setup --------------------------------\n2025-09-12 15:46:19,759 - asyncio - DEBUG - Using selector: EpollSelector\n-------------------------------- live log call ---------------------------------\n2025-09-12 15:46:19,760 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,760 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,760 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,760 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,760 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,760 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,760 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,761 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,761 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,761 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,761 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,761 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,761 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,761 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,761 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,761 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,762 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,762 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,762 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,762 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,762 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,762 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,762 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,762 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,762 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,763 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,763 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,763 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,763 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,763 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,763 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,763 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,763 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,764 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,764 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,764 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,764 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,764 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,764 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,764 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,764 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,765 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,765 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,765 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,765 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,765 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,765 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,765 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,765 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,765 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,766 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,766 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,766 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,766 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,766 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,766 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,766 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,766 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,767 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,767 - src.bot.security.advanced_security - INFO - Security Event: request_validated (User: user123)\n2025-09-12 15:46:19,767 - src.bot.security.advanced_security - WARNING - Security Event: rate_limit_exceeded (User: user123)\n2025-09-12 15:46:19,767 - src.bot.security.advanced_security - WARNING - Security Event: rate_limit_exceeded (User: user123)\n2025-09-12 15:46:19,767 - src.bot.security.advanced_security - WARNING - Security Event: rate_limit_exceeded (User: user123)\n2025-09-12 15:46:19,767 - src.bot.security.advanced_security - WARNING - Security Event: rate_limit_exceeded (User: user123)\n2025-09-12 15:46:19,767 - src.bot.security.advanced_security - WARNING - Security Event: rate_limit_exceeded (User: user123)\n2025-09-12 15:46:19,767 - src.bot.security.advanced_security - WARNING - Security Event: rate_limit_exceeded (User: user123)\n2025-09-12 15:46:19,767 - src.bot.security.advanced_security - WARNING - Security Event: rate_limit_exceeded (User: user123)\nPASSED\ntests/test_comprehensive.py::TestIntegration::test_pipeline_with_optimization \n-------------------------------- live log setup --------------------------------\n2025-09-12 15:46:19,768 - asyncio - DEBUG - Using selector: EpollSelector\n-------------------------------- live log call ---------------------------------\n2025-09-12 15:46:19,770 - bot.pipeline.performance_optimizer - INFO - Pipeline performance optimizer initialized\n2025-09-12 15:46:19,771 - bot.pipeline.performance_optimizer - INFO - Performance optimizer background tasks started\n2025-09-12 15:46:19,871 - bot.pipeline.performance_optimizer - DEBUG - Cache set for key 1ddd95007f8a0975\n2025-09-12 15:46:19,872 - bot.pipeline.performance_optimizer - DEBUG - Cache hit for key 1ddd95007f8a0975\n2025-09-12 15:46:19,872 - bot.pipeline.performance_optimizer - INFO - Cache hit for query optimization (0.000s saved)\nPASSED\ntests/test_comprehensive.py::TestPerformanceBenchmarks::test_response_time_benchmark \n-------------------------------- live log setup --------------------------------\n2025-09-12 15:46:19,876 - asyncio - DEBUG - Using selector: EpollSelector\nPASSED\ntests/test_comprehensive.py::TestPerformanceBenchmarks::test_concurrent_user_handling \n-------------------------------- live log setup --------------------------------\n2025-09-12 15:46:20,382 - asyncio - DEBUG - Using selector: EpollSelector\nPASSED\n\n==================================== ERRORS ====================================\n_____ ERROR at setup of TestWatchlistManager.test_watchlist_initialization _____\n\nself = <test_comprehensive.TestWatchlistManager object at 0x7a3e6865e180>\n\n    @pytest.fixture\n    def mock_db_pool(self):\n        \"\"\"Mock database pool\"\"\"\n        pool = Mock()\n        conn = AsyncMock()\n>       pool.acquire.return_value.__aenter__.return_value = conn\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\ntests/test_comprehensive.py:100: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <Mock name='mock.acquire()' id='134408458554544'>, name = '__aenter__'\n\n    def __getattr__(self, name):\n        if name in {'_mock_methods', '_mock_unsafe'}:\n            raise AttributeError(name)\n        elif self._mock_methods is not None:\n            if name not in self._mock_methods or name in _all_magics:\n                raise AttributeError(\"Mock object has no attribute %r\" % name)\n        elif _is_magic(name):\n>           raise AttributeError(name)\nE           AttributeError: __aenter__\n\n/usr/lib/python3.12/unittest/mock.py:660: AttributeError\n_________ ERROR at setup of TestWatchlistManager.test_create_watchlist _________\n\nself = <test_comprehensive.TestWatchlistManager object at 0x7a3e6865e300>\n\n    @pytest.fixture\n    def mock_db_pool(self):\n        \"\"\"Mock database pool\"\"\"\n        pool = Mock()\n        conn = AsyncMock()\n>       pool.acquire.return_value.__aenter__.return_value = conn\n        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\ntests/test_comprehensive.py:100: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <Mock name='mock.acquire()' id='134408458562080'>, name = '__aenter__'\n\n    def __getattr__(self, name):\n        if name in {'_mock_methods', '_mock_unsafe'}:\n            raise AttributeError(name)\n        elif self._mock_methods is not None:\n            if name not in self._mock_methods or name in _all_magics:\n                raise AttributeError(\"Mock object has no attribute %r\" % name)\n        elif _is_magic(name):\n>           raise AttributeError(name)\nE           AttributeError: __aenter__\n\n/usr/lib/python3.12/unittest/mock.py:660: AttributeError\n=================================== FAILURES ===================================\n__________________ TestAdvancedSecurity.test_input_validation __________________\n\nself = <test_comprehensive.TestAdvancedSecurity object at 0x7a3e6865ed50>\n\n    def test_input_validation(self):\n        \"\"\"Test input validation and sanitization\"\"\"\n        # Test valid input\n        valid, sanitized, error = InputValidator.validate_user_input(\"AAPL analysis please\")\n        assert valid == True\n        assert error == \"\"\n    \n        # Test dangerous input\n        valid, sanitized, error = InputValidator.validate_user_input(\"<script>alert('xss')</script>\")\n        assert valid == False\n        assert \"dangerous\" in error.lower()\n    \n        # Test SQL injection\n        valid, sanitized, error = InputValidator.validate_user_input(\"'; DROP TABLE users; --\")\n        assert valid == False\n>       assert \"injection\" in error.lower()\nE       AssertionError: assert 'injection' in 'input contains potentially dangerous content'\nE        +  where 'input contains potentially dangerous content' = <built-in method lower of str object at 0x7a3e68606c10>()\nE        +    where <built-in method lower of str object at 0x7a3e68606c10> = 'Input contains potentially dangerous content'.lower\n\ntests/test_comprehensive.py:236: AssertionError\n=============================== warnings summary ===============================\nvenv/lib/python3.12/site-packages/discord/player.py:30\n  /home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/discord/player.py:30: DeprecationWarning: 'audioop' is deprecated and slated for removal in Python 3.13\n    import audioop\n\nsrc/shared/database/supabase_sdk_client.py:22\n  /home/<USER>/Desktop/tradingview-automatio/src/shared/database/supabase_sdk_client.py:22: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/\n    @validator('url')\n\nsrc/shared/database/supabase_sdk_client.py:36\n  /home/<USER>/Desktop/tradingview-automatio/src/shared/database/supabase_sdk_client.py:36: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/\n    @validator('key')\n\ntests/test_comprehensive.py::TestDiscordUX::test_interactive_embeds\n  /home/<USER>/Desktop/tradingview-automatio/src/bot/enhancements/discord_ux.py:163: DeprecationWarning: datetime.datetime.utcnow() is deprecated and scheduled for removal in a future version. Use timezone-aware objects to represent datetimes in UTC: datetime.datetime.now(datetime.UTC).\n    timestamp=datetime.utcnow()\n\n-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html\n\n🤖 AI Trading Bot Test Summary 🤖\n----------------------------------------\nPython Version: 3.12.3\nSupabase Configured: Yes\nDiscord Bot Token: Configured\n----------------------------------------\n================================ tests coverage ================================\n_______________ coverage: platform linux, python 3.12.3-final-0 ________________\n\nName                                                                        Stmts   Miss  Cover   Missing\n---------------------------------------------------------------------------------------------------------\nsrc/__init__.py                                                                 0      0   100%\nsrc/analysis/__init__.py                                                        2      2     0%   9-16\nsrc/analysis/ai/__init__.py                                                     0      0   100%\nsrc/analysis/ai/calculators/__init__.py                                         0      0   100%\nsrc/analysis/ai/calculators/sentiment_calculator.py                            38     38     0%   1-102\nsrc/analysis/ai/enhancement_strategy.py                                        45     45     0%   1-246\nsrc/analysis/ai/recommendation_engine.py                                      266    266     0%   1-445\nsrc/analysis/fundamental/__init__.py                                            0      0   100%\nsrc/analysis/fundamental/calculators/__init__.py                                0      0   100%\nsrc/analysis/fundamental/calculators/growth_calculator.py                     114    114     0%   1-283\nsrc/analysis/fundamental/calculators/pe_calculator.py                          62     62     0%   1-175\nsrc/analysis/fundamental/metrics.py                                           105    105     0%   1-149\nsrc/analysis/orchestration/__init__.py                                          0      0   100%\nsrc/analysis/orchestration/analysis_orchestrator.py                           204    204     0%   1-341\nsrc/analysis/orchestration/enhancement_strategy.py                             48     48     0%   1-199\nsrc/analysis/probability/__init__.py                                            0      0   100%\nsrc/analysis/probability/probability_engine.py                                342    342     0%   12-757\nsrc/analysis/risk/__init__.py                                                   0      0   100%\nsrc/analysis/risk/assessment.py                                                93     93     0%   1-166\nsrc/analysis/risk/calculators/__init__.py                                       0      0   100%\nsrc/analysis/risk/calculators/beta_calculator.py                               95     95     0%   1-216\nsrc/analysis/risk/calculators/volatility_calculator.py                         80     80     0%   1-178\nsrc/analysis/technical/__init__.py                                              0      0   100%\nsrc/analysis/technical/calculators/__init__.py                                  0      0   100%\nsrc/analysis/technical/calculators/macd_calculator.py                         102    102     0%   1-242\nsrc/analysis/technical/calculators/rsi_calculator.py                           81     81     0%   1-201\nsrc/analysis/technical/config.py                                               50     50     0%   8-109\nsrc/analysis/technical/indicators.py                                          111    111     0%   1-240\nsrc/analysis/technical/price_targets.py                                       230    230     0%   12-495\nsrc/analysis/technical/timeframe_confirmation.py                              379    379     0%   12-685\nsrc/analysis/utils/__init__.py                                                  0      0   100%\nsrc/analysis/utils/data_validators.py                                         124    124     0%   1-236\nsrc/api/__init__.py                                                             0      0   100%\nsrc/api/analytics/__init__.py                                                   0      0   100%\nsrc/api/config.py                                                              21     21     0%   8-55\nsrc/api/data/__init__.py                                                        0      0   100%\nsrc/api/data/cache.py                                                         179    179     0%   1-439\nsrc/api/data/cache_warming_scheduler.py                                       124    124     0%   1-295\nsrc/api/data/constants.py                                                       6      6     0%   6-21\nsrc/api/data/market_data_service.py                                            62     62     0%   1-210\nsrc/api/data/metrics.py                                                       209    209     0%   1-648\nsrc/api/data/providers/__init__.py                                              0      0   100%\nsrc/api/data/providers/alpha_vantage.py                                        81     81     0%   4-143\nsrc/api/data/providers/base.py                                                139    139     0%   5-264\nsrc/api/data/providers/data_source_manager.py                                 726    726     0%   9-1383\nsrc/api/data/providers/finnhub.py                                              95     95     0%   1-264\nsrc/api/data/providers/polygon.py                                             168    168     0%   1-385\nsrc/api/data/scheduled_tasks.py                                               132    132     0%   7-281\nsrc/api/main.py                                                                61     61     0%   2-160\nsrc/api/middleware/__init__.py                                                  0      0   100%\nsrc/api/middleware/security.py                                                 93     93     0%   1-213\nsrc/api/middleware/security_utils.py                                           35     35     0%   5-62\nsrc/api/routers/__init__.py                                                     0      0   100%\nsrc/api/routers/market_data.py                                                 64     64     0%   4-202\nsrc/api/routes/__init__.py                                                      0      0   100%\nsrc/api/routes/analytics.py                                                   133    133     0%   5-306\nsrc/api/routes/bot_health.py                                                  107    107     0%   1-276\nsrc/api/routes/dashboard.py                                                    57     57     0%   1-409\nsrc/api/routes/debug.py                                                        35     35     0%   1-66\nsrc/api/routes/feedback.py                                                     26     26     0%   1-68\nsrc/api/routes/health.py                                                      102    102     0%   1-187\nsrc/api/routes/market_data.py                                                 113    113     0%   1-300\nsrc/api/routes/metrics.py                                                      20     20     0%   1-43\nsrc/api/schemas/__init__.py                                                     0      0   100%\nsrc/api/schemas/feedback_schema.py                                             25     25     0%   1-61\nsrc/api/schemas/metrics_schema.py                                              11     11     0%   1-29\nsrc/api/webhooks/__init__.py                                                    0      0   100%\nsrc/bot/__init__.py                                                             0      0   100%\nsrc/bot/audit/__init__.py                                                       4      4     0%   6-12\nsrc/bot/audit/rate_limiter.py                                                 129    129     0%   8-251\nsrc/bot/audit/request_visualizer.py                                            48     48     0%   7-86\nsrc/bot/audit/request_visualizer_patch.py                                      24     24     0%   8-65\nsrc/bot/audit/session_manager.py                                               37     37     0%   8-85\nsrc/bot/client.py                                                             701    701     0%   6-1470\nsrc/bot/client_audit_integration.py                                            84     84     0%   8-198\nsrc/bot/client_with_monitoring.py                                              54     54     0%   7-137\nsrc/bot/commands/__init__.py                                                    1      1     0%   6\nsrc/bot/commands/alerts.py                                                    164    164     0%   9-476\nsrc/bot/commands/analyze_async.py                                             226    226     0%   8-516\nsrc/bot/commands/batch_analyze.py                                             334    334     0%   8-554\nsrc/bot/commands/help_interactive.py                                          168    168     0%   7-662\nsrc/bot/commands/portfolio.py                                                 281    281     0%   8-679\nsrc/bot/commands/recommendations_command.py                                   255    255     0%   8-713\nsrc/bot/commands/recommendations_enhanced.py                                   91     91     0%   8-221\nsrc/bot/commands/watchlist_enhanced.py                                        286    286     0%   7-679\nsrc/bot/commands/zones_enhanced.py                                            320    320     0%   8-807\nsrc/bot/database_manager.py                                                   126    126     0%   6-188\nsrc/bot/enhancements/__init__.py                                                0      0   100%\nsrc/bot/enhancements/discord_ux.py                                            209    147    30%   19-22, 26-30, 34-40, 44-49, 55-61, 65-72, 76-84, 88-96, 100-142, 205-213, 224, 243-253, 258-294, 307-322, 328-354, 363-412, 420-422, 427-434\nsrc/bot/enhancements/pipeline_visualizer.py                                   303    303     0%   6-510\nsrc/bot/events/__init__.py                                                      0      0   100%\nsrc/bot/metrics_collector.py                                                   93     93     0%   8-272\nsrc/bot/monitoring/__init__.py                                                  0      0   100%\nsrc/bot/monitoring/health_monitor.py                                          200    109    46%   115-117, 128-183, 187-233, 243, 245, 251-303, 318, 322, 326-331, 335-342, 346-359, 363-367, 371-372, 400, 404-406, 410\nsrc/bot/permissions.py                                                        177    177     0%   6-360\nsrc/bot/pipeline/__init__.py                                                    0      0   100%\nsrc/bot/pipeline/ask/__init__.py                                                0      0   100%\nsrc/bot/pipeline/ask/stages/__init__.py                                         0      0   100%\nsrc/bot/pipeline/ask/stages/ai_chat_processor.py                               40     40     0%   9-88\nsrc/bot/pipeline/ask/stages/ai_service_wrapper.py                              19     19     0%   9-54\nsrc/bot/pipeline/ask/stages/conversation_memory_service.py                    233    233     0%   12-513\nsrc/bot/pipeline/commands/__init__.py                                           0      0   100%\nsrc/bot/pipeline/commands/analyze/__init__.py                                   0      0   100%\nsrc/bot/pipeline/commands/analyze/parallel_pipeline.py                        286    286     0%   8-707\nsrc/bot/pipeline/commands/analyze/pipeline.py                                 221    221     0%   7-481\nsrc/bot/pipeline/commands/analyze/stages/__init__.py                            2      2     0%   7-10\nsrc/bot/pipeline/commands/analyze/stages/enhanced_analysis.py                  42     42     0%   5-103\nsrc/bot/pipeline/commands/analyze/stages/fetch_data.py                        100    100     0%   11-161\nsrc/bot/pipeline/commands/analyze/stages/price_targets.py                      37     37     0%   7-67\nsrc/bot/pipeline/commands/analyze/stages/report_generator.py                   61     61     0%   7-153\nsrc/bot/pipeline/commands/analyze/stages/report_template.py                   104    104     0%   9-255\nsrc/bot/pipeline/commands/analyze/stages/technical_analysis.py                 38     38     0%   6-76\nsrc/bot/pipeline/commands/ask/__init__.py                                       2      2     0%   8-10\nsrc/bot/pipeline/commands/ask/batch_processor.py                              114    114     0%   8-372\nsrc/bot/pipeline/commands/ask/config.py                                       332    332     0%   13-677\nsrc/bot/pipeline/commands/ask/executor.py                                      41     41     0%   8-128\nsrc/bot/pipeline/commands/ask/executor_with_grading.py                         59     59     0%   8-199\nsrc/bot/pipeline/commands/ask/modules/__init__.py                               0      0   100%\nsrc/bot/pipeline/commands/ask/modules/config/__init__.py                        0      0   100%\nsrc/bot/pipeline/commands/ask/modules/models/__init__.py                        0      0   100%\nsrc/bot/pipeline/commands/ask/modules/models/data_models.py                   159    159     0%   9-255\nsrc/bot/pipeline/commands/ask/modules/services/__init__.py                      0      0   100%\nsrc/bot/pipeline/commands/ask/modules/tests/__init__.py                         0      0   100%\nsrc/bot/pipeline/commands/ask/modules/utils/__init__.py                         6      6     0%   16-124\nsrc/bot/pipeline/commands/ask/modules/utils/cache.py                          167    167     0%   12-277\nsrc/bot/pipeline/commands/ask/modules/utils/cache_manager.py                  132    132     0%   8-285\nsrc/bot/pipeline/commands/ask/modules/utils/retry.py                          139    139     0%   8-328\nsrc/bot/pipeline/commands/ask/modules/utils/validation.py                     114    114     0%   9-350\nsrc/bot/pipeline/commands/ask/modules/utils/validators.py                      82     82     0%   8-261\nsrc/bot/pipeline/commands/ask/pipeline.py                                     113    113     0%   17-268\nsrc/bot/pipeline/commands/ask/stages/__init__.py                                5      5     0%   8-13\nsrc/bot/pipeline/commands/ask/stages/advanced_classifier.py                   173    173     0%   11-460\nsrc/bot/pipeline/commands/ask/stages/ai_cache.py                              250    250     0%   6-478\nsrc/bot/pipeline/commands/ask/stages/ai_chat_processor.py                      69     69     0%   9-157\nsrc/bot/pipeline/commands/ask/stages/ai_models_config_loader.py               136    136     0%   8-254\nsrc/bot/pipeline/commands/ask/stages/ai_routing_service.py                    258    258     0%   12-562\nsrc/bot/pipeline/commands/ask/stages/ai_service_wrapper.py                     20     20     0%   9-54\nsrc/bot/pipeline/commands/ask/stages/ask_sections.py                          460    460     0%   30-968\nsrc/bot/pipeline/commands/ask/stages/config.py                                 93     93     0%   8-240\nsrc/bot/pipeline/commands/ask/stages/conversation_memory_service.py           257    257     0%   12-547\nsrc/bot/pipeline/commands/ask/stages/core/__init__.py                           7      7     0%   8-15\nsrc/bot/pipeline/commands/ask/stages/core/ai_client.py                         12     12     0%   8-41\nsrc/bot/pipeline/commands/ask/stages/core/base.py                              83     83     0%   8-152\nsrc/bot/pipeline/commands/ask/stages/core/enhanced_ai_client.py               164    164     0%   8-350\nsrc/bot/pipeline/commands/ask/stages/core/error_handler.py                    113    113     0%   8-272\nsrc/bot/pipeline/commands/ask/stages/core/market_context_processor.py         190    190     0%   8-434\nsrc/bot/pipeline/commands/ask/stages/core/response_parser.py                    8      8     0%   8-24\nsrc/bot/pipeline/commands/ask/stages/core/technical_analysis_processor.py     311    311     0%   8-597\nsrc/bot/pipeline/commands/ask/stages/cross_platform_context.py                154    154     0%   8-473\nsrc/bot/pipeline/commands/ask/stages/depth_style_analyzer.py                  270    270     0%   8-572\nsrc/bot/pipeline/commands/ask/stages/discord_formatter.py                     148    148     0%   8-373\nsrc/bot/pipeline/commands/ask/stages/enhanced_analyzer.py                      86     86     0%   8-233\nsrc/bot/pipeline/commands/ask/stages/enhanced_context.py                      233    233     0%   11-552\nsrc/bot/pipeline/commands/ask/stages/language_detector.py                      64     64     0%   8-281\nsrc/bot/pipeline/commands/ask/stages/market_context_service.py                173    173     0%   12-451\nsrc/bot/pipeline/commands/ask/stages/ml_sentiment_analyzer.py                 151    151     0%   8-363\nsrc/bot/pipeline/commands/ask/stages/models.py                                 35     35     0%   6-76\nsrc/bot/pipeline/commands/ask/stages/pipeline_sections.py                     226    226     0%   7-368\nsrc/bot/pipeline/commands/ask/stages/postprocessor/__init__.py                  5      5     0%   8-13\nsrc/bot/pipeline/commands/ask/stages/postprocessor/memory_updater.py            8      8     0%   8-24\nsrc/bot/pipeline/commands/ask/stages/postprocessor/metrics_collector.py         8      8     0%   8-24\nsrc/bot/pipeline/commands/ask/stages/postprocessor/response_formatter.py        4      4     0%   8-17\nsrc/bot/pipeline/commands/ask/stages/postprocessor/response_generator.py      180    180     0%   8-364\nsrc/bot/pipeline/commands/ask/stages/preprocessor/__init__.py                   6      6     0%   8-14\nsrc/bot/pipeline/commands/ask/stages/preprocessor/context_builder.py            8      8     0%   8-24\nsrc/bot/pipeline/commands/ask/stages/preprocessor/context_processor.py        122    122     0%   8-259\nsrc/bot/pipeline/commands/ask/stages/preprocessor/input_processor.py           67     67     0%   8-158\nsrc/bot/pipeline/commands/ask/stages/preprocessor/input_validator.py            6      6     0%   8-25\nsrc/bot/pipeline/commands/ask/stages/preprocessor/prompt_formatter.py           4      4     0%   8-17\nsrc/bot/pipeline/commands/ask/stages/prompts.py                                 7      7     0%   7-389\nsrc/bot/pipeline/commands/ask/stages/query_analyzer.py                        282    282     0%   7-593\nsrc/bot/pipeline/commands/ask/stages/quick_commands.py                        177    177     0%   8-398\nsrc/bot/pipeline/commands/ask/stages/response_audit.py                        134    134     0%   1-274\nsrc/bot/pipeline/commands/ask/stages/response_templates.py                    925    925     0%   7-1791\nsrc/bot/pipeline/commands/ask/stages/response_validator.py                    139    139     0%   7-281\nsrc/bot/pipeline/commands/ask/stages/symbol_validator.py                      115    115     0%   8-258\nsrc/bot/pipeline/commands/ask/stages/test_ai_models_config.py                 122    122     0%   8-213\nsrc/bot/pipeline/commands/ask/stages/test_infrastructure.py                   103    103     0%   8-193\nsrc/bot/pipeline/commands/ask/stages/utils/__init__.py                          6      6     0%   8-14\nsrc/bot/pipeline/commands/ask/stages/utils/cache_integration.py                76     76     0%   8-132\nsrc/bot/pipeline/commands/ask/stages/utils/cache_manager.py                    10     10     0%   8-32\nsrc/bot/pipeline/commands/ask/stages/utils/enhanced_cache_manager.py          177    177     0%   8-466\nsrc/bot/pipeline/commands/ask/stages/utils/fallback_handler.py                  4      4     0%   8-17\nsrc/bot/pipeline/commands/ask/stages/utils/rate_limiter.py                      8      8     0%   8-27\nsrc/bot/pipeline/commands/ask/stages/voice_processor.py                        70     70     0%   8-195\nsrc/bot/pipeline/commands/ask/test_modular_system.py                           28     28     0%   7-59\nsrc/bot/pipeline/commands/watchlist/__init__.py                                 0      0   100%\nsrc/bot/pipeline/commands/watchlist/stages/__init__.py                          0      0   100%\nsrc/bot/pipeline/core/__init__.py                                               0      0   100%\nsrc/bot/pipeline/core/circuit_breaker.py                                      141    141     0%   9-332\nsrc/bot/pipeline/core/context_manager.py                                      144    144     0%   8-286\nsrc/bot/pipeline/core/parallel_pipeline.py                                    129    129     0%   8-246\nsrc/bot/pipeline/core/pipeline_engine.py                                      233    233     0%   8-452\nsrc/bot/pipeline/core/pipeline_optimizer.py                                   175    175     0%   11-313\nsrc/bot/pipeline/data/__init__.py                                               0      0   100%\nsrc/bot/pipeline/logs/__init__.py                                               0      0   100%\nsrc/bot/pipeline/monitoring/__init__.py                                         0      0   100%\nsrc/bot/pipeline/performance_optimizer.py                                     152     29    81%   113, 126-137, 141-142, 153-156, 160-167, 171, 232-234, 236, 240-245\nsrc/bot/pipeline/shared/__init__.py                                             0      0   100%\nsrc/bot/pipeline/shared/data_collectors/__init__.py                             0      0   100%\nsrc/bot/pipeline/shared/formatters/__init__.py                                  0      0   100%\nsrc/bot/pipeline/shared/validators/__init__.py                                  0      0   100%\nsrc/bot/pipeline/test_pipeline.py                                              58     58     0%   8-128\nsrc/bot/pipeline/utils/__init__.py                                              0      0   100%\nsrc/bot/pipeline/utils/circuit_breaker.py                                     109    109     0%   8-201\nsrc/bot/pipeline/utils/metrics.py                                             119    119     0%   8-263\nsrc/bot/pipeline_framework.py                                                  63     63     0%   1-204\nsrc/bot/rate_limiter.py                                                       149    149     0%   8-377\nsrc/bot/security/__init__.py                                                    0      0   100%\nsrc/bot/security/advanced_security.py                                         225     87    61%   65, 109, 113, 124, 160-166, 179-199, 203-211, 217-219, 223-225, 254, 261, 263, 266, 268, 276-278, 282-284, 307-314, 333-343, 356-378, 382-393, 397-406, 410-424, 441, 445\nsrc/bot/setup_audit.py                                                         80     80     0%   8-276\nsrc/bot/token_validator.py                                                     71     71     0%   8-178\nsrc/bot/update_imports.py                                                      25     25     0%   8-50\nsrc/bot/utils/__init__.py                                                       0      0   100%\nsrc/bot/utils/component_checker.py                                             23     23     0%   6-61\nsrc/bot/utils/disclaimer_manager.py                                            53     53     0%   8-184\nsrc/bot/utils/error_handler.py                                                 72     30    58%   80, 117-125, 138-166, 185-209\nsrc/bot/utils/input_sanitizer.py                                              130    130     0%   8-304\nsrc/bot/utils/rate_limiter.py                                                  51     21    59%   83-95, 107-127, 136-139\nsrc/bot/watchlist_alerts.py                                                   266    266     0%   8-553\nsrc/bot/watchlist_realtime.py                                                 201    201     0%   10-468\nsrc/core/__init__.py                                                           10      1    90%   45\nsrc/core/automation/__init__.py                                                 0      0   100%\nsrc/core/automation/analysis_scheduler.py                                     266    266     0%   8-488\nsrc/core/automation/discord_handler.py                                        120    120     0%   8-313\nsrc/core/automation/report_formatter.py                                       372    372     0%   8-826\nsrc/core/automation/report_scheduler.py                                       332    332     0%   8-620\nsrc/core/config_manager.py                                                    217     34    84%   210-212, 218, 343-344, 367, 375, 384-388, 391, 394, 453-454, 466, 477-479, 483, 487, 491, 495, 506, 516, 526, 537, 541-542, 546, 550, 568-569\nsrc/core/data_quality_validator.py                                             99     99     0%   6-223\nsrc/core/enums/__init__.py                                                      0      0   100%\nsrc/core/enums/stock_analysis.py                                               30     30     0%   1-69\nsrc/core/exceptions.py                                                         38     13    66%   11-12, 18, 24, 30, 36, 42, 48, 54, 60, 66, 72, 78\nsrc/core/feedback_mechanism.py                                                233    233     0%   1-526\nsrc/core/formatting/__init__.py                                                 4      4     0%   1-5\nsrc/core/formatting/analysis_template.py                                      172    172     0%   6-295\nsrc/core/formatting/response_templates.py                                      43     43     0%   1-230\nsrc/core/formatting/technical_analysis.py                                     166    166     0%   8-323\nsrc/core/formatting/text_formatting.py                                         41     41     0%   1-121\nsrc/core/logger.py                                                              2      0   100%\nsrc/core/market_calendar.py                                                   185    185     0%   8-530\nsrc/core/monitoring_pkg/__init__.py                                             3      0   100%\nsrc/core/monitoring_pkg/bot_monitor.py                                        127     89    30%   34-35, 45-59, 69-70, 79-81, 94-103, 110-125, 155-165, 176-181, 193-253, 262, 274-277, 289-292, 310-317, 327-338\nsrc/core/monitoring_pkg/performance_tracker.py                                 58     42    28%   27-29, 44-65, 75-95, 114-117, 126-143, 153-154, 158-159\nsrc/core/pipeline_engine.py                                                   124     85    31%   27-65, 77, 81, 85, 89, 93, 99-113, 117-120, 124-152, 159, 163-167, 171, 203-205, 208-209, 212-226\nsrc/core/prompts/__init__.py                                                    4      4     0%   13-31\nsrc/core/prompts/models.py                                                     66     66     0%   5-104\nsrc/core/prompts/prompt_manager.py                                            140    140     0%   5-452\nsrc/core/prompts/templates/__init__.py                                          0      0   100%\nsrc/core/response_generator.py                                                 83     83     0%   1-229\nsrc/core/risk_management/__init__.py                                            2      2     0%   8-16\nsrc/core/risk_management/atr_calculator.py                                    364    364     0%   8-622\nsrc/core/risk_management/compliance_framework.py                              129    129     0%   1-459\nsrc/core/scheduler.py                                                          39     39     0%   1-75\nsrc/core/secure_cache.py                                                       66     66     0%   5-178\nsrc/core/trade_scanner.py                                                     101    101     0%   1-192\nsrc/core/utils.py                                                              87     87     0%   1-236\nsrc/core/validation/__init__.py                                                 2      2     0%   2-4\nsrc/core/validation/financial_validator.py                                    251    251     0%   1-473\nsrc/core/watchlist/__init__.py                                                  1      1     0%   9\nsrc/data/__init__.py                                                            5      5     0%   9-33\nsrc/data/cache/__init__.py                                                      2      2     0%   8-10\nsrc/data/cache/manager.py                                                     137    137     0%   5-300\nsrc/data/models/__init__.py                                                     3      3     0%   7-10\nsrc/data/models/indicators.py                                                  26     26     0%   5-45\nsrc/data/models/stock_data.py                                                  84     84     0%   1-106\nsrc/data/providers/__init__.py                                                 33     33     0%   7-110\nsrc/data/providers/alpha_vantage_provider.py                                  137    137     0%   5-324\nsrc/data/providers/base.py                                                    273    273     0%   5-577\nsrc/data/providers/config.py                                                   53     53     0%   8-170\nsrc/data/providers/finnhub_provider.py                                        122    122     0%   5-297\nsrc/data/providers/manager.py                                                 156    156     0%   8-350\nsrc/data/providers/polygon_provider.py                                        139    139     0%   5-312\nsrc/data/providers/yfinance_provider.py                                       135    135     0%   6-352\nsrc/database/__init__.py                                                        0      0   100%\nsrc/database/config.py                                                         27     27     0%   8-107\nsrc/database/connection.py                                                    154    154     0%   1-265\nsrc/database/migrations/__init__.py                                             0      0   100%\nsrc/database/migrations/env.py                                                 33     33     0%   1-98\nsrc/database/models/__init__.py                                                 5      5     0%   1-6\nsrc/database/models/alerts.py                                                  37     37     0%   1-86\nsrc/database/models/analysis.py                                                35     35     0%   1-83\nsrc/database/models/interactions.py                                            29     29     0%   1-73\nsrc/database/models/market_data.py                                             30     30     0%   1-63\nsrc/database/query_wrapper.py                                                  67     67     0%   7-242\nsrc/database/repositories/__init__.py                                           0      0   100%\nsrc/database/supabase_client.py                                                 4      4     0%   6-14\nsrc/database/unified_client.py                                                 88     88     0%   9-252\nsrc/logs/__init__.py                                                            0      0   100%\nsrc/main.py                                                                    94     94     0%   1-183\nsrc/services/__init__.py                                                        0      0   100%\nsrc/services/analytics_service.py                                              39     39     0%   1-102\nsrc/shared/__init__.py                                                          0      0   100%\nsrc/shared/ai/__init__.py                                                       0      0   100%\nsrc/shared/ai/depth_controller.py                                              87     87     0%   8-231\nsrc/shared/ai/model_fine_tuner.py                                             137    137     0%   8-437\nsrc/shared/ai/recommendation_engine.py                                        282    282     0%   8-597\nsrc/shared/ai_services/__init__.py                                              0      0   100%\nsrc/shared/ai_services/ai_chat_processor.py                                   391    391     0%   8-750\nsrc/shared/ai_services/ai_service_wrapper.py                                  350    350     0%   7-786\nsrc/shared/ai_services/intelligent_chatbot.py                                 275    275     0%   12-635\nsrc/shared/ai_services/openrouter_key.py                                        1      1     0%   7\nsrc/shared/ai_services/query_router.py                                        134    134     0%   8-384\nsrc/shared/ai_services/response_synthesizer.py                                142    142     0%   8-319\nsrc/shared/ai_services/tool_registry.py                                       117    117     0%   9-382\nsrc/shared/analytics/__init__.py                                                0      0   100%\nsrc/shared/analytics/performance_tracker.py                                   264    264     0%   8-489\nsrc/shared/background/__init__.py                                               0      0   100%\nsrc/shared/background/celery_app.py                                            20     20     0%   6-71\nsrc/shared/background/tasks/__init__.py                                         0      0   100%\nsrc/shared/background/tasks/indicators.py                                      32     32     0%   6-84\nsrc/shared/configuration/__init__.py                                           10     10     0%   6-34\nsrc/shared/configuration/validators.py                                        150    150     0%   6-297\nsrc/shared/data_providers/__init__.py                                           5      5     0%   7-12\nsrc/shared/data_providers/aggregator.py                                       127    127     0%   8-266\nsrc/shared/data_providers/alpaca_provider.py                                  155    155     0%   9-354\nsrc/shared/data_providers/alpha_vantage.py                                     11     11     0%   7-29\nsrc/shared/data_providers/base.py                                              16     16     0%   8-71\nsrc/shared/data_providers/fallback_provider.py                                 31     31     0%   8-105\nsrc/shared/data_providers/polygon_provider.py                                 108    108     0%   6-216\nsrc/shared/data_providers/unified_base.py                                      37     37     0%   6-71\nsrc/shared/data_providers/yfinance_provider.py                                 10     10     0%   4-23\nsrc/shared/data_validation.py                                                 233    233     0%   10-579\nsrc/shared/database/__init__.py                                                 5      0   100%\nsrc/shared/database/db_manager.py                                             115     71    38%   71-83, 92-113, 117-118, 122-137, 146-152, 166-171, 185-190, 205-210, 215-229, 237, 241, 245, 249\nsrc/shared/database/supabase_base.py                                           28      8    71%   18, 23, 28, 33, 38, 43, 48, 53\nsrc/shared/database/supabase_http_client.py                                   104     70    33%   44-45, 48-50, 54-81, 85-105, 109-130, 134-153, 157-181, 185-187, 191, 195, 199\nsrc/shared/database/supabase_sdk_client.py                                    202    151    25%   27-34, 41-53, 59-61, 77, 83-87, 91-92, 96-97, 101-102, 106-107, 111, 115-128, 134, 173-226, 231-278, 283-326, 334-364, 368-394, 398-431, 436, 440-441, 445-446\nsrc/shared/database/usage_example.py                                           30     30     0%   5-69\nsrc/shared/error_handling/__init__.py                                           3      0   100%\nsrc/shared/error_handling/fallback.py                                         125     77    38%   89-96, 105-108, 120-139, 159-176, 194-217, 233-263, 277-284, 295-303, 316\nsrc/shared/error_handling/logging.py                                           85     52    39%   31, 34-39, 43-47, 51, 78-83, 95-97, 112-122, 134-142, 157-166, 176-201, 209-211\nsrc/shared/error_handling/retry.py                                            163    163     0%   6-433\nsrc/shared/market_analysis/__init__.py                                          5      5     0%   8-14\nsrc/shared/market_analysis/confidence_scorer.py                               130    130     0%   9-279\nsrc/shared/market_analysis/signal_analyzer.py                                  27     27     0%   10-93\nsrc/shared/market_analysis/signals.py                                          33     33     0%   8-76\nsrc/shared/market_analysis/unified_signal_analyzer.py                         149    149     0%   8-397\nsrc/shared/market_analysis/utils.py                                            41     41     0%   8-120\nsrc/shared/monitoring/__init__.py                                               2      2     0%   7-17\nsrc/shared/monitoring/pipeline_grader.py                                      276    276     0%   9-582\nsrc/shared/redis/__init__.py                                                    2      2     0%   5-7\nsrc/shared/redis/cache_manager.py                                             164    164     0%   12-359\nsrc/shared/redis/redis_manager.py                                             127    127     0%   8-248\nsrc/shared/sentiment/__init__.py                                                0      0   100%\nsrc/shared/sentiment/sentiment_analyzer.py                                    316    316     0%   8-625\nsrc/shared/technical_analysis/__init__.py                                       3      3     0%   6-19\nsrc/shared/technical_analysis/calculator.py                                   100    100     0%   4-251\nsrc/shared/technical_analysis/enhanced_indicators.py                          342    342     0%   15-650\nsrc/shared/technical_analysis/indicators.py                                   135    135     0%   6-350\nsrc/shared/technical_analysis/multi_timeframe_analyzer.py                     414    414     0%   8-780\nsrc/shared/technical_analysis/options_greeks_calculator.py                     82     82     0%   8-257\nsrc/shared/technical_analysis/signal_generator.py                              50     50     0%   7-134\nsrc/shared/technical_analysis/strategy_calculator.py                           66     66     0%   1-247\nsrc/shared/technical_analysis/test_indicators.py                               40     40     0%   6-74\nsrc/shared/technical_analysis/volume_analyzer.py                              298    298     0%   12-541\nsrc/shared/technical_analysis/zones.py                                        648    648     0%   9-1351\nsrc/shared/utils/__init__.py                                                    2      2     0%   5-7\nsrc/shared/utils/deprecation.py                                                 4      4     0%   7-20\nsrc/shared/utils/discord_helpers.py                                           100    100     0%   8-264\nsrc/shared/utils/symbol_extraction.py                                          79     79     0%   13-208\nsrc/shared/watchlist/__init__.py                                                0      0   100%\nsrc/shared/watchlist/base_manager.py                                          180    180     0%   5-352\nsrc/shared/watchlist/bot_manager.py                                            25     25     0%   5-48\nsrc/shared/watchlist/models.py                                                 21      0   100%\nsrc/shared/watchlist/supabase_manager.py                                      167    150    10%   18-19, 23-26, 30-32, 36-38, 42, 46-76, 80-141, 145-174, 179-210, 214-255, 259-295\nsrc/shared/watchlist/webhook_manager.py                                        18     18     0%   5-38\nsrc/templates/__init__.py                                                       0      0   100%\nsrc/templates/analysis_response.py                                            110    110     0%   1-247\nsrc/templates/ask.py                                                          488    488     0%   9-1079\nsrc/templates/core.py                                                          30     30     0%   5-137\n---------------------------------------------------------------------------------------------------------\nTOTAL                                                                       36555  35478     3%\n=========================== short test summary info ============================\nFAILED tests/test_comprehensive.py::TestAdvancedSecurity::test_input_validation - AssertionError: assert 'injection' in 'input contains potentially dangerous content'\n +  where 'input contains potentially dangerous content' = <built-in method lower of str object at 0x7a3e68606c10>()\n +    where <built-in method lower of str object at 0x7a3e68606c10> = 'Input contains potentially dangerous content'.lower\nERROR tests/test_comprehensive.py::TestWatchlistManager::test_watchlist_initialization - AttributeError: __aenter__\nERROR tests/test_comprehensive.py::TestWatchlistManager::test_create_watchlist - AttributeError: __aenter__\n============== 1 failed, 13 passed, 4 warnings, 2 errors in 6.02s ==============\n", "metadata": {"operation_id": "OP_000050", "operation_type": "DATA_FLOW", "source": "PYTEST", "destination": "STDOUT", "operation": "COMPREHENSIVE_TESTS_OUTPUT", "data_type": "str", "data_size": 60879, "data_preview": "============================= test session starts ==============================\nplatform linux -- Python 3.12.3, pytest-8.4.2, pluggy-1.6.0 -- /home/<USER>/Desktop/tradingview-automatio/venv/bin/python\ncachedir: .pytest_cache\nrootdir: /home/<USER>/Desktop/tradingview-automatio\nconfigfile: pytest.ini\nplugins: asyncio-1.2.0, cov-7.0.0, anyio-4.10.0\nasyncio: mode=Mode.AUTO, debug=False, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ncollecting ... \n--------------------...", "timestamp": "2025-09-12T15:46:22.763489", "session_id": "test_session_1757706366"}}