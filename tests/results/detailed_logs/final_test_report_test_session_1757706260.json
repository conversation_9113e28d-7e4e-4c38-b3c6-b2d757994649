{"session_id": "test_session_1757706260", "total_duration_seconds": 29.22857975959778, "test_results": {"UNIT_TESTS": {"total": 16, "passed": 14, "failed": 2, "errors": 0, "results": [{"test_file": "tests/unit/test_options_greeks_calculator.py", "test_name": "test_estimate_new_delta PASSED", "status": "PASSED", "line": "tests/unit/test_options_greeks_calculator.py::test_estimate_new_delta PASSED"}, {"test_file": "tests/unit/test_options_greeks_calculator.py", "test_name": "test_estimate_new_price PASSED", "status": "PASSED", "line": "tests/unit/test_options_greeks_calculator.py::test_estimate_new_price PASSED"}, {"test_file": "tests/unit/test_options_greeks_calculator.py", "test_name": "test_breakeven_calculation PASSED", "status": "PASSED", "line": "tests/unit/test_options_greeks_calculator.py::test_breakeven_calculation PASSED"}, {"test_file": "tests/unit/test_options_greeks_calculator.py", "test_name": "test_probability_of_profit PASSED", "status": "PASSED", "line": "tests/unit/test_options_greeks_calculator.py::test_probability_of_profit PASSED"}, {"test_file": "tests/unit/test_options_greeks_calculator.py", "test_name": "test_time_decay_effect PASSED", "status": "PASSED", "line": "tests/unit/test_options_greeks_calculator.py::test_time_decay_effect PASSED"}, {"test_file": "tests/unit/test_options_greeks_calculator.py", "test_name": "test_volatility_effect PASSED", "status": "PASSED", "line": "tests/unit/test_options_greeks_calculator.py::test_volatility_effect PASSED"}, {"test_file": "tests/unit/test_strategy_calculator.py", "test_name": "test_calculate_entry_strategy PASSED", "status": "PASSED", "line": "tests/unit/test_strategy_calculator.py::test_calculate_entry_strategy PASSED"}, {"test_file": "tests/unit/test_strategy_calculator.py", "test_name": "test_estimate_options_strategy PASSED", "status": "PASSED", "line": "tests/unit/test_strategy_calculator.py::test_estimate_options_strategy PASSED"}, {"test_file": "tests/unit/test_strategy_calculator.py", "test_name": "test_risk_classification PASSED", "status": "PASSED", "line": "tests/unit/test_strategy_calculator.py::test_risk_classification PASSED"}, {"test_file": "tests/unit/test_strategy_calculator.py", "test_name": "test_stop_loss_calculation PASSED", "status": "PASSED", "line": "tests/unit/test_strategy_calculator.py::test_stop_loss_calculation PASSED"}, {"test_file": "tests/unit/test_strategy_calculator.py", "test_name": "test_take_profit_calculation PASSED", "status": "PASSED", "line": "tests/unit/test_strategy_calculator.py::test_take_profit_calculation PASSED"}, {"test_file": "tests/unit/test_strategy_calculator.py", "test_name": "test_supertrend_integration PASSED", "status": "PASSED", "line": "tests/unit/test_strategy_calculator.py::test_supertrend_integration PASSED"}, {"test_file": "tests/unit/test_strategy_calculator.py", "test_name": "test_supertrend_flip_tracking PASSED", "status": "PASSED", "line": "tests/unit/test_strategy_calculator.py::test_supertrend_flip_tracking PASSED"}, {"test_file": "tests/unit/test_strategy_calculator.py", "test_name": "test_supertrend_logging PASSED", "status": "PASSED", "line": "tests/unit/test_strategy_calculator.py::test_supertrend_logging PASSED"}, {"test_file": "FAILED tests/unit/test_ml_sentiment_analyzer.py", "test_name": "test_negative_sentiment_analysis - AssertionError: assert 'neutral' == 'negative'", "status": "FAILED", "line": "FAILED tests/unit/test_ml_sentiment_analyzer.py::test_negative_sentiment_analysis - AssertionError: assert 'neutral' == 'negative'"}, {"test_file": "FAILED tests/unit/test_ml_sentiment_analyzer.py", "test_name": "test_sentiment_extraction - AssertionError: assert 'risks' in ['weak']", "status": "FAILED", "line": "FAILED tests/unit/test_ml_sentiment_analyzer.py::test_sentiment_extraction - AssertionError: assert 'risks' in ['weak']"}]}, "INTEGRATION_TESTS": {"total": 0, "passed": 0, "failed": 0, "errors": 0, "results": []}, "COMPREHENSIVE_TESTS": {"total": 9, "passed": 3, "failed": 2, "errors": 4, "results": [{"test_file": "tests/test_comprehensive.py", "test_name": "TestErro<PERSON><PERSON><PERSON><PERSON>", "status": "PASSED", "line": "tests/test_comprehensive.py::TestErrorHandler::test_user_friendly_messages PASSED"}, {"test_file": "tests/test_comprehensive.py", "test_name": "TestWatchlistManager", "status": "ERROR", "line": "tests/test_comprehensive.py::TestWatchlistManager::test_watchlist_initialization ERROR"}, {"test_file": "tests/test_comprehensive.py", "test_name": "TestWatchlistManager", "status": "ERROR", "line": "tests/test_comprehensive.py::TestWatchlistManager::test_create_watchlist ERROR"}, {"test_file": "tests/test_comprehensive.py", "test_name": "TestDiscordUX", "status": "PASSED", "line": "tests/test_comprehensive.py::TestDiscordUX::test_interactive_embeds PASSED"}, {"test_file": "tests/test_comprehensive.py", "test_name": "TestAdvancedSecurity", "status": "FAILED", "line": "tests/test_comprehensive.py::TestAdvancedSecurity::test_input_validation FAILED"}, {"test_file": "tests/test_comprehensive.py", "test_name": "TestAdvancedSecurity", "status": "PASSED", "line": "tests/test_comprehensive.py::TestAdvancedSecurity::test_symbol_validation PASSED"}, {"test_file": "FAILED tests/test_comprehensive.py", "test_name": "TestAdvancedSecurity", "status": "FAILED", "line": "FAILED tests/test_comprehensive.py::TestAdvancedSecurity::test_input_validation - AssertionError: assert 'injection' in 'input contains potentially dangerous content'"}, {"test_file": "ERROR tests/test_comprehensive.py", "test_name": "TestWatchlistManager", "status": "ERROR", "line": "ERROR tests/test_comprehensive.py::TestWatchlistManager::test_watchlist_initialization - AttributeError: __aenter__"}, {"test_file": "ERROR tests/test_comprehensive.py", "test_name": "TestWatchlistManager", "status": "ERROR", "line": "ERROR tests/test_comprehensive.py::TestWatchlistManager::test_create_watchlist - AttributeError: __aenter__"}]}}, "import_errors": [], "test_errors": [{"test_type": "unit_tests", "error": "log_performance_metric() takes 2 positional arguments but 3 were given", "timestamp": "2025-09-12T15:44:29.002766"}, {"test_type": "integration_tests", "error": "log_performance_metric() takes 2 positional arguments but 3 were given", "timestamp": "2025-09-12T15:44:38.132872"}, {"test_type": "comprehensive_tests", "error": "log_performance_metric() takes 2 positional arguments but 3 were given", "timestamp": "2025-09-12T15:44:49.606372"}], "summary": {"total_tests": 25, "total_passed": 17, "total_failed": 4, "total_errors": 4, "overall_success_rate": 68.0}, "timestamp": "2025-09-12T15:44:49.606678"}