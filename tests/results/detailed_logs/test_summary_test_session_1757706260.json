{"session_id": "test_session_1757706260", "total_duration_seconds": 29.23211359977722, "total_operations": 58, "performance_summary": {"ENVIRONMENT_VALIDATION": {"operation_id": "OP_000009", "operation_type": "PERFORMANCE", "operation": "ENVIRONMENT_VALIDATION", "duration_seconds": 0.0010569095611572266, "memory_usage_mb": null, "timestamp": "2025-09-12T15:44:20.379688", "session_id": "test_session_1757706260"}, "import_pytest": {"operation_id": "OP_000012", "operation_type": "PERFORMANCE", "operation": "import_pytest", "duration_seconds": 1.0251998901367188e-05, "memory_usage_mb": null, "timestamp": "2025-09-12T15:44:20.380140", "session_id": "test_session_1757706260"}, "import_pytest_asyncio": {"operation_id": "OP_000014", "operation_type": "PERFORMANCE", "operation": "import_pytest_asyncio", "duration_seconds": 0.0451817512512207, "memory_usage_mb": null, "timestamp": "2025-09-12T15:44:20.425972", "session_id": "test_session_1757706260"}, "import_pytest_cov": {"operation_id": "OP_000016", "operation_type": "PERFORMANCE", "operation": "import_pytest_cov", "duration_seconds": 0.0008254051208496094, "memory_usage_mb": null, "timestamp": "2025-09-12T15:44:20.427478", "session_id": "test_session_1757706260"}, "import_sqlalchemy": {"operation_id": "OP_000018", "operation_type": "PERFORMANCE", "operation": "import_sqlalchemy", "duration_seconds": 0.19489002227783203, "memory_usage_mb": null, "timestamp": "2025-09-12T15:44:20.623216", "session_id": "test_session_1757706260"}, "import_discord": {"operation_id": "OP_000020", "operation_type": "PERFORMANCE", "operation": "import_discord", "duration_seconds": 0.29721689224243164, "memory_usage_mb": null, "timestamp": "2025-09-12T15:44:20.921014", "session_id": "test_session_1757706260"}, "import_openai": {"operation_id": "OP_000022", "operation_type": "PERFORMANCE", "operation": "import_openai", "duration_seconds": 0.4612107276916504, "memory_usage_mb": null, "timestamp": "2025-09-12T15:44:21.383012", "session_id": "test_session_1757706260"}, "import_yfinance": {"operation_id": "OP_000024", "operation_type": "PERFORMANCE", "operation": "import_yfinance", "duration_seconds": 0.7480349540710449, "memory_usage_mb": null, "timestamp": "2025-09-12T15:44:22.131796", "session_id": "test_session_1757706260"}, "import_supabase": {"operation_id": "OP_000026", "operation_type": "PERFORMANCE", "operation": "import_supabase", "duration_seconds": 0.11747932434082031, "memory_usage_mb": null, "timestamp": "2025-09-12T15:44:22.249751", "session_id": "test_session_1757706260"}, "import_redis": {"operation_id": "OP_000028", "operation_type": "PERFORMANCE", "operation": "import_redis", "duration_seconds": 0.05241680145263672, "memory_usage_mb": null, "timestamp": "2025-09-12T15:44:22.302829", "session_id": "test_session_1757706260"}, "import_tenacity": {"operation_id": "OP_000030", "operation_type": "PERFORMANCE", "operation": "import_tenacity", "duration_seconds": 0.007498979568481445, "memory_usage_mb": null, "timestamp": "2025-09-12T15:44:22.310918", "session_id": "test_session_1757706260"}, "import_structlog": {"operation_id": "OP_000032", "operation_type": "PERFORMANCE", "operation": "import_structlog", "duration_seconds": 0.01726078987121582, "memory_usage_mb": null, "timestamp": "2025-09-12T15:44:22.328548", "session_id": "test_session_1757706260"}, "import_psutil": {"operation_id": "OP_000034", "operation_type": "PERFORMANCE", "operation": "import_psutil", "duration_seconds": 5.4836273193359375e-06, "memory_usage_mb": null, "timestamp": "2025-09-12T15:44:22.329504", "session_id": "test_session_1757706260"}, "IMPORT_VALIDATION": {"operation_id": "OP_000035", "operation_type": "PERFORMANCE", "operation": "IMPORT_VALIDATION", "duration_seconds": 1.9499497413635254, "memory_usage_mb": null, "timestamp": "2025-09-12T15:44:22.329777", "session_id": "test_session_1757706260"}, "PARSE_PYTEST_OUTPUT_UNIT_TESTS": {"operation_id": "OP_000040", "operation_type": "PERFORMANCE", "operation": "PARSE_PYTEST_OUTPUT_UNIT_TESTS", "duration_seconds": 0.0025055408477783203, "memory_usage_mb": null, "timestamp": "2025-09-12T15:44:29.001009", "session_id": "test_session_1757706260"}, "UNIT_TESTS": {"operation_id": "OP_000042", "operation_type": "PERFORMANCE", "operation": "UNIT_TESTS", "duration_seconds": 6.672902345657349, "memory_usage_mb": null, "timestamp": "2025-09-12T15:44:29.002784", "session_id": "test_session_1757706260"}, "PARSE_PYTEST_OUTPUT_INTEGRATION_TESTS": {"operation_id": "OP_000046", "operation_type": "PERFORMANCE", "operation": "PARSE_PYTEST_OUTPUT_INTEGRATION_TESTS", "duration_seconds": 0.0003314018249511719, "memory_usage_mb": null, "timestamp": "2025-09-12T15:44:38.132434", "session_id": "test_session_1757706260"}, "INTEGRATION_TESTS": {"operation_id": "OP_000048", "operation_type": "PERFORMANCE", "operation": "INTEGRATION_TESTS", "duration_seconds": 9.129896879196167, "memory_usage_mb": null, "timestamp": "2025-09-12T15:44:38.132890", "session_id": "test_session_1757706260"}, "PARSE_PYTEST_OUTPUT_COMPREHENSIVE_TESTS": {"operation_id": "OP_000053", "operation_type": "PERFORMANCE", "operation": "PARSE_PYTEST_OUTPUT_COMPREHENSIVE_TESTS", "duration_seconds": 0.0020771026611328125, "memory_usage_mb": null, "timestamp": "2025-09-12T15:44:49.605691", "session_id": "test_session_1757706260"}, "COMPREHENSIVE_TESTS": {"operation_id": "OP_000055", "operation_type": "PERFORMANCE", "operation": "COMPREHENSIVE_TESTS", "duration_seconds": 11.473331212997437, "memory_usage_mb": null, "timestamp": "2025-09-12T15:44:49.606385", "session_id": "test_session_1757706260"}, "GENERATE_FINAL_REPORT": {"operation_id": "OP_000058", "operation_type": "PERFORMANCE", "operation": "GENERATE_FINAL_REPORT", "duration_seconds": 0.002655506134033203, "memory_usage_mb": null, "timestamp": "2025-09-12T15:44:49.609197", "session_id": "test_session_1757706260"}}, "memory_usage_summary": {"peak_usage_mb": 8669.13671875, "average_usage_mb": 8606.020963541667, "samples": 30}, "error_summary": {"total_errors": 3, "error_types": ["TypeError"], "errors": [{"operation_id": "OP_000041", "operation_type": "ERROR", "error_type": "TypeError", "error_message": "log_performance_metric() takes 2 positional arguments but 3 were given", "error_traceback": "Traceback (most recent call last):\n  File \"/home/<USER>/Desktop/tradingview-automatio/test_results/detailed_logs/comprehensive_test_runner.py\", line 234, in run_unit_tests\n    log_performance_metric(\"unit_tests_execution\", duration, {\nTypeError: log_performance_metric() takes 2 positional arguments but 3 were given\n", "context": {"test_type": "unit_tests", "operation_id": "OP_000036"}, "timestamp": "2025-09-12T15:44:29.002341", "session_id": "test_session_1757706260"}, {"operation_id": "OP_000047", "operation_type": "ERROR", "error_type": "TypeError", "error_message": "log_performance_metric() takes 2 positional arguments but 3 were given", "error_traceback": "Traceback (most recent call last):\n  File \"/home/<USER>/Desktop/tradingview-automatio/test_results/detailed_logs/comprehensive_test_runner.py\", line 291, in run_integration_tests\n    log_performance_metric(\"integration_tests_execution\", duration, {\nTypeError: log_performance_metric() takes 2 positional arguments but 3 were given\n", "context": {"test_type": "integration_tests", "operation_id": "OP_000043"}, "timestamp": "2025-09-12T15:44:38.132755", "session_id": "test_session_1757706260"}, {"operation_id": "OP_000054", "operation_type": "ERROR", "error_type": "TypeError", "error_message": "log_performance_metric() takes 2 positional arguments but 3 were given", "error_traceback": "Traceback (most recent call last):\n  File \"/home/<USER>/Desktop/tradingview-automatio/test_results/detailed_logs/comprehensive_test_runner.py\", line 345, in run_comprehensive_tests\n    log_performance_metric(\"comprehensive_tests_execution\", duration, {\nTypeError: log_performance_metric() takes 2 positional arguments but 3 were given\n", "context": {"test_type": "comprehensive_tests", "operation_id": "OP_000049"}, "timestamp": "2025-09-12T15:44:49.606207", "session_id": "test_session_1757706260"}]}, "data_flow_summary": {"total_data_transfers": 0, "data_flow": []}}