{"operation_id": "OP_000044", "data": "============================= test session starts ==============================\nplatform linux -- Python 3.12.3, pytest-8.4.2, pluggy-1.6.0 -- /home/<USER>/Desktop/tradingview-automatio/venv/bin/python\ncachedir: .pytest_cache\nrootdir: /home/<USER>/Desktop/tradingview-automatio\nconfigfile: pytest.ini\nplugins: asyncio-1.2.0, cov-7.0.0, anyio-4.10.0\nasyncio: mode=Mode.AUTO, debug=False, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ncollecting ... \n----------------------------- live log collection ------------------------------\n2025-09-12 15:46:12,245 - src.core.config_manager - WARNING - Environment variable DATABASE_URL not found for substitution\n2025-09-12 15:46:12,443 - src.bot.pipeline.commands.ask.config - INFO - 🔧 Configuration loaded from environment\n2025-09-12 15:46:12,443 - src.bot.pipeline.commands.ask.config - INFO - ✅ Configuration validation passed\n2025-09-12 15:46:12,582 - src.bot.pipeline.commands.ask.stages.ml_sentiment_analyzer - INFO - Sentiment model loaded successfully\n2025-09-12 15:46:12,589 - src.bot.pipeline.commands.ask.stages.symbol_validator - INFO - Loaded 6659 valid tickers\n2025-09-12 15:46:13,495 - src.api.data.providers.data_source_manager - INFO - ✅ Yahoo Finance provider initialized\n2025-09-12 15:46:13,495 - src.api.data.providers.data_source_manager - INFO - ✅ Polygon provider initialized\n2025-09-12 15:46:13,495 - src.api.data.providers.data_source_manager - INFO - ✅ Finnhub provider initialized\n2025-09-12 15:46:13,495 - src.api.data.providers.data_source_manager - INFO - ✅ Initialized 3 data providers\n2025-09-12 15:46:13,502 - deprecation - WARNING - Deprecated module src.shared.market_analysis.signal_analyzer used by <frozen importlib._bootstrap>:488\n2025-09-12 15:46:13,508 - src.api.data.providers.data_source_manager - INFO - ✅ Yahoo Finance provider initialized\n2025-09-12 15:46:13,508 - src.api.data.providers.data_source_manager - INFO - ✅ Polygon provider initialized\n2025-09-12 15:46:13,508 - src.api.data.providers.data_source_manager - INFO - ✅ Finnhub provider initialized\n2025-09-12 15:46:13,508 - src.api.data.providers.data_source_manager - INFO - ✅ Initialized 3 data providers\n2025-09-12 15:46:13,513 - src.shared.data_providers.aggregator - WARNING - Alpaca provider module not found, skipping\n2025-09-12 15:46:13,514 - src.shared.data_providers.polygon_provider - INFO - Polygon provider initialized successfully\n2025-09-12 15:46:13,514 - src.shared.data_providers.aggregator - INFO - Initialized data provider: polygon\n2025-09-12 15:46:13,514 - src.shared.data_providers.aggregator - WARNING - Unknown provider: alpaca\n2025-09-12 15:46:13,514 - src.shared.data_providers.aggregator - INFO - Initialized data provider: alpha_vantage\n2025-09-12 15:46:13,514 - src.shared.data_providers.aggregator - INFO - Initialized data provider: yfinance\n2025-09-12 15:46:13,514 - src.shared.data_providers.fallback_provider - INFO - Fallback provider initialized\n2025-09-12 15:46:13,514 - src.shared.data_providers.aggregator - INFO - Initialized data provider: fallback\n2025-09-12 15:46:13,514 - src.shared.technical_analysis.signal_generator - INFO - SignalGenerator initialized\ncollected 18 items / 10 errors\n\n==================================== ERRORS ====================================\n______ ERROR collecting tests/integration/test_alpha_vantage_provider.py _______\nImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/integration/test_alpha_vantage_provider.py'.\nHint: make sure your test modules/packages have valid Python names.\nTraceback:\nvenv/lib/python3.12/site-packages/_pytest/python.py:498: in importtestmodule\n    mod = import_path(\nvenv/lib/python3.12/site-packages/_pytest/pathlib.py:587: in import_path\n    importlib.import_module(module_name)\n/usr/lib/python3.12/importlib/__init__.py:90: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n<frozen importlib._bootstrap>:1387: in _gcd_import\n    ???\n<frozen importlib._bootstrap>:1360: in _find_and_load\n    ???\n<frozen importlib._bootstrap>:1331: in _find_and_load_unlocked\n    ???\n<frozen importlib._bootstrap>:935: in _load_unlocked\n    ???\nvenv/lib/python3.12/site-packages/_pytest/assertion/rewrite.py:186: in exec_module\n    exec(co, module.__dict__)\ntests/integration/test_alpha_vantage_provider.py:9: in <module>\n    from src.core.exceptions import MarketDataError\nE   ImportError: cannot import name 'MarketDataError' from 'src.core.exceptions' (/home/<USER>/Desktop/tradingview-automatio/src/core/exceptions.py)\n______ ERROR collecting tests/integration/test_alpha_vantage_provider.py _______\nImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/integration/test_alpha_vantage_provider.py'.\nHint: make sure your test modules/packages have valid Python names.\nTraceback:\nvenv/lib/python3.12/site-packages/_pytest/python.py:498: in importtestmodule\n    mod = import_path(\nvenv/lib/python3.12/site-packages/_pytest/pathlib.py:587: in import_path\n    importlib.import_module(module_name)\n/usr/lib/python3.12/importlib/__init__.py:90: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n<frozen importlib._bootstrap>:1387: in _gcd_import\n    ???\n<frozen importlib._bootstrap>:1360: in _find_and_load\n    ???\n<frozen importlib._bootstrap>:1331: in _find_and_load_unlocked\n    ???\n<frozen importlib._bootstrap>:935: in _load_unlocked\n    ???\nvenv/lib/python3.12/site-packages/_pytest/assertion/rewrite.py:186: in exec_module\n    exec(co, module.__dict__)\ntests/integration/test_alpha_vantage_provider.py:9: in <module>\n    from src.core.exceptions import MarketDataError\nE   ImportError: cannot import name 'MarketDataError' from 'src.core.exceptions' (/home/<USER>/Desktop/tradingview-automatio/src/core/exceptions.py)\n________ ERROR collecting tests/integration/test_market_data_service.py ________\nImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/integration/test_market_data_service.py'.\nHint: make sure your test modules/packages have valid Python names.\nTraceback:\nvenv/lib/python3.12/site-packages/_pytest/python.py:498: in importtestmodule\n    mod = import_path(\nvenv/lib/python3.12/site-packages/_pytest/pathlib.py:587: in import_path\n    importlib.import_module(module_name)\n/usr/lib/python3.12/importlib/__init__.py:90: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n<frozen importlib._bootstrap>:1387: in _gcd_import\n    ???\n<frozen importlib._bootstrap>:1360: in _find_and_load\n    ???\n<frozen importlib._bootstrap>:1331: in _find_and_load_unlocked\n    ???\n<frozen importlib._bootstrap>:935: in _load_unlocked\n    ???\nvenv/lib/python3.12/site-packages/_pytest/assertion/rewrite.py:186: in exec_module\n    exec(co, module.__dict__)\ntests/integration/test_market_data_service.py:7: in <module>\n    from src.core.exceptions import MarketDataError\nE   ImportError: cannot import name 'MarketDataError' from 'src.core.exceptions' (/home/<USER>/Desktop/tradingview-automatio/src/core/exceptions.py)\n________ ERROR collecting tests/integration/test_market_data_service.py ________\nImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/integration/test_market_data_service.py'.\nHint: make sure your test modules/packages have valid Python names.\nTraceback:\nvenv/lib/python3.12/site-packages/_pytest/python.py:498: in importtestmodule\n    mod = import_path(\nvenv/lib/python3.12/site-packages/_pytest/pathlib.py:587: in import_path\n    importlib.import_module(module_name)\n/usr/lib/python3.12/importlib/__init__.py:90: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n<frozen importlib._bootstrap>:1387: in _gcd_import\n    ???\n<frozen importlib._bootstrap>:1360: in _find_and_load\n    ???\n<frozen importlib._bootstrap>:1331: in _find_and_load_unlocked\n    ???\n<frozen importlib._bootstrap>:935: in _load_unlocked\n    ???\nvenv/lib/python3.12/site-packages/_pytest/assertion/rewrite.py:186: in exec_module\n    exec(co, module.__dict__)\ntests/integration/test_market_data_service.py:7: in <module>\n    from src.core.exceptions import MarketDataError\nE   ImportError: cannot import name 'MarketDataError' from 'src.core.exceptions' (/home/<USER>/Desktop/tradingview-automatio/src/core/exceptions.py)\n_________ ERROR collecting tests/integration/test_polygon_provider.py __________\nImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/integration/test_polygon_provider.py'.\nHint: make sure your test modules/packages have valid Python names.\nTraceback:\nvenv/lib/python3.12/site-packages/_pytest/python.py:498: in importtestmodule\n    mod = import_path(\nvenv/lib/python3.12/site-packages/_pytest/pathlib.py:587: in import_path\n    importlib.import_module(module_name)\n/usr/lib/python3.12/importlib/__init__.py:90: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n<frozen importlib._bootstrap>:1387: in _gcd_import\n    ???\n<frozen importlib._bootstrap>:1360: in _find_and_load\n    ???\n<frozen importlib._bootstrap>:1331: in _find_and_load_unlocked\n    ???\n<frozen importlib._bootstrap>:935: in _load_unlocked\n    ???\nvenv/lib/python3.12/site-packages/_pytest/assertion/rewrite.py:186: in exec_module\n    exec(co, module.__dict__)\ntests/integration/test_polygon_provider.py:5: in <module>\n    from src.api.data.providers.polygon import PolygonProvider\nsrc/api/data/providers/polygon.py:10: in <module>\n    from src.data.providers.base import (\nE   ImportError: cannot import name 'UnifiedDataProvider' from 'src.data.providers.base' (/home/<USER>/Desktop/tradingview-automatio/src/data/providers/base.py)\n_________ ERROR collecting tests/integration/test_polygon_provider.py __________\nImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/integration/test_polygon_provider.py'.\nHint: make sure your test modules/packages have valid Python names.\nTraceback:\nvenv/lib/python3.12/site-packages/_pytest/python.py:498: in importtestmodule\n    mod = import_path(\nvenv/lib/python3.12/site-packages/_pytest/pathlib.py:587: in import_path\n    importlib.import_module(module_name)\n/usr/lib/python3.12/importlib/__init__.py:90: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n<frozen importlib._bootstrap>:1387: in _gcd_import\n    ???\n<frozen importlib._bootstrap>:1360: in _find_and_load\n    ???\n<frozen importlib._bootstrap>:1331: in _find_and_load_unlocked\n    ???\n<frozen importlib._bootstrap>:935: in _load_unlocked\n    ???\nvenv/lib/python3.12/site-packages/_pytest/assertion/rewrite.py:186: in exec_module\n    exec(co, module.__dict__)\ntests/integration/test_polygon_provider.py:5: in <module>\n    from src.api.data.providers.polygon import PolygonProvider\nsrc/api/data/providers/polygon.py:10: in <module>\n    from src.data.providers.base import (\nE   ImportError: cannot import name 'UnifiedDataProvider' from 'src.data.providers.base' (/home/<USER>/Desktop/tradingview-automatio/src/data/providers/base.py)\n______ ERROR collecting tests/integration/test_qqq_options_estimation.py _______\nImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/integration/test_qqq_options_estimation.py'.\nHint: make sure your test modules/packages have valid Python names.\nTraceback:\nvenv/lib/python3.12/site-packages/_pytest/python.py:498: in importtestmodule\n    mod = import_path(\nvenv/lib/python3.12/site-packages/_pytest/pathlib.py:587: in import_path\n    importlib.import_module(module_name)\n/usr/lib/python3.12/importlib/__init__.py:90: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n<frozen importlib._bootstrap>:1387: in _gcd_import\n    ???\n<frozen importlib._bootstrap>:1360: in _find_and_load\n    ???\n<frozen importlib._bootstrap>:1331: in _find_and_load_unlocked\n    ???\n<frozen importlib._bootstrap>:935: in _load_unlocked\n    ???\nvenv/lib/python3.12/site-packages/_pytest/assertion/rewrite.py:186: in exec_module\n    exec(co, module.__dict__)\ntests/integration/test_qqq_options_estimation.py:20: in <module>\n    from src.api.data.providers.finnhub import FinnhubProvider\nsrc/api/data/providers/finnhub.py:8: in <module>\n    from src.data.providers.base import (\nE   ImportError: cannot import name 'UnifiedDataProvider' from 'src.data.providers.base' (/home/<USER>/Desktop/tradingview-automatio/src/data/providers/base.py)\n______ ERROR collecting tests/integration/test_qqq_options_estimation.py _______\nImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/integration/test_qqq_options_estimation.py'.\nHint: make sure your test modules/packages have valid Python names.\nTraceback:\nvenv/lib/python3.12/site-packages/_pytest/python.py:498: in importtestmodule\n    mod = import_path(\nvenv/lib/python3.12/site-packages/_pytest/pathlib.py:587: in import_path\n    importlib.import_module(module_name)\n/usr/lib/python3.12/importlib/__init__.py:90: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n<frozen importlib._bootstrap>:1387: in _gcd_import\n    ???\n<frozen importlib._bootstrap>:1360: in _find_and_load\n    ???\n<frozen importlib._bootstrap>:1331: in _find_and_load_unlocked\n    ???\n<frozen importlib._bootstrap>:935: in _load_unlocked\n    ???\nvenv/lib/python3.12/site-packages/_pytest/assertion/rewrite.py:186: in exec_module\n    exec(co, module.__dict__)\ntests/integration/test_qqq_options_estimation.py:20: in <module>\n    from src.api.data.providers.finnhub import FinnhubProvider\nsrc/api/data/providers/finnhub.py:8: in <module>\n    from src.data.providers.base import (\nE   ImportError: cannot import name 'UnifiedDataProvider' from 'src.data.providers.base' (/home/<USER>/Desktop/tradingview-automatio/src/data/providers/base.py)\n________ ERROR collecting tests/integration/test_supertrend_analysis.py ________\nImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/integration/test_supertrend_analysis.py'.\nHint: make sure your test modules/packages have valid Python names.\nTraceback:\nvenv/lib/python3.12/site-packages/_pytest/python.py:498: in importtestmodule\n    mod = import_path(\nvenv/lib/python3.12/site-packages/_pytest/pathlib.py:587: in import_path\n    importlib.import_module(module_name)\n/usr/lib/python3.12/importlib/__init__.py:90: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n<frozen importlib._bootstrap>:1387: in _gcd_import\n    ???\n<frozen importlib._bootstrap>:1360: in _find_and_load\n    ???\n<frozen importlib._bootstrap>:1331: in _find_and_load_unlocked\n    ???\n<frozen importlib._bootstrap>:935: in _load_unlocked\n    ???\nvenv/lib/python3.12/site-packages/_pytest/assertion/rewrite.py:186: in exec_module\n    exec(co, module.__dict__)\ntests/integration/test_supertrend_analysis.py:8: in <module>\n    from src.api.data.providers.finnhub import FinnhubProvider\nsrc/api/data/providers/finnhub.py:8: in <module>\n    from src.data.providers.base import (\nE   ImportError: cannot import name 'UnifiedDataProvider' from 'src.data.providers.base' (/home/<USER>/Desktop/tradingview-automatio/src/data/providers/base.py)\n________ ERROR collecting tests/integration/test_supertrend_analysis.py ________\nImportError while importing test module '/home/<USER>/Desktop/tradingview-automatio/tests/integration/test_supertrend_analysis.py'.\nHint: make sure your test modules/packages have valid Python names.\nTraceback:\nvenv/lib/python3.12/site-packages/_pytest/python.py:498: in importtestmodule\n    mod = import_path(\nvenv/lib/python3.12/site-packages/_pytest/pathlib.py:587: in import_path\n    importlib.import_module(module_name)\n/usr/lib/python3.12/importlib/__init__.py:90: in import_module\n    return _bootstrap._gcd_import(name[level:], package, level)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n<frozen importlib._bootstrap>:1387: in _gcd_import\n    ???\n<frozen importlib._bootstrap>:1360: in _find_and_load\n    ???\n<frozen importlib._bootstrap>:1331: in _find_and_load_unlocked\n    ???\n<frozen importlib._bootstrap>:935: in _load_unlocked\n    ???\nvenv/lib/python3.12/site-packages/_pytest/assertion/rewrite.py:186: in exec_module\n    exec(co, module.__dict__)\ntests/integration/test_supertrend_analysis.py:8: in <module>\n    from src.api.data.providers.finnhub import FinnhubProvider\nsrc/api/data/providers/finnhub.py:8: in <module>\n    from src.data.providers.base import (\nE   ImportError: cannot import name 'UnifiedDataProvider' from 'src.data.providers.base' (/home/<USER>/Desktop/tradingview-automatio/src/data/providers/base.py)\n=============================== warnings summary ===============================\nsrc/shared/market_analysis/__init__.py:10\n  /home/<USER>/Desktop/tradingview-automatio/src/shared/market_analysis/__init__.py:10: DeprecationWarning: This module is deprecated. Import from src.shared.market_analysis.unified_signal_analyzer instead.\n    from .signal_analyzer import SignalAnalyzer\n\nsrc/shared/database/supabase_sdk_client.py:22\n  /home/<USER>/Desktop/tradingview-automatio/src/shared/database/supabase_sdk_client.py:22: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/\n    @validator('url')\n\nsrc/shared/database/supabase_sdk_client.py:36\n  /home/<USER>/Desktop/tradingview-automatio/src/shared/database/supabase_sdk_client.py:36: PydanticDeprecatedSince20: Pydantic V1 style `@validator` validators are deprecated. You should migrate to Pydantic V2 style `@field_validator` validators, see the migration guide for more details. Deprecated in Pydantic V2.0 to be removed in V3.0. See Pydantic V2 Migration Guide at https://errors.pydantic.dev/2.11/migration/\n    @validator('key')\n\n-- Docs: https://docs.pytest.org/en/stable/how-to/capture-warnings.html\n\n🤖 AI Trading Bot Test Summary 🤖\n----------------------------------------\nPython Version: 3.12.3\nSupabase Configured: Yes\nDiscord Bot Token: Configured\n----------------------------------------\n=========================== short test summary info ============================\nERROR tests/integration/test_alpha_vantage_provider.py\nERROR tests/integration/test_alpha_vantage_provider.py\nERROR tests/integration/test_market_data_service.py\nERROR tests/integration/test_market_data_service.py\nERROR tests/integration/test_polygon_provider.py\nERROR tests/integration/test_polygon_provider.py\nERROR tests/integration/test_qqq_options_estimation.py\nERROR tests/integration/test_qqq_options_estimation.py\nERROR tests/integration/test_supertrend_analysis.py\nERROR tests/integration/test_supertrend_analysis.py\n!!!!!!!!!!!!!!!!!!! Interrupted: 10 errors during collection !!!!!!!!!!!!!!!!!!!\n======================== 3 warnings, 10 errors in 3.46s ========================\n", "metadata": {"operation_id": "OP_000044", "operation_type": "DATA_FLOW", "source": "PYTEST", "destination": "STDOUT", "operation": "INTEGRATION_TESTS_OUTPUT", "data_type": "str", "data_size": 19562, "data_preview": "============================= test session starts ==============================\nplatform linux -- Python 3.12.3, pytest-8.4.2, pluggy-1.6.0 -- /home/<USER>/Desktop/tradingview-automatio/venv/bin/python\ncachedir: .pytest_cache\nrootdir: /home/<USER>/Desktop/tradingview-automatio\nconfigfile: pytest.ini\nplugins: asyncio-1.2.0, cov-7.0.0, anyio-4.10.0\nasyncio: mode=Mode.AUTO, debug=False, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function\ncollecting ... \n--------------------...", "timestamp": "2025-09-12T15:46:15.936983", "session_id": "test_session_1757706366"}}