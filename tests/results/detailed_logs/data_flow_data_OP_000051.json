{"operation_id": "OP_000051", "data": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/coverage/report_core.py:107: CoverageWarning: Couldn't parse Python file '/home/<USER>/Desktop/tradingview-automatio/src/core/automation/report_engine.py' (couldnt-parse)\n  coverage._warn(msg, slug=\"couldnt-parse\")\n/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/coverage/report_core.py:107: CoverageWarning: Couldn't parse Python file '/home/<USER>/Desktop/tradingview-automatio/src/shared/background/tasks/market_intelligence.py' (couldnt-parse)\n  coverage._warn(msg, slug=\"couldnt-parse\")\n", "metadata": {"operation_id": "OP_000051", "operation_type": "DATA_FLOW", "source": "PYTEST", "destination": "STDERR", "operation": "COMPREHENSIVE_TESTS_ERRORS", "data_type": "str", "data_size": 586, "data_preview": "/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/coverage/report_core.py:107: CoverageWarning: Couldn't parse Python file '/home/<USER>/Desktop/tradingview-automatio/src/core/automation/report_engine.py' (couldnt-parse)\n  coverage._warn(msg, slug=\"couldnt-parse\")\n/home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/coverage/report_core.py:107: CoverageWarning: Couldn't parse Python file '/home/<USER>/Desktop/tradingview-automatio/src/shared/background/task...", "timestamp": "2025-09-12T15:46:22.763863", "session_id": "test_session_1757706366"}}