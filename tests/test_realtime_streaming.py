#!/usr/bin/env python3
"""
Test Real-Time Data Streaming System
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_realtime_streaming():
    """Test real-time data streaming"""
    print("🧪 Testing Real-Time Data Streaming...")
    
    try:
        from bot.real_time_data_stream import real_time_streamer, DataSource, MarketData
        
        # Initialize
        await real_time_streamer.initialize()
        print("✅ Streamer initialized")
        
        # Test data callback
        received_data = []
        
        async def test_callback(data: MarketData):
            received_data.append(data)
            print(f"📊 Received: {data.symbol} @ ${data.price:.2f} from {data.source.value}")
        
        # Test subscription (this will use polling since we don't have real API keys)
        success = await real_time_streamer.subscribe_to_symbol(
            "AAPL", test_callback, [DataSource.YAHOO_FINANCE]
        )
        
        if success:
            print("✅ Subscription successful")
        else:
            print("❌ Subscription failed")
        
        # Test health check
        health = await real_time_streamer.get_stream_health()
        print(f"✅ Stream health: {health['active_streams']} active streams")
        
        # Wait a bit to see if we get any data
        print("⏳ Waiting for data (5 seconds)...")
        await asyncio.sleep(5)
        
        print(f"📈 Received {len(received_data)} data points")
        
        # Cleanup
        await real_time_streamer.cleanup()
        print("✅ Cleanup completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Real-time streaming test failed: {e}")
        return False

async def main():
    """Run real-time streaming test"""
    print("🚀 Testing Real-Time Data Streaming System")
    print("=" * 50)
    
    success = await test_realtime_streaming()
    
    print("=" * 50)
    if success:
        print("🎉 REAL-TIME STREAMING TEST PASSED!")
    else:
        print("❌ Real-time streaming test failed")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
