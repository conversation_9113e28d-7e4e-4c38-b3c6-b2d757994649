"""
Tests for data processing with legacy code disabled
"""

import os
import sys
import pytest
from unittest.mock import patch, MagicMock

# Add src directory to path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))

# Import test configuration
from test_config import disable_legacy_code, enable_legacy_code

# Import components to test
from src.parser import UnifiedParser
from src.storage_manager import StorageManager


class TestNoLegacyDataProcessing:
    """Test suite for data processing with legacy code disabled"""
    
    def setup_method(self):
        """Set up test environment with legacy code disabled"""
        # Disable all legacy code
        self.patchers = disable_legacy_code()
        
        # Create test instances
        self.parser = UnifiedParser()
        self.storage_manager = StorageManager()
        
        # Sample data for testing
        self.emoji_text = "🟢 LONG TRADE ALERT $AAPL (1h) Entry: 150.00 TP1: 155.00 TP2: 160.00 TP3: 165.00 SL: 145.00"
        self.pipe_text = "AAPL|LONG_ENTRY|1625097600|1h|150.00|155.00|160.00|165.00|145.00"
        self.uniform_text = "AAPL|LONG_ENTRY|1625097600|1h|150.00|155.00|160.00|165.00|145.00"
        self.webhook_data = {"raw_text": self.pipe_text}
        self.structured_data = {
            "symbol": "AAPL",
            "signal_type": "LONG_ENTRY",
            "timestamp": 1625097600,
            "timeframe": "1h",
            "entry_price": 150.00,
            "tp1_price": 155.00,
            "tp2_price": 160.00,
            "tp3_price": 165.00,
            "sl_price": 145.00
        }
    
    def teardown_method(self):
        """Clean up test environment"""
        # Re-enable legacy code
        enable_legacy_code(self.patchers)
    
    def test_legacy_parser_disabled(self):
        """Test that legacy parser is disabled"""
        # Legacy emoji format should be rejected
        with pytest.raises(ValueError, match="Legacy parsers are disabled"):
            self.parser.parse_text_alert(self.emoji_text)
        
        # Uniform format should still work
        result = self.parser.parse_text_alert(self.uniform_text)
        assert result is not None
        assert result.symbol == "AAPL"
        assert result.signal == "LONG_ENTRY"
    
    @patch('src.storage_manager.StorageManager.store_webhook_alert')
    def test_legacy_storage_disabled(self, mock_store):
        """Test that legacy storage is disabled and redirected"""
        # Configure mock
        mock_store.return_value = True
        
        # Legacy storage method should redirect to modern method
        self.storage_manager._store_webhook_alert_legacy(self.webhook_data)
        
        # Verify that modern method was called
        mock_store.assert_called_once_with(self.webhook_data)
    
    def test_structured_data_processing(self):
        """Test processing of structured data without legacy code"""
        # Create a mock for the parser
        with patch.object(self.parser, 'parse_webhook_data') as mock_parse:
            mock_parse.return_value = self.structured_data
            
            # Process structured data
            result = self.parser.parse_data(self.structured_data)
            
            # Verify that structured data was processed correctly
            assert result == self.structured_data
            mock_parse.assert_called_once_with(self.structured_data)
    
    @patch('src.storage_manager.StorageManager.db_pool')
    def test_webhook_storage(self, mock_db_pool):
        """Test storage of webhook data without legacy code"""
        # Configure mock
        mock_db_pool = MagicMock()
        mock_db_pool.acquire.return_value.__aenter__.return_value.execute.return_value = True
        
        # Store webhook data
        with patch.object(self.storage_manager, 'store_webhook_alert') as mock_store:
            mock_store.return_value = True
            
            # Call the method
            result = self.storage_manager.store_webhook_alert(self.structured_data)
            
            # Verify that data was stored correctly
            assert result is True
            mock_store.assert_called_once_with(self.structured_data)


if __name__ == "__main__":
    pytest.main(["-v", "test_no_legacy_data_processing.py"])
