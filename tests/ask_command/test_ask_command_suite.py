#!/usr/bin/env python3
"""
Comprehensive test suite for the /ask command and its pipeline.

This file consolidates multiple test files related to the 'ask' command
to provide a single point of testing for this feature.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
from dataclasses import dataclass
from typing import Dict, Any

import pytest
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add project root to path
sys.path.insert(0, str(Path(__file__).resolve().parents[2]))

from src.bot.pipeline.commands.ask.pipeline import AskPipeline, PipelineResult
from src.bot.pipeline.commands.ask.config import AskConfig
from src.bot.pipeline.commands.ask.stages.intent_detector import IntentResult
from src.bot.pipeline.commands.ask.stages.tool_orchestrator import Tool<PERSON>esult
from src.bot.pipeline.commands.ask.stages.response_generator import ResponseResult
from src.bot.pipeline.commands.ask.stages.formatter import FormattedResponse
from src.bot.pipeline.commands.ask.error_handling.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from src.bot.pipeline.commands.ask.audit import create_audit_logger
from src.bot.pipeline.commands.ask.executor import execute_ask_pipeline
from src.bot.core.bot import create_bot
from src.bot.pipeline.commands.ask.stages.proactive_response import ProactiveResponseGenerator


# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class MockDiscordContext:
    """Mock Discord context for testing"""
    user_id: str = "test_user_123"
    channel_id: str = "test_channel_456"
    guild_id: str = "test_guild_789"

    async def send(self, content=None, embed=None):
        """Mock send method"""
        return {"content": content, "embed": embed}


# --- Test Cases ---

async def test_ask_command_registration():
    """Test if ask command is properly registered"""
    print("🔍 Testing /ask command registration...")

    try:
        bot = create_bot()
        print("✅ Bot created successfully")

        await bot._load_extensions()
        print("✅ Extensions loaded")

        ask_command = next((cmd for cmd in bot.bot.tree.walk_commands() if cmd.name == 'ask'), None)

        if ask_command:
            print("✅ /ask command found in command tree!")
            assert hasattr(ask_command, 'callback'), "❌ /ask command missing callback function"
            return True
        else:
            all_commands = [cmd.name for cmd in bot.bot.tree.walk_commands()]
            print(f"❌ /ask command NOT found in command tree. Available commands: {all_commands}")
            return False

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_comprehensive_pipeline():
    """Test the ASK pipeline with various query types"""
    print("🧪 Comprehensive ASK Pipeline Test")
    pipeline = AskPipeline()
    test_cases = [
        {"name": "Casual Greeting", "query": "Hello! How are you today?", "expected_intent": "casual"},
        {"name": "Help Request", "query": "What can you help me with?", "expected_intent": "casual"},
        {"name": "Stock Price Query", "query": "What's the current price of AAPL stock?", "expected_intent": "data_needed"},
        {"name": "Technical Analysis", "query": "Can you analyze the chart patterns for Tesla?", "expected_intent": "data_needed"},
    ]

    results = []
    for test_case in test_cases:
        result = await pipeline.process(query=test_case['query'], user_id=f"test_user_{test_case['name']}")
        results.append(result.success)

    assert all(results), "Not all comprehensive pipeline tests passed"


async def test_ask_pipeline_debugging():
    """Test the ask pipeline directly for debugging purposes"""
    print("🧪 Testing Ask Pipeline Debug...")
    pipeline = AskPipeline()
    test_query = "What is the price of AAPL?"
    result = await pipeline.process(query=test_query, user_id="test_user", correlation_id="test_correlation")
    assert result.success, f"Pipeline processing failed with error: {result.error}"


class TestASKPipelineIntegration:
    """Integration tests for the complete ASK pipeline"""

    @pytest.fixture
    async def pipeline(self):
        """Create ASK pipeline for testing"""
        pipeline = AskPipeline()
        await pipeline.initialize()
        return pipeline

    @pytest.fixture
    def mock_context(self):
        """Create mock Discord context"""
        return MockDiscordContext()

    @pytest.mark.asyncio
    async def test_complete_pipeline_casual_query(self, pipeline, mock_context):
        """Test complete pipeline with casual query"""
        query = "Hello! How are you today?"
        result = await pipeline.process(query, mock_context, "test_casual_001")
        assert isinstance(result, FormattedResponse)

    @pytest.mark.asyncio
    async def test_complete_pipeline_data_query(self, pipeline, mock_context):
        """Test complete pipeline with data-needed query"""
        query = "What's the current price of AAPL?"
        with patch('src.bot.pipeline.commands.ask.stages.tool_orchestrator.MCPClient') as mock_mcp:
            mock_mcp.return_value.execute_tool.return_value = {"symbol": "AAPL", "price": 150.25}
            result = await pipeline.process(query, mock_context, "test_data_001")
            assert isinstance(result, FormattedResponse)
            assert "AAPL" in str(result) or "150" in str(result)


def test_simple_ask_query():
    """Test a simple ask query"""
    print("🧪 Testing Simple Ask Query...")
    pipeline = AskPipeline()
    test_query = "What is the current price of Apple stock?"
    result = await pipeline.process(query=test_query, user_id="test_user", correlation_id="test_correlation")
    assert result.success


def test_ask_with_mock_data():
    """Test ask pipeline with mock data to avoid rate limits"""
    print("🧪 Testing Ask Pipeline with Mock Data...")
    pipeline = AskPipeline()
    test_query = "What is the current price of Apple stock?"
    result = await pipeline.process(query=test_query, user_id="test_user", correlation_id="test_correlation")
    assert result.success
    assert "temporarily limited" not in result.response and "reduced functionality" not in result.response


def test_ask2_transformation():
    """Test ASK2 transformation with before/after comparison"""
    print("🚀 ASK2 Transformation Validation")
    generator = ProactiveResponseGenerator()
    test_scenarios = [
        {"name": "Market Query Without Data", "query": "What's happening with the market today?", "intent": "market_overview", "entities": {"symbols": []}, "confidence": 0.8, "tool_result": None},
        {"name": "Stock Price Query", "query": "What's AAPL price?", "intent": "price_check", "entities": {"symbols": ["AAPL"]}, "confidence": 0.9, "tool_result": None},
    ]

    for scenario in test_scenarios:
        intent_result = IntentResult(intent=scenario["intent"], confidence=scenario["confidence"], reasoning="", execution_time=0.1)
        intent_result.entities = scenario["entities"]
        result = await generator.generate_proactive_response(scenario["query"], intent_result, scenario["tool_result"])
        assert result.immediate_value_provided


def test_ask_audit_logging():
    """Test the ASK command with comprehensive audit logging"""
    print("🔍 Testing ASK Command with Comprehensive Audit Logging")
    query = "What's the current price of AAPL?"
    correlation_id = f"test_audit_{int(asyncio.get_event_loop().time())}"
    audit_logger = create_audit_logger(correlation_id=correlation_id, user_id="test_user_123", query=query)
    result = await execute_ask_pipeline(query=query, user_id="test_user_123", username="TestUser", guild_id="test_guild_456", correlation_id=correlation_id)
    audit_summary = audit_logger.get_audit_summary()
    assert audit_summary['total_steps'] > 0


# --- Main Execution ---

def main():
    """Run all tests"""
    tests = [
        test_ask_command_registration,
        test_comprehensive_pipeline,
        test_ask_pipeline_debugging,
        test_simple_ask_query,
        test_ask_with_mock_data,
        test_ask2_transformation,
        test_ask_audit_logging,
    ]

    results = []
    for test_func in tests:
        try:
            print(f"\n--- Running {test_func.__name__} ---")
            success = await test_func()
            print(f"--- Result: {'✅ PASS' if success else '❌ FAIL'} ---")
            results.append(success)
        except Exception as e:
            print(f"--- ❌ ERROR in {test_func.__name__}: {e} ---")
            results.append(False)

    # Pytest integration tests
    print("\n--- Running Pytest Integration Tests ---")
    exit_code = pytest.main([__file__, "-k", "TestASKPipelineIntegration", "-v"])
    results.append(exit_code == 0)


    print("\n\n--- 📊 Overall Test Summary 📊 ---")
    total_tests = len(results)
    passed_tests = sum(1 for r in results if r)
    print(f"Total tests run: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print("--- ------------------- ---")

    if all(results):
        print("\n🎉🎉🎉 ALL ASK COMMAND TESTS PASSED! 🎉🎉🎉")
    else:
        print("\n🔥🔥🔥 SOME ASK COMMAND TESTS FAILED! 🔥🔥🔥")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
