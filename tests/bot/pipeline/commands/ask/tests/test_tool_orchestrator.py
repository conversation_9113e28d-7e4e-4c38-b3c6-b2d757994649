"""
Unit Tests for Tool Orchestration System

Tests MCP tool integration, parallel execution, and error handling.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from dataclasses import dataclass

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../../..'))

from src.bot.pipeline.commands.ask.stages.simplified_tool_orchestrator import (
    SimplifiedToolOrchestrator, ToolResult
)
from src.bot.pipeline.commands.ask.stages.intent_detector import IntentResult

class TestToolOrchestrator:
    """Test suite for ToolOrchestrator"""
    
    @pytest.fixture
    def tool_orchestrator(self):
        """Create ToolOrchestrator instance for testing"""
        return SimplifiedToolOrchestrator()
    
    @pytest.fixture
    def mock_intent_result(self):
        """Mock intent result for testing"""
        return IntentResult(
            intent="data_needed",
            confidence=0.9,
            reasoning="Query requires market data",
            entities={
                'symbols': ['AAPL'],
                'timeframes': ['daily'],
                'indicators': ['price']
            }
        )
    
    @pytest.fixture
    def mock_mcp_client(self):
        """Mock MCP client for testing"""
        mock_client = AsyncMock()
        mock_client.execute_tools.return_value = [
            {
                'tool': 'get_global_quote',
                'success': True,
                'data': {'symbol': 'AAPL', 'price': 150.00},
                'execution_time': 0.1
            }
        ]
        return mock_client
    
    @pytest.mark.asyncio
    async def test_tool_selection_for_price_query(self, tool_orchestrator, mock_intent_result):
        """Test tool selection for price queries"""
        query = "What's AAPL price?"
        
        tools = tool_orchestrator._select_tools(query, mock_intent_result)
        
        assert isinstance(tools, list)
        assert len(tools) > 0
        # Should include price-related tools
        tool_names = [tool['tool'] for tool in tools]
        assert any('quote' in tool.lower() or 'price' in tool.lower() for tool in tool_names)
    
    @pytest.mark.asyncio
    async def test_tool_selection_for_analysis_query(self, tool_orchestrator):
        """Test tool selection for analysis queries"""
        query = "Analyze TSLA technical indicators"
        intent_result = IntentResult(
            intent="data_needed",
            confidence=0.9,
            reasoning="Technical analysis required",
            entities={
                'symbols': ['TSLA'],
                'indicators': ['rsi', 'macd'],
                'timeframes': ['daily']
            }
        )
        
        tools = tool_orchestrator._select_tools(query, intent_result)
        
        assert isinstance(tools, list)
        assert len(tools) > 0
        # Should include technical analysis tools
        tool_names = [tool['tool'] for tool in tools]
        assert any('rsi' in tool.lower() or 'macd' in tool.lower() for tool in tool_names)
    
    @pytest.mark.asyncio
    async def test_parallel_tool_execution(self, tool_orchestrator, mock_intent_result):
        """Test parallel execution of multiple tools"""
        query = "Get AAPL price and RSI"
        
        with patch.object(tool_orchestrator.mcp_client, 'execute_tools') as mock_execute:
            mock_execute.return_value = [
                {
                    'tool': 'get_global_quote',
                    'success': True,
                    'data': {'symbol': 'AAPL', 'price': 150.00},
                    'execution_time': 0.1
                },
                {
                    'tool': 'get_rsi',
                    'success': True,
                    'data': {'symbol': 'AAPL', 'rsi': 65.5},
                    'execution_time': 0.15
                }
            ]
            
            result = await tool_orchestrator.orchestrate(query, mock_intent_result, "test_correlation")
            
            assert isinstance(result, ToolResult)
            assert result.success
            assert len(result.tool_results) == 2
            assert result.execution_time > 0
    
    @pytest.mark.asyncio
    async def test_tool_execution_with_failures(self, tool_orchestrator, mock_intent_result):
        """Test handling of tool execution failures"""
        query = "Get AAPL data"
        
        with patch.object(tool_orchestrator.mcp_client, 'execute_tools') as mock_execute:
            mock_execute.return_value = [
                {
                    'tool': 'get_global_quote',
                    'success': False,
                    'error': 'API rate limit exceeded',
                    'execution_time': 0.1
                },
                {
                    'tool': 'get_rsi',
                    'success': True,
                    'data': {'symbol': 'AAPL', 'rsi': 65.5},
                    'execution_time': 0.15
                }
            ]
            
            result = await tool_orchestrator.orchestrate(query, mock_intent_result, "test_correlation")
            
            assert isinstance(result, ToolResult)
            # Should still succeed if at least one tool works
            assert len(result.tool_results) == 2
            assert any(tr['success'] for tr in result.tool_results)
    
    @pytest.mark.asyncio
    async def test_no_tools_needed(self, tool_orchestrator):
        """Test handling when no tools are needed"""
        query = "Hello, how are you?"
        intent_result = IntentResult(
            intent="casual",
            confidence=0.9,
            reasoning="Casual greeting",
            entities={}
        )
        
        result = await tool_orchestrator.orchestrate(query, intent_result, "test_correlation")
        
        assert isinstance(result, ToolResult)
        assert result.success
        assert len(result.tool_results) == 0
        assert result.summary == "No tools executed - casual query"
    
    @pytest.mark.asyncio
    async def test_symbol_extraction_from_query(self, tool_orchestrator):
        """Test symbol extraction for tool parameters"""
        test_cases = [
            ("AAPL price", ["AAPL"]),
            ("Compare TSLA vs MSFT", ["TSLA", "MSFT"]),
            ("Bitcoin analysis", ["BTC"]),
            ("Market overview", [])
        ]
        
        for query, expected_symbols in test_cases:
            symbols = tool_orchestrator._extract_symbols(query)
            
            if expected_symbols:
                assert len(symbols) > 0
                # Check if at least one expected symbol is found
                assert any(symbol in symbols for symbol in expected_symbols)
    
    @pytest.mark.asyncio
    async def test_tool_timeout_handling(self, tool_orchestrator, mock_intent_result):
        """Test timeout handling in tool execution"""
        query = "Get AAPL price"
        
        with patch.object(tool_orchestrator.mcp_client, 'execute_tools') as mock_execute:
            # Simulate timeout
            mock_execute.side_effect = asyncio.TimeoutError("Tool execution timed out")
            
            result = await tool_orchestrator.orchestrate(query, mock_intent_result, "test_correlation")
            
            assert isinstance(result, ToolResult)
            # Should handle timeout gracefully
            assert not result.success or len(result.tool_results) == 0
    
    @pytest.mark.asyncio
    async def test_tool_result_aggregation(self, tool_orchestrator, mock_intent_result):
        """Test aggregation of tool results"""
        query = "Get comprehensive AAPL data"
        
        with patch.object(tool_orchestrator.mcp_client, 'execute_tools') as mock_execute:
            mock_execute.return_value = [
                {
                    'tool': 'get_global_quote',
                    'success': True,
                    'data': {'symbol': 'AAPL', 'price': 150.00, 'change': 2.5},
                    'execution_time': 0.1
                },
                {
                    'tool': 'get_company_overview',
                    'success': True,
                    'data': {'symbol': 'AAPL', 'name': 'Apple Inc.', 'sector': 'Technology'},
                    'execution_time': 0.2
                }
            ]
            
            result = await tool_orchestrator.orchestrate(query, mock_intent_result, "test_correlation")
            
            assert isinstance(result, ToolResult)
            assert result.success
            assert len(result.tool_results) == 2
            
            # Check aggregated data
            assert 'AAPL' in result.summary
            assert result.execution_time > 0
    
    def test_tool_priority_ordering(self, tool_orchestrator):
        """Test tool priority and ordering"""
        query = "AAPL price and analysis"
        intent_result = IntentResult(
            intent="data_needed",
            confidence=0.9,
            reasoning="Price and analysis needed",
            entities={'symbols': ['AAPL'], 'indicators': ['price', 'rsi']}
        )
        
        tools = tool_orchestrator._select_tools(query, intent_result)
        
        assert isinstance(tools, list)
        if len(tools) > 1:
            # Price tools should generally come first
            tool_names = [tool['tool'] for tool in tools]
            price_tools = [i for i, name in enumerate(tool_names) if 'quote' in name.lower() or 'price' in name.lower()]
            if price_tools:
                # At least one price tool should be early in the list
                assert min(price_tools) < len(tools) // 2
    
    @pytest.mark.asyncio
    async def test_error_recovery(self, tool_orchestrator, mock_intent_result):
        """Test error recovery mechanisms"""
        query = "Get AAPL data"
        
        with patch.object(tool_orchestrator.mcp_client, 'execute_tools') as mock_execute:
            # First call fails, second succeeds
            mock_execute.side_effect = [
                Exception("Network error"),
                [
                    {
                        'tool': 'get_global_quote',
                        'success': True,
                        'data': {'symbol': 'AAPL', 'price': 150.00},
                        'execution_time': 0.1
                    }
                ]
            ]
            
            # Should handle the exception gracefully
            result = await tool_orchestrator.orchestrate(query, mock_intent_result, "test_correlation")
            
            assert isinstance(result, ToolResult)
            # Should still return a result even if tools fail
    
    def test_tool_configuration_validation(self, tool_orchestrator):
        """Test tool configuration validation"""
        # Test with valid configuration
        valid_tools = [
            {'tool': 'get_global_quote', 'params': {'symbol': 'AAPL'}},
            {'tool': 'get_rsi', 'params': {'symbol': 'AAPL', 'interval': 'daily'}}
        ]
        
        for tool_config in valid_tools:
            # Should not raise exception
            assert 'tool' in tool_config
            assert isinstance(tool_config.get('params', {}), dict)

@pytest.mark.asyncio
async def test_tool_orchestrator_integration():
    """Integration test for tool orchestrator"""
    orchestrator = ToolOrchestrator()
    
    # Test with realistic intent result
    intent_result = IntentResult(
        intent="data_needed",
        confidence=0.9,
        reasoning="Stock price query",
        entities={'symbols': ['AAPL'], 'indicators': ['price']}
    )
    
    result = await orchestrator.orchestrate(
        "What's AAPL price?", 
        intent_result, 
        "integration_test"
    )
    
    assert isinstance(result, ToolResult)
    assert result.execution_time >= 0
    # Should handle the case where tools are not configured
    assert isinstance(result.tool_results, list)

if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
