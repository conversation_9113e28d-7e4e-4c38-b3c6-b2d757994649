"""
Unit Tests for Error Handling System

Tests error classification, fallback chains, and user message generation.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from dataclasses import dataclass

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../../..'))

from src.bot.pipeline.commands.ask.error_handling.simplified_error_handler import (
    SimplifiedError<PERSON><PERSON><PERSON>, ErrorResult, ErrorSeverity
)

class TestSimplifiedErrorHandler:
    """Test suite for SimplifiedErrorHandler"""
    
    @pytest.fixture
    def error_classifier(self):
        """Create ErrorClassifier instance for testing"""
        return ErrorClassifier()
    
    @pytest.fixture
    def error_context(self):
        """Create error context for testing"""
        return ErrorContext(
            stage="test_stage",
            correlation_id="test_correlation",
            user_query="test query",
            execution_time=0.1
        )
    
    def test_network_error_classification(self, error_classifier, error_context):
        """Test classification of network errors"""
        error = ConnectionError("Network connection failed")
        
        classified = error_classifier.classify_error(error, error_context)
        
        assert isinstance(classified, ClassifiedError)
        assert classified.error_type == ErrorType.NETWORK_ERROR
        assert classified.severity == ErrorSeverity.HIGH
        assert "connectivity" in classified.user_message.lower()
    
    def test_timeout_error_classification(self, error_classifier, error_context):
        """Test classification of timeout errors"""
        error = TimeoutError("Operation timed out")
        
        classified = error_classifier.classify_error(error, error_context)
        
        assert isinstance(classified, ClassifiedError)
        assert classified.error_type == ErrorType.TIMEOUT_ERROR
        assert classified.severity == ErrorSeverity.MEDIUM
    
    def test_validation_error_classification(self, error_classifier, error_context):
        """Test classification of validation errors"""
        error = ValueError("Invalid input provided")
        
        classified = error_classifier.classify_error(error, error_context)
        
        assert isinstance(classified, ClassifiedError)
        assert classified.error_type == ErrorType.VALIDATION_ERROR
        assert classified.severity == ErrorSeverity.LOW
    
    def test_unknown_error_classification(self, error_classifier, error_context):
        """Test classification of unknown errors"""
        error = RuntimeError("Unexpected error occurred")
        
        classified = error_classifier.classify_error(error, error_context)
        
        assert isinstance(classified, ClassifiedError)
        assert classified.error_type == ErrorType.UNKNOWN_ERROR
        assert classified.severity == ErrorSeverity.MEDIUM
    
    def test_severity_escalation_with_retries(self, error_classifier):
        """Test severity escalation with multiple retries"""
        error = ConnectionError("Network failed")
        context = ErrorContext(
            stage="test_stage",
            correlation_id="test_correlation",
            user_query="test query",
            execution_time=0.1,
            retry_count=3  # Multiple retries
        )
        
        classified = error_classifier.classify_error(error, context)
        
        # Severity should be escalated due to retries
        assert classified.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]
    
    def test_retry_recommendation(self, error_classifier, error_context):
        """Test retry recommendation logic"""
        # Network errors should recommend retry
        network_error = ConnectionError("Network failed")
        classified = error_classifier.classify_error(network_error, error_context)
        assert classified.retry_recommended is True
        
        # Validation errors should not recommend retry
        validation_error = ValueError("Invalid input")
        classified = error_classifier.classify_error(validation_error, error_context)
        assert classified.retry_recommended is False

class TestFallbackChain:
    """Test suite for FallbackChain"""
    
    @pytest.fixture
    def fallback_chain(self):
        """Create FallbackChain instance for testing"""
        return FallbackChain()
    
    @pytest.fixture
    def classified_error(self):
        """Create classified error for testing"""
        context = ErrorContext(
            stage="test_stage",
            correlation_id="test_correlation",
            user_query="What's AAPL price?",
            execution_time=0.1
        )
        
        return ClassifiedError(
            error_type=ErrorType.AI_SERVICE_UNAVAILABLE,
            severity=ErrorSeverity.MEDIUM,
            message="AI service unavailable",
            user_message="I'm having trouble with my AI services",
            recovery_strategy="use_fallback_response",
            retry_recommended=False,
            context=context,
            original_exception=Exception("AI failed"),
            timestamp=1234567890
        )
    
    @pytest.mark.asyncio
    async def test_static_response_fallback(self, fallback_chain, classified_error):
        """Test static response fallback strategy"""
        result = await fallback_chain._use_static_response(
            "What's AAPL price?", "test_correlation"
        )
        
        assert isinstance(result, FallbackResult)
        assert result.success is True
        assert len(result.response) > 0
        assert result.strategy_used == "use_static_response"
        assert result.confidence > 0
    
    @pytest.mark.asyncio
    async def test_cached_response_fallback(self, fallback_chain):
        """Test cached response fallback strategy"""
        result = await fallback_chain._use_cached_response(
            "What's AAPL price?", "test_correlation"
        )
        
        assert isinstance(result, FallbackResult)
        assert result.strategy_used == "use_cached_response"
        # May succeed or fail depending on cache availability
    
    @pytest.mark.asyncio
    async def test_fallback_chain_execution(self, fallback_chain, classified_error):
        """Test complete fallback chain execution"""
        result = await fallback_chain.execute_fallback(
            classified_error, "What's AAPL price?", "test_correlation"
        )
        
        assert isinstance(result, FallbackResult)
        assert result.success is True  # Should eventually succeed with static response
        assert len(result.response) > 0
        assert result.strategy_used is not None
    
    @pytest.mark.asyncio
    async def test_emergency_fallback(self, fallback_chain):
        """Test emergency fallback when all strategies fail"""
        result = await fallback_chain._emergency_fallback(
            "test query", "test_correlation", 0.1
        )
        
        assert isinstance(result, FallbackResult)
        assert result.success is True
        assert "technical difficulties" in result.response.lower()
        assert result.strategy_used == "emergency_fallback"
    
    def test_fallback_statistics(self, fallback_chain):
        """Test fallback statistics tracking"""
        # Simulate some fallback usage
        fallback_chain._update_stats(FallbackStrategy.USE_STATIC_RESPONSE, True, 0.1)
        fallback_chain._update_stats(FallbackStrategy.USE_CACHED_RESPONSE, False, 0.05)
        
        stats = fallback_chain.get_fallback_stats()
        
        assert isinstance(stats, dict)
        assert "use_static_response" in stats
        assert stats["use_static_response"]["success_rate"] == 1.0

class TestUserMessageGenerator:
    """Test suite for UserMessageGenerator"""
    
    @pytest.fixture
    def message_generator(self):
        """Create UserMessageGenerator instance for testing"""
        return UserMessageGenerator()
    
    @pytest.fixture
    def classified_error(self):
        """Create classified error for testing"""
        context = ErrorContext(
            stage="test_stage",
            correlation_id="test_correlation",
            user_query="What's AAPL price?",
            execution_time=0.1
        )
        
        return ClassifiedError(
            error_type=ErrorType.AI_SERVICE_UNAVAILABLE,
            severity=ErrorSeverity.MEDIUM,
            message="AI service unavailable",
            user_message="I'm having trouble with my AI services",
            recovery_strategy="use_fallback_response",
            retry_recommended=False,
            context=context,
            original_exception=Exception("AI failed"),
            timestamp=1234567890
        )
    
    @pytest.fixture
    def fallback_result(self):
        """Create fallback result for testing"""
        return FallbackResult(
            success=True,
            response="I can help with general trading information.",
            strategy_used="use_static_response",
            execution_time=0.001,
            confidence=0.6
        )
    
    def test_ai_service_error_message(self, message_generator, classified_error, fallback_result):
        """Test message generation for AI service errors"""
        message = message_generator.generate_response(
            classified_error, fallback_result, "What's AAPL price?"
        )
        
        assert isinstance(message, str)
        assert len(message) > 0
        # Should be user-friendly and not technical
        assert "AI" not in message or "service" not in message.lower()
    
    def test_rate_limit_error_message(self, message_generator, fallback_result):
        """Test message generation for rate limit errors"""
        context = ErrorContext(
            stage="test_stage",
            correlation_id="test_correlation",
            user_query="Analyze market",
            execution_time=0.1
        )
        
        classified_error = ClassifiedError(
            error_type=ErrorType.AI_RATE_LIMITED,
            severity=ErrorSeverity.MEDIUM,
            message="Rate limit exceeded",
            user_message="Too many requests",
            recovery_strategy="retry_with_backoff",
            retry_recommended=True,
            context=context,
            original_exception=Exception("Rate limited"),
            timestamp=1234567890
        )
        
        message = message_generator.generate_response(
            classified_error, fallback_result, "Analyze market"
        )
        
        assert isinstance(message, str)
        assert "wait" in message.lower() or "moment" in message.lower()
    
    def test_validation_error_message(self, message_generator, fallback_result):
        """Test message generation for validation errors"""
        context = ErrorContext(
            stage="test_stage",
            correlation_id="test_correlation",
            user_query="invalid query",
            execution_time=0.1
        )
        
        classified_error = ClassifiedError(
            error_type=ErrorType.VALIDATION_ERROR,
            severity=ErrorSeverity.LOW,
            message="Validation failed",
            user_message="Invalid input",
            recovery_strategy="return_validation_error",
            retry_recommended=False,
            context=context,
            original_exception=ValueError("Invalid"),
            timestamp=1234567890
        )
        
        message = message_generator.generate_response(
            classified_error, fallback_result, "invalid query"
        )
        
        assert isinstance(message, str)
        assert "rephrase" in message.lower() or "understand" in message.lower()
    
    def test_helpful_alternatives_generation(self, message_generator):
        """Test generation of helpful alternatives"""
        alternatives = message_generator._get_helpful_alternatives("What's AAPL price?")
        
        assert isinstance(alternatives, str)
        assert len(alternatives) > 0
        assert "•" in alternatives  # Should be formatted as bullet points
    
    def test_recovery_suggestions_generation(self, message_generator, classified_error):
        """Test generation of recovery suggestions"""
        suggestions = message_generator._get_recovery_suggestions(
            classified_error, "What's AAPL price?"
        )
        
        assert isinstance(suggestions, str)
        if len(suggestions) > 0:
            assert "•" in suggestions  # Should be formatted as bullet points

class TestErrorHandler:
    """Test suite for ErrorHandler"""
    
    @pytest.fixture
    def error_handler(self):
        """Create ErrorHandler instance for testing"""
        return ErrorHandler()
    
    @pytest.mark.asyncio
    async def test_complete_error_handling(self, error_handler):
        """Test complete error handling flow"""
        exception = ConnectionError("Network failed")
        
        result = await error_handler.handle_error(
            exception=exception,
            stage="test_stage",
            correlation_id="test_correlation",
            user_query="What's AAPL price?",
            execution_time=0.1
        )
        
        assert isinstance(result, ErrorResult)
        assert result.correlation_id == "test_correlation"
        assert result.user_friendly is True
        assert len(result.response) > 0
        assert result.execution_time >= 0
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_activation(self, error_handler):
        """Test circuit breaker activation"""
        # Simulate multiple errors to trigger circuit breaker
        for i in range(6):  # More than threshold
            await error_handler.handle_error(
                exception=Exception(f"Error {i}"),
                stage="test_stage",
                correlation_id=f"test_{i}",
                user_query="test query",
                execution_time=0.1
            )
        
        # Next error should trigger circuit breaker
        result = await error_handler.handle_error(
            exception=Exception("Another error"),
            stage="test_stage",
            correlation_id="circuit_test",
            user_query="test query",
            execution_time=0.1
        )
        
        # Should still return a valid result
        assert isinstance(result, ErrorResult)
    
    @pytest.mark.asyncio
    async def test_emergency_fallback(self, error_handler):
        """Test emergency fallback when error handler fails"""
        # Mock the classifier to fail
        with patch.object(error_handler.classifier, 'classify_error', side_effect=Exception("Classifier failed")):
            result = await error_handler._emergency_fallback(
                Exception("Original error"), "test_correlation", "test query", 0.1
            )
            
            assert isinstance(result, ErrorResult)
            assert result.error_type == "emergency_fallback"
            assert "technical difficulties" in result.response.lower()
    
    def test_error_statistics(self, error_handler):
        """Test error statistics tracking"""
        # Simulate some errors
        error_handler._update_error_stats(ClassifiedError(
            error_type=ErrorType.NETWORK_ERROR,
            severity=ErrorSeverity.HIGH,
            message="Network failed",
            user_message="Connectivity issues",
            recovery_strategy="retry",
            retry_recommended=True,
            context=ErrorContext("test", "test", "test", 0.1),
            original_exception=Exception("test"),
            timestamp=1234567890
        ))
        
        stats = error_handler.get_error_summary()
        
        assert isinstance(stats, dict)
        assert "total_errors" in stats
        assert "errors_by_type" in stats
        assert stats["total_errors"] > 0

@pytest.mark.asyncio
async def test_error_handling_integration():
    """Integration test for complete error handling system"""
    handler = ErrorHandler()
    
    # Test various error types
    errors = [
        ConnectionError("Network failed"),
        TimeoutError("Operation timed out"),
        ValueError("Invalid input"),
        Exception("Unknown error")
    ]
    
    results = []
    for i, error in enumerate(errors):
        result = await handler.handle_error(
            exception=error,
            stage="integration_test",
            correlation_id=f"integration_{i}",
            user_query="test query",
            execution_time=0.1
        )
        results.append(result)
    
    # All should return valid results
    assert len(results) == len(errors)
    for result in results:
        assert isinstance(result, ErrorResult)
        assert result.user_friendly is True
        assert len(result.response) > 0

if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
