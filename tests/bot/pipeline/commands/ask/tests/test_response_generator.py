"""
Unit Tests for Response Generation System

Tests AI response synthesis, persona selection, and fallback handling.
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from dataclasses import dataclass

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../../..'))

from src.bot.pipeline.commands.ask.stages.response_generator import (
    ResponseGenerator, ResponseResult
)
from src.bot.pipeline.commands.ask.stages.intent_detector import IntentResult
from src.bot.pipeline.commands.ask.stages.simplified_tool_orchestrator import ToolResult

class TestResponseGenerator:
    """Test suite for ResponseGenerator"""
    
    @pytest.fixture
    def response_generator(self):
        """Create ResponseGenerator instance for testing"""
        return ResponseGenerator()
    
    @pytest.fixture
    def mock_intent_result(self):
        """Mock intent result for testing"""
        return IntentResult(
            intent="data_needed",
            confidence=0.9,
            reasoning="Stock price query",
            entities={'symbols': ['AAPL'], 'indicators': ['price']}
        )
    
    @pytest.fixture
    def mock_tool_result(self):
        """Mock tool result for testing"""
        return ToolResult(
            success=True,
            tool_results=[
                {
                    'tool': 'get_global_quote',
                    'success': True,
                    'data': {'symbol': 'AAPL', 'price': 150.00, 'change': 2.5},
                    'execution_time': 0.1
                }
            ],
            summary="Retrieved AAPL price: $150.00 (+2.5)",
            execution_time=0.1
        )
    
    @pytest.fixture
    def mock_ai_client(self):
        """Mock AI client for testing"""
        mock_client = AsyncMock()
        mock_client.generate_response.return_value = "Based on the current market data, AAPL is trading at $150.00, showing a positive movement of $2.5."
        return mock_client
    
    @pytest.mark.asyncio
    async def test_successful_response_generation(self, response_generator, mock_intent_result, mock_tool_result):
        """Test successful AI response generation"""
        query = "What's AAPL price?"
        
        with patch.object(response_generator, 'ai_client') as mock_ai:
            mock_ai.generate_response.return_value = "AAPL is currently trading at $150.00."
            
            result = await response_generator.generate(
                query, mock_intent_result, mock_tool_result, "test_correlation"
            )
            
            assert isinstance(result, ResponseResult)
            assert result.response is not None
            assert len(result.response) > 0
            assert result.confidence > 0
            assert result.execution_time >= 0
    
    @pytest.mark.asyncio
    async def test_persona_selection_trading_expert(self, response_generator):
        """Test persona selection for trading queries"""
        query = "Should I buy AAPL stock?"
        intent_result = IntentResult(
            intent="data_needed",
            confidence=0.9,
            reasoning="Trading decision query",
            entities={'symbols': ['AAPL']}
        )
        
        persona = response_generator._select_persona(query, intent_result)
        
        assert persona in ["trading_expert", "risk_analyst", "market_analyst"]
    
    @pytest.mark.asyncio
    async def test_persona_selection_risk_analyst(self, response_generator):
        """Test persona selection for risk queries"""
        query = "What are the risks of investing in TSLA?"
        intent_result = IntentResult(
            intent="data_needed",
            confidence=0.9,
            reasoning="Risk analysis query",
            entities={'symbols': ['TSLA']}
        )
        
        persona = response_generator._select_persona(query, intent_result)
        
        assert persona == "risk_analyst"
    
    @pytest.mark.asyncio
    async def test_persona_selection_educational(self, response_generator):
        """Test persona selection for educational queries"""
        query = "How do I learn about options trading?"
        intent_result = IntentResult(
            intent="casual",
            confidence=0.9,
            reasoning="Educational query",
            entities={}
        )
        
        persona = response_generator._select_persona(query, intent_result)
        
        assert persona == "educational_assistant"
    
    @pytest.mark.asyncio
    async def test_context_building_with_tools(self, response_generator, mock_intent_result, mock_tool_result):
        """Test context building with tool results"""
        query = "What's AAPL price?"
        
        context = response_generator._build_response_context(
            query, mock_intent_result, mock_tool_result
        )
        
        assert isinstance(context, dict)
        assert 'query' in context
        assert 'intent' in context
        assert 'tool_results' in context
        assert 'symbols' in context
        assert context['query'] == query
        assert 'AAPL' in str(context)
    
    @pytest.mark.asyncio
    async def test_context_building_without_tools(self, response_generator, mock_intent_result):
        """Test context building without tool results"""
        query = "Hello, how are you?"
        
        context = response_generator._build_response_context(
            query, mock_intent_result, None
        )
        
        assert isinstance(context, dict)
        assert 'query' in context
        assert 'intent' in context
        assert context['tool_results'] is None
    
    @pytest.mark.asyncio
    async def test_fallback_response_generation(self, response_generator, mock_intent_result, mock_tool_result):
        """Test fallback response when AI fails"""
        query = "What's AAPL price?"
        
        with patch.object(response_generator, 'ai_client') as mock_ai:
            mock_ai.generate_response.side_effect = Exception("AI service unavailable")
            
            result = await response_generator.generate(
                query, mock_intent_result, mock_tool_result, "test_correlation"
            )
            
            assert isinstance(result, ResponseResult)
            assert result.response is not None
            assert len(result.response) > 0
            assert result.fallback_used is True
            assert result.confidence < 0.8  # Lower confidence for fallback
    
    @pytest.mark.asyncio
    async def test_tool_result_summarization(self, response_generator):
        """Test summarization of tool results"""
        tool_result = ToolResult(
            success=True,
            tool_results=[
                {
                    'tool': 'get_global_quote',
                    'success': True,
                    'data': {'symbol': 'AAPL', 'price': 150.00, 'change': 2.5, 'change_percent': 1.69},
                    'execution_time': 0.1
                },
                {
                    'tool': 'get_rsi',
                    'success': True,
                    'data': {'symbol': 'AAPL', 'rsi': 65.5},
                    'execution_time': 0.15
                }
            ],
            summary="Retrieved AAPL data",
            execution_time=0.25
        )
        
        summary = response_generator._summarize_tool_results(tool_result)
        
        assert isinstance(summary, str)
        assert len(summary) > 0
        assert 'AAPL' in summary
        assert '150.00' in summary or '65.5' in summary
    
    @pytest.mark.asyncio
    async def test_disclaimer_addition(self, response_generator, mock_intent_result, mock_tool_result):
        """Test automatic disclaimer addition for financial advice"""
        query = "Should I buy AAPL stock now?"
        
        with patch.object(response_generator, 'ai_client') as mock_ai:
            mock_ai.generate_response.return_value = "Based on the analysis, AAPL shows positive momentum."
            
            result = await response_generator.generate(
                query, mock_intent_result, mock_tool_result, "test_correlation"
            )
            
            # Should add disclaimer for investment advice
            if "should" in query.lower() or "buy" in query.lower():
                assert result.disclaimer_added is True
    
    @pytest.mark.asyncio
    async def test_confidence_scoring(self, response_generator, mock_intent_result, mock_tool_result):
        """Test confidence scoring logic"""
        query = "What's AAPL price?"
        
        with patch.object(response_generator, 'ai_client') as mock_ai:
            mock_ai.generate_response.return_value = "AAPL is trading at $150.00."
            
            result = await response_generator.generate(
                query, mock_intent_result, mock_tool_result, "test_correlation"
            )
            
            assert 0.0 <= result.confidence <= 1.0
            # Should have high confidence with successful AI and tools
            assert result.confidence > 0.7
    
    @pytest.mark.asyncio
    async def test_timeout_handling(self, response_generator, mock_intent_result, mock_tool_result):
        """Test timeout handling in AI generation"""
        query = "What's AAPL price?"
        
        with patch.object(response_generator, 'ai_client') as mock_ai:
            mock_ai.generate_response.side_effect = asyncio.TimeoutError("AI timeout")
            
            result = await response_generator.generate(
                query, mock_intent_result, mock_tool_result, "test_correlation"
            )
            
            assert isinstance(result, ResponseResult)
            assert result.fallback_used is True
            assert result.response is not None
    
    @pytest.mark.asyncio
    async def test_empty_tool_results_handling(self, response_generator, mock_intent_result):
        """Test handling of empty tool results"""
        query = "What's AAPL price?"
        empty_tool_result = ToolResult(
            success=False,
            tool_results=[],
            summary="No tools executed successfully",
            execution_time=0.0
        )
        
        with patch.object(response_generator, 'ai_client') as mock_ai:
            mock_ai.generate_response.return_value = "I'm unable to access current market data."
            
            result = await response_generator.generate(
                query, mock_intent_result, empty_tool_result, "test_correlation"
            )
            
            assert isinstance(result, ResponseResult)
            assert result.response is not None
            # Confidence should be lower without tool data
            assert result.confidence < 0.8
    
    def test_response_validation(self, response_generator):
        """Test response validation logic"""
        valid_responses = [
            "AAPL is trading at $150.00.",
            "Based on the analysis, the stock shows positive momentum.",
            "I'm unable to access current market data at the moment."
        ]
        
        invalid_responses = [
            "",
            "   ",
            None
        ]
        
        for response in valid_responses:
            assert response_generator._validate_response(response) is True
        
        for response in invalid_responses:
            assert response_generator._validate_response(response) is False
    
    @pytest.mark.asyncio
    async def test_performance_tracking(self, response_generator, mock_intent_result, mock_tool_result):
        """Test performance tracking"""
        query = "What's AAPL price?"
        
        with patch.object(response_generator, 'ai_client') as mock_ai:
            mock_ai.generate_response.return_value = "AAPL is trading at $150.00."
            
            result = await response_generator.generate(
                query, mock_intent_result, mock_tool_result, "test_correlation"
            )
            
            assert result.execution_time >= 0
            assert hasattr(result, 'model_used')
            assert result.model_used is not None

@pytest.mark.asyncio
async def test_response_generator_integration():
    """Integration test for response generator"""
    generator = ResponseGenerator()
    
    # Test with realistic data
    intent_result = IntentResult(
        intent="data_needed",
        confidence=0.9,
        reasoning="Stock price query",
        entities={'symbols': ['AAPL']}
    )
    
    tool_result = ToolResult(
        success=True,
        tool_results=[],
        summary="No tools available",
        execution_time=0.0
    )
    
    result = await generator.generate(
        "What's AAPL price?",
        intent_result,
        tool_result,
        "integration_test"
    )
    
    assert isinstance(result, ResponseResult)
    assert result.response is not None
    assert result.execution_time >= 0

if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
