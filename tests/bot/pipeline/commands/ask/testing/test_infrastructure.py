"""
Test Infrastructure for ASK Pipeline

Provides comprehensive testing infrastructure:
- Mock services for AI APIs and external dependencies
- Test data factories for consistent test scenarios
- Test database setup and teardown automation
- Test environment configuration and isolation
- Test reporting and coverage analysis
"""

import asyncio
import pytest
import json
import tempfile
import shutil
from typing import Dict, Any, Optional, List, Callable, Awaitable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
import logging
from pathlib import Path
import os
import sys

logger = logging.getLogger(__name__)

@dataclass
class TestConfig:
    """Configuration for test environment"""
    use_real_apis: bool = False
    mock_ai_responses: bool = True
    mock_external_apis: bool = True
    use_test_database: bool = True
    test_data_dir: str = "test_data"
    log_level: str = "DEBUG"
    timeout: float = 30.0
    max_retries: int = 3

@dataclass
class TestData:
    """Test data container"""
    queries: List[str] = field(default_factory=list)
    responses: List[str] = field(default_factory=list)
    user_data: List[Dict[str, Any]] = field(default_factory=list)
    market_data: List[Dict[str, Any]] = field(default_factory=list)
    error_scenarios: List[Dict[str, Any]] = field(default_factory=list)

class MockAIService:
    """Mock AI service for testing"""
    
    def __init__(self, config: TestConfig):
        self.config = config
        self.call_count = 0
        self.responses = []
        self.errors = []
        
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """Mock AI response generation"""
        self.call_count += 1
        
        # Simulate API delay
        await asyncio.sleep(0.01)
        
        # Return mock response based on prompt content
        if "error" in prompt.lower():
            error = Exception("Mock AI error")
            self.errors.append(error)
            raise error
        
        response = f"Mock AI response for: {prompt[:50]}..."
        self.responses.append(response)
        return response
    
    async def analyze_intent(self, query: str) -> Dict[str, Any]:
        """Mock intent analysis"""
        self.call_count += 1
        await asyncio.sleep(0.005)
        
        # Simple intent detection based on keywords
        intents = {
            "price": ["price", "cost", "value", "worth"],
            "analysis": ["analyze", "analysis", "trend", "chart"],
            "news": ["news", "update", "announcement"],
            "help": ["help", "assist", "support"]
        }
        
        query_lower = query.lower()
        detected_intent = "general"
        confidence = 0.5
        
        for intent, keywords in intents.items():
            if any(keyword in query_lower for keyword in keywords):
                detected_intent = intent
                confidence = 0.8
                break
        
        return {
            "intent": detected_intent,
            "confidence": confidence,
            "entities": [],
            "sentiment": "neutral"
        }
    
    def reset(self):
        """Reset mock state"""
        self.call_count = 0
        self.responses.clear()
        self.errors.clear()

class MockExternalAPI:
    """Mock external API service for testing"""
    
    def __init__(self, config: TestConfig):
        self.config = config
        self.call_count = 0
        self.responses = {}
        self.errors = []
        
    async def get_stock_price(self, symbol: str) -> Dict[str, Any]:
        """Mock stock price API"""
        self.call_count += 1
        await asyncio.sleep(0.01)
        
        if symbol.upper() in ["ERROR", "INVALID"]:
            error = Exception(f"Mock API error for symbol: {symbol}")
            self.errors.append(error)
            raise error
        
        return {
            "symbol": symbol.upper(),
            "price": 150.25,
            "change": 2.50,
            "change_percent": 1.69,
            "volume": 1000000,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    async def get_market_data(self, symbols: List[str]) -> Dict[str, Any]:
        """Mock market data API"""
        self.call_count += 1
        await asyncio.sleep(0.02)
        
        data = {}
        for symbol in symbols:
            if symbol.upper() != "ERROR":
                data[symbol.upper()] = await self.get_stock_price(symbol)
        
        return {"data": data, "timestamp": datetime.utcnow().isoformat()}
    
    def reset(self):
        """Reset mock state"""
        self.call_count = 0
        self.responses.clear()
        self.errors.clear()

class MockDatabase:
    """Mock database for testing"""
    
    def __init__(self, config: TestConfig):
        self.config = config
        self.data = {}
        self.queries = []
        self.errors = []
        
    async def execute_query(self, query: str, params: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """Mock database query execution"""
        self.queries.append({"query": query, "params": params})
        await asyncio.sleep(0.001)
        
        # Return mock data based on query type
        if "SELECT" in query.upper():
            return [{"id": 1, "data": "mock_data"}]
        elif "INSERT" in query.upper():
            return [{"id": 1, "affected_rows": 1}]
        else:
            return []
    
    async def close(self):
        """Mock database close"""
        self.data.clear()
        self.queries.clear()
        self.errors.clear()
    
    def reset(self):
        """Reset mock state"""
        self.data.clear()
        self.queries.clear()
        self.errors.clear()

class TestDataFactory:
    """Factory for creating consistent test data"""
    
    def __init__(self):
        self.query_templates = [
            "What is the price of {symbol}?",
            "Analyze the trend for {symbol}",
            "Get news about {symbol}",
            "Show me the chart for {symbol}",
            "What's the market sentiment for {symbol}?"
        ]
        self.symbols = ["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN", "META", "NVDA"]
        self.user_ids = ["user_123", "user_456", "user_789"]
        
    def create_query(self, symbol: str = None, query_type: str = "price") -> str:
        """Create a test query"""
        if symbol is None:
            symbol = self.symbols[0]
        
        template = self.query_templates[0]  # Default to price query
        if query_type == "analysis":
            template = self.query_templates[1]
        elif query_type == "news":
            template = self.query_templates[2]
        elif query_type == "chart":
            template = self.query_templates[3]
        elif query_type == "sentiment":
            template = self.query_templates[4]
        
        return template.format(symbol=symbol)
    
    def create_user_data(self, user_id: str = None) -> Dict[str, Any]:
        """Create test user data"""
        if user_id is None:
            user_id = self.user_ids[0]
        
        return {
            "user_id": user_id,
            "username": f"test_user_{user_id}",
            "guild_id": "guild_123",
            "permissions": ["ask", "view"],
            "rate_limit": 100,
            "created_at": datetime.utcnow().isoformat()
        }
    
    def create_market_data(self, symbol: str = None) -> Dict[str, Any]:
        """Create test market data"""
        if symbol is None:
            symbol = self.symbols[0]
        
        return {
            "symbol": symbol,
            "price": 150.25,
            "change": 2.50,
            "change_percent": 1.69,
            "volume": 1000000,
            "high": 155.00,
            "low": 148.00,
            "open": 149.50,
            "close": 150.25,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    def create_error_scenario(self, error_type: str = "api_error") -> Dict[str, Any]:
        """Create test error scenario"""
        scenarios = {
            "api_error": {
                "error": "APIError",
                "message": "External API unavailable",
                "status_code": 503,
                "retry_after": 60
            },
            "rate_limit": {
                "error": "RateLimitError",
                "message": "Rate limit exceeded",
                "status_code": 429,
                "retry_after": 300
            },
            "validation_error": {
                "error": "ValidationError",
                "message": "Invalid input parameters",
                "status_code": 400,
                "retry_after": 0
            },
            "timeout": {
                "error": "TimeoutError",
                "message": "Request timeout",
                "status_code": 408,
                "retry_after": 30
            }
        }
        
        return scenarios.get(error_type, scenarios["api_error"])

class TestEnvironment:
    """Test environment manager"""
    
    def __init__(self, config: TestConfig):
        self.config = config
        self.temp_dir = None
        self.mock_ai = MockAIService(config)
        self.mock_api = MockExternalAPI(config)
        self.mock_db = MockDatabase(config)
        self.data_factory = TestDataFactory()
        self.patches = []
        
    async def setup(self):
        """Setup test environment"""
        # Create temporary directory
        self.temp_dir = tempfile.mkdtemp(prefix="ask_test_")
        
        # Setup logging
        logging.basicConfig(level=getattr(logging, self.config.log_level))
        
        # Mock external dependencies
        await self._setup_mocks()
        
        logger.info(f"Test environment setup complete: {self.temp_dir}")
    
    async def teardown(self):
        """Teardown test environment"""
        # Cleanup patches
        for patch in self.patches:
            patch.stop()
        self.patches.clear()
        
        # Reset mocks
        self.mock_ai.reset()
        self.mock_api.reset()
        self.mock_db.reset()
        
        # Cleanup temporary directory
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
        
        logger.info("Test environment teardown complete")
    
    async def _setup_mocks(self):
        """Setup mock services"""
        # Mock AI service
        ai_patch = patch('src.bot.pipeline.commands.ask.ai.ai_service', self.mock_ai)
        ai_patch.start()
        self.patches.append(ai_patch)
        
        # Mock external API
        api_patch = patch('src.bot.pipeline.commands.ask.tools.external_api', self.mock_api)
        api_patch.start()
        self.patches.append(api_patch)
        
        # Mock database
        db_patch = patch('src.bot.pipeline.commands.ask.database', self.mock_db)
        db_patch.start()
        self.patches.append(db_patch)
    
    def get_test_data(self) -> TestData:
        """Get test data"""
        return TestData(
            queries=[self.data_factory.create_query() for _ in range(10)],
            responses=[f"Mock response {i}" for i in range(10)],
            user_data=[self.data_factory.create_user_data() for _ in range(5)],
            market_data=[self.data_factory.create_market_data() for _ in range(5)],
            error_scenarios=[self.data_factory.create_error_scenario() for _ in range(3)]
        )

class TestReporter:
    """Test reporting and coverage analysis"""
    
    def __init__(self):
        self.test_results = []
        self.coverage_data = {}
        self.performance_metrics = {}
        
    def record_test_result(self, test_name: str, passed: bool, duration: float, error: str = None):
        """Record test result"""
        self.test_results.append({
            "test_name": test_name,
            "passed": passed,
            "duration": duration,
            "error": error,
            "timestamp": datetime.utcnow().isoformat()
        })
    
    def record_coverage(self, module: str, coverage_percent: float):
        """Record coverage data"""
        self.coverage_data[module] = coverage_percent
    
    def record_performance(self, test_name: str, metrics: Dict[str, Any]):
        """Record performance metrics"""
        self.performance_metrics[test_name] = metrics
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["passed"])
        failed_tests = total_tests - passed_tests
        
        avg_duration = sum(result["duration"] for result in self.test_results) / total_tests if total_tests > 0 else 0
        
        return {
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "pass_rate": passed_tests / total_tests if total_tests > 0 else 0,
                "avg_duration": avg_duration
            },
            "coverage": self.coverage_data,
            "performance": self.performance_metrics,
            "details": self.test_results
        }

# Global test environment
_test_env: Optional[TestEnvironment] = None
_test_reporter = TestReporter()

@pytest.fixture
async def test_environment():
    """Pytest fixture for test environment"""
    global _test_env
    config = TestConfig()
    _test_env = TestEnvironment(config)
    await _test_env.setup()
    yield _test_env
    await _test_env.teardown()

@pytest.fixture
def test_data():
    """Pytest fixture for test data"""
    return _test_env.get_test_data() if _test_env else TestData()

@pytest.fixture
def mock_ai():
    """Pytest fixture for mock AI service"""
    return _test_env.mock_ai if _test_env else MockAIService(TestConfig())

@pytest.fixture
def mock_api():
    """Pytest fixture for mock external API"""
    return _test_env.mock_api if _test_env else MockExternalAPI(TestConfig())

@pytest.fixture
def mock_db():
    """Pytest fixture for mock database"""
    return _test_env.mock_db if _test_env else MockDatabase(TestConfig())

def get_test_reporter() -> TestReporter:
    """Get global test reporter"""
    return _test_reporter

def record_test_result(test_name: str, passed: bool, duration: float, error: str = None):
    """Record test result"""
    _test_reporter.record_test_result(test_name, passed, duration, error)

def record_coverage(module: str, coverage_percent: float):
    """Record coverage data"""
    _test_reporter.record_coverage(module, coverage_percent)

def record_performance(test_name: str, metrics: Dict[str, Any]):
    """Record performance metrics"""
    _test_reporter.record_performance(test_name, metrics)

def generate_test_report() -> Dict[str, Any]:
    """Generate test report"""
    return _test_reporter.generate_report()
