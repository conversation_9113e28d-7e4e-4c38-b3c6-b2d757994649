"""
Testing Infrastructure for ASK Pipeline

This module provides comprehensive testing capabilities:

1. Test Infrastructure - Mock services, data factories, and test environment
2. Unit Tests - Individual component testing with 95% coverage target
3. Integration Tests - Pipeline flow and component interaction testing
4. Security Tests - Security vulnerability scanning and attack simulation
5. Performance Tests - Load testing, benchmarking, and capacity planning

Features:
- Mock services for AI APIs and external dependencies
- Test data factories for consistent test scenarios
- Comprehensive test coverage tracking
- Performance benchmarking and load testing
- Security testing with attack simulation
- Integration testing with Discord simulation
- Automated test reporting and analysis
"""

from .test_infrastructure import (
    TestConfig,
    TestData,
    MockAIService,
    MockExternalAPI,
    MockDatabase,
    TestDataFactory,
    TestEnvironment,
    TestReporter,
    test_environment,
    test_data,
    mock_ai,
    mock_api,
    mock_db,
    get_test_reporter,
    record_test_result,
    record_coverage,
    record_performance,
    generate_test_report
)

from .unit_tests import (
    TestSecurityComponents,
    TestCoreComponents,
    TestPerformanceComponents,
    TestQualityComponents,
    TestIntegrationScenarios
)

from .integration_tests import (
    MockDiscordInteraction,
    TestPipelineIntegration,
    TestDiscordIntegration,
    TestComponentIntegration,
    TestDataFlowIntegration,
    TestConcurrentIntegration
)

from .security_tests import (
    SecurityTestSuite,
    TestInputValidation,
    TestRateLimiting,
    TestPIIProtection,
    TestAuthenticationSecurity,
    TestDataLeakagePrevention,
    TestSecurityRegression
)

from .performance_tests import (
    PerformanceTestSuite,
    TestLoadPerformance,
    TestStressPerformance,
    TestSpikePerformance,
    TestComponentPerformance,
    TestEndurancePerformance
)

__all__ = [
    # Test Infrastructure
    'TestConfig',
    'TestData',
    'MockAIService',
    'MockExternalAPI',
    'MockDatabase',
    'TestDataFactory',
    'TestEnvironment',
    'TestReporter',
    'test_environment',
    'test_data',
    'mock_ai',
    'mock_api',
    'mock_db',
    'get_test_reporter',
    'record_test_result',
    'record_coverage',
    'record_performance',
    'generate_test_report',
    
    # Unit Tests
    'TestSecurityComponents',
    'TestCoreComponents',
    'TestPerformanceComponents',
    'TestQualityComponents',
    'TestIntegrationScenarios',
    
    # Integration Tests
    'MockDiscordInteraction',
    'TestPipelineIntegration',
    'TestDiscordIntegration',
    'TestComponentIntegration',
    'TestDataFlowIntegration',
    'TestConcurrentIntegration',
    
    # Security Tests
    'SecurityTestSuite',
    'TestInputValidation',
    'TestRateLimiting',
    'TestPIIProtection',
    'TestAuthenticationSecurity',
    'TestDataLeakagePrevention',
    'TestSecurityRegression',
    
    # Performance Tests
    'PerformanceTestSuite',
    'TestLoadPerformance',
    'TestStressPerformance',
    'TestSpikePerformance',
    'TestComponentPerformance',
    'TestEndurancePerformance'
]
