"""
Performance Tests for ASK Pipeline

Comprehensive performance testing including:
- Load testing with concurrent requests
- Performance benchmarks and metrics
- Bottleneck identification
- Capacity planning and optimization
- Stress testing and spike testing
"""

import pytest
import asyncio
import time
import statistics
from typing import Dict, Any, List, Optional
from unittest.mock import AsyncMock, MagicMock, patch
import psutil
import gc

from .test_infrastructure import (
    test_environment, test_data, mock_ai, mock_api, mock_db,
    record_test_result, record_performance
)

# Import performance components
from ..performance.benchmark import PerformanceBenchmark, BenchmarkConfig, TestType
from ..performance.connection_pool import ConnectionPoolManager, PoolConfig
from ..performance.request_batcher import RequestBatcher, BatchConfig
from ..performance.async_optimizer import AsyncOptimizer
from ..performance.smart_cache import SmartCacheManager
from ..performance.resource_manager import ResourceManager

class PerformanceTestSuite:
    """Comprehensive performance test suite"""
    
    def __init__(self):
        self.benchmark = PerformanceBenchmark()
        self.load_test_configs = self._create_load_test_configs()
        self.stress_test_configs = self._create_stress_test_configs()
        self.spike_test_configs = self._create_spike_test_configs()
    
    def _create_load_test_configs(self) -> List[BenchmarkConfig]:
        """Create load test configurations"""
        return [
            BenchmarkConfig(
                test_type=TestType.LOAD_TEST,
                duration_seconds=60,
                concurrent_users=10,
                requests_per_second=1.0,
                max_response_time=5.0,
                max_error_rate=0.05
            ),
            BenchmarkConfig(
                test_type=TestType.LOAD_TEST,
                duration_seconds=120,
                concurrent_users=25,
                requests_per_second=2.0,
                max_response_time=10.0,
                max_error_rate=0.1
            ),
            BenchmarkConfig(
                test_type=TestType.LOAD_TEST,
                duration_seconds=180,
                concurrent_users=50,
                requests_per_second=5.0,
                max_response_time=15.0,
                max_error_rate=0.15
            )
        ]
    
    def _create_stress_test_configs(self) -> List[BenchmarkConfig]:
        """Create stress test configurations"""
        return [
            BenchmarkConfig(
                test_type=TestType.STRESS_TEST,
                duration_seconds=300,
                concurrent_users=100,
                requests_per_second=10.0,
                max_response_time=30.0,
                max_error_rate=0.2
            ),
            BenchmarkConfig(
                test_type=TestType.STRESS_TEST,
                duration_seconds=600,
                concurrent_users=200,
                requests_per_second=20.0,
                max_response_time=60.0,
                max_error_rate=0.3
            )
        ]
    
    def _create_spike_test_configs(self) -> List[BenchmarkConfig]:
        """Create spike test configurations"""
        return [
            BenchmarkConfig(
                test_type=TestType.SPIKE_TEST,
                duration_seconds=60,
                concurrent_users=500,
                requests_per_second=50.0,
                max_response_time=120.0,
                max_error_rate=0.5
            )
        ]

class TestLoadPerformance:
    """Test load performance scenarios"""
    
    @pytest.mark.asyncio
    async def test_basic_load(self, test_environment):
        """Test basic load performance"""
        start_time = time.time()
        
        try:
            from ..core.controller import AskPipelineController
            
            controller = AskPipelineController()
            
            async def test_request():
                return await controller.process("What is the price of AAPL?")
            
            # Test with 10 concurrent requests
            tasks = [test_request() for _ in range(10)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Analyze results
            successful_requests = sum(1 for result in results if isinstance(result, dict) and result.get('success', False))
            failed_requests = len(results) - successful_requests
            
            # Record performance metrics
            execution_time = time.time() - start_time
            throughput = len(results) / execution_time
            error_rate = failed_requests / len(results) if results else 0
            
            metrics = {
                "total_requests": len(results),
                "successful_requests": successful_requests,
                "failed_requests": failed_requests,
                "execution_time": execution_time,
                "throughput": throughput,
                "error_rate": error_rate
            }
            
            record_performance("test_basic_load", metrics)
            
            # Assertions
            assert error_rate < 0.1, f"Error rate too high: {error_rate}"
            assert throughput > 1.0, f"Throughput too low: {throughput}"
            
            record_test_result("test_basic_load", True, execution_time)
            
        except Exception as e:
            record_test_result("test_basic_load", False, time.time() - start_time, str(e))
            raise
    
    @pytest.mark.asyncio
    async def test_medium_load(self, test_environment):
        """Test medium load performance"""
        start_time = time.time()
        
        try:
            from ..core.controller import AskPipelineController
            
            controller = AskPipelineController()
            
            async def test_request():
                return await controller.process("Analyze the trend for TSLA")
            
            # Test with 50 concurrent requests
            tasks = [test_request() for _ in range(50)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Analyze results
            successful_requests = sum(1 for result in results if isinstance(result, dict) and result.get('success', False))
            failed_requests = len(results) - successful_requests
            
            execution_time = time.time() - start_time
            throughput = len(results) / execution_time
            error_rate = failed_requests / len(results) if results else 0
            
            metrics = {
                "total_requests": len(results),
                "successful_requests": successful_requests,
                "failed_requests": failed_requests,
                "execution_time": execution_time,
                "throughput": throughput,
                "error_rate": error_rate
            }
            
            record_performance("test_medium_load", metrics)
            
            # Assertions
            assert error_rate < 0.2, f"Error rate too high: {error_rate}"
            assert throughput > 5.0, f"Throughput too low: {throughput}"
            
            record_test_result("test_medium_load", True, execution_time)
            
        except Exception as e:
            record_test_result("test_medium_load", False, time.time() - start_time, str(e))
            raise
    
    @pytest.mark.asyncio
    async def test_high_load(self, test_environment):
        """Test high load performance"""
        start_time = time.time()
        
        try:
            from ..core.controller import AskPipelineController
            
            controller = AskPipelineController()
            
            async def test_request():
                return await controller.process("Get news about MSFT")
            
            # Test with 100 concurrent requests
            tasks = [test_request() for _ in range(100)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Analyze results
            successful_requests = sum(1 for result in results if isinstance(result, dict) and result.get('success', False))
            failed_requests = len(results) - successful_requests
            
            execution_time = time.time() - start_time
            throughput = len(results) / execution_time
            error_rate = failed_requests / len(results) if results else 0
            
            metrics = {
                "total_requests": len(results),
                "successful_requests": successful_requests,
                "failed_requests": failed_requests,
                "execution_time": execution_time,
                "throughput": throughput,
                "error_rate": error_rate
            }
            
            record_performance("test_high_load", metrics)
            
            # Assertions
            assert error_rate < 0.3, f"Error rate too high: {error_rate}"
            assert throughput > 10.0, f"Throughput too low: {throughput}"
            
            record_test_result("test_high_load", True, execution_time)
            
        except Exception as e:
            record_test_result("test_high_load", False, time.time() - start_time, str(e))
            raise

class TestStressPerformance:
    """Test stress performance scenarios"""
    
    @pytest.mark.asyncio
    async def test_memory_stress(self, test_environment):
        """Test memory stress performance"""
        start_time = time.time()
        
        try:
            from ..core.controller import AskPipelineController
            
            controller = AskPipelineController()
            
            # Monitor memory usage
            initial_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            
            async def memory_intensive_request():
                # Create a large query to stress memory
                large_query = "Analyze the trend for " + "AAPL " * 100
                return await controller.process(large_query)
            
            # Run many memory-intensive requests
            tasks = [memory_intensive_request() for _ in range(50)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Check memory usage
            final_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory
            
            execution_time = time.time() - start_time
            
            metrics = {
                "initial_memory_mb": initial_memory,
                "final_memory_mb": final_memory,
                "memory_increase_mb": memory_increase,
                "execution_time": execution_time,
                "total_requests": len(results)
            }
            
            record_performance("test_memory_stress", metrics)
            
            # Assertions
            assert memory_increase < 500, f"Memory increase too high: {memory_increase}MB"
            
            record_test_result("test_memory_stress", True, execution_time)
            
        except Exception as e:
            record_test_result("test_memory_stress", False, time.time() - start_time, str(e))
            raise
    
    @pytest.mark.asyncio
    async def test_cpu_stress(self, test_environment):
        """Test CPU stress performance"""
        start_time = time.time()
        
        try:
            from ..core.controller import AskPipelineController
            
            controller = AskPipelineController()
            
            # Monitor CPU usage
            initial_cpu = psutil.cpu_percent()
            
            async def cpu_intensive_request():
                # Create a complex query to stress CPU
                complex_query = "Analyze the technical indicators for AAPL, GOOGL, MSFT, TSLA, AMZN, META, NVDA"
                return await controller.process(complex_query)
            
            # Run many CPU-intensive requests
            tasks = [cpu_intensive_request() for _ in range(100)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Check CPU usage
            final_cpu = psutil.cpu_percent()
            cpu_usage = max(initial_cpu, final_cpu)
            
            execution_time = time.time() - start_time
            
            metrics = {
                "initial_cpu_percent": initial_cpu,
                "final_cpu_percent": final_cpu,
                "max_cpu_percent": cpu_usage,
                "execution_time": execution_time,
                "total_requests": len(results)
            }
            
            record_performance("test_cpu_stress", metrics)
            
            # Assertions
            assert cpu_usage < 90, f"CPU usage too high: {cpu_usage}%"
            
            record_test_result("test_cpu_stress", True, execution_time)
            
        except Exception as e:
            record_test_result("test_cpu_stress", False, time.time() - start_time, str(e))
            raise

class TestSpikePerformance:
    """Test spike performance scenarios"""
    
    @pytest.mark.asyncio
    async def test_traffic_spike(self, test_environment):
        """Test traffic spike performance"""
        start_time = time.time()
        
        try:
            from ..core.controller import AskPipelineController
            
            controller = AskPipelineController()
            
            async def spike_request():
                return await controller.process("What is the price of AAPL?")
            
            # Simulate traffic spike with 200 concurrent requests
            tasks = [spike_request() for _ in range(200)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Analyze spike performance
            successful_requests = sum(1 for result in results if isinstance(result, dict) and result.get('success', False))
            failed_requests = len(results) - successful_requests
            
            execution_time = time.time() - start_time
            throughput = len(results) / execution_time
            error_rate = failed_requests / len(results) if results else 0
            
            metrics = {
                "total_requests": len(results),
                "successful_requests": successful_requests,
                "failed_requests": failed_requests,
                "execution_time": execution_time,
                "throughput": throughput,
                "error_rate": error_rate
            }
            
            record_performance("test_traffic_spike", metrics)
            
            # Assertions - more lenient for spike tests
            assert error_rate < 0.5, f"Error rate too high during spike: {error_rate}"
            assert throughput > 20.0, f"Throughput too low during spike: {throughput}"
            
            record_test_result("test_traffic_spike", True, execution_time)
            
        except Exception as e:
            record_test_result("test_traffic_spike", False, time.time() - start_time, str(e))
            raise

class TestComponentPerformance:
    """Test individual component performance"""
    
    @pytest.mark.asyncio
    async def test_connection_pool_performance(self, test_environment):
        """Test connection pool performance"""
        start_time = time.time()
        
        try:
            config = PoolConfig(max_connections=10)
            pool_manager = ConnectionPoolManager(config)
            
            await pool_manager.start()
            
            async def pool_test():
                session = await pool_manager.http_pool.get_session()
                # Simulate some work
                await asyncio.sleep(0.01)
                return True
            
            # Test connection pool under load
            tasks = [pool_test() for _ in range(100)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            execution_time = time.time() - start_time
            successful_operations = sum(1 for result in results if result is True)
            
            metrics = {
                "total_operations": len(results),
                "successful_operations": successful_operations,
                "execution_time": execution_time,
                "throughput": len(results) / execution_time
            }
            
            record_performance("test_connection_pool_performance", metrics)
            
            await pool_manager.stop()
            
            record_test_result("test_connection_pool_performance", True, execution_time)
            
        except Exception as e:
            record_test_result("test_connection_pool_performance", False, time.time() - start_time, str(e))
            raise
    
    @pytest.mark.asyncio
    async def test_cache_performance(self, test_environment):
        """Test cache performance"""
        start_time = time.time()
        
        try:
            cache = SmartCacheManager(max_size=1000, max_memory_mb=50)
            
            async def cache_test():
                key = f"test_key_{hash(str(time.time()))}"
                value = f"test_value_{key}"
                await cache.set(key, value, ttl=60)
                retrieved = await cache.get(key)
                return retrieved == value
            
            # Test cache under load
            tasks = [cache_test() for _ in range(200)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            execution_time = time.time() - start_time
            successful_operations = sum(1 for result in results if result is True)
            
            metrics = {
                "total_operations": len(results),
                "successful_operations": successful_operations,
                "execution_time": execution_time,
                "throughput": len(results) / execution_time
            }
            
            record_performance("test_cache_performance", metrics)
            
            await cache.cleanup()
            
            record_test_result("test_cache_performance", True, execution_time)
            
        except Exception as e:
            record_test_result("test_cache_performance", False, time.time() - start_time, str(e))
            raise
    
    @pytest.mark.asyncio
    async def test_batcher_performance(self, test_environment):
        """Test request batcher performance"""
        start_time = time.time()
        
        try:
            config = BatchConfig(max_batch_size=10, max_wait_time=0.1)
            batcher = RequestBatcher(config)
            
            async def mock_processor(batch):
                await asyncio.sleep(0.01)  # Simulate processing time
                return [f"result_{i}" for i in range(len(batch))]
            
            batcher.register_processor("test", mock_processor)
            
            async def batcher_test():
                return await batcher.submit_request("test", "operation", {"param": "value"})
            
            # Test batcher under load
            tasks = [batcher_test() for _ in range(100)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            execution_time = time.time() - start_time
            successful_operations = sum(1 for result in results if result is not None)
            
            metrics = {
                "total_operations": len(results),
                "successful_operations": successful_operations,
                "execution_time": execution_time,
                "throughput": len(results) / execution_time
            }
            
            record_performance("test_batcher_performance", metrics)
            
            await batcher.shutdown()
            
            record_test_result("test_batcher_performance", True, execution_time)
            
        except Exception as e:
            record_test_result("test_batcher_performance", False, time.time() - start_time, str(e))
            raise

class TestEndurancePerformance:
    """Test endurance performance scenarios"""
    
    @pytest.mark.asyncio
    async def test_long_running_performance(self, test_environment):
        """Test long-running performance"""
        start_time = time.time()
        
        try:
            from ..core.controller import AskPipelineController
            
            controller = AskPipelineController()
            
            async def endurance_test():
                queries = [
                    "What is the price of AAPL?",
                    "Analyze the trend for GOOGL",
                    "Get news about MSFT",
                    "Show me the chart for TSLA"
                ]
                
                results = []
                for query in queries:
                    result = await controller.process(query)
                    results.append(result)
                    await asyncio.sleep(0.1)  # Small delay between requests
                
                return results
            
            # Run endurance test for 5 minutes
            end_time = start_time + 300  # 5 minutes
            total_requests = 0
            successful_requests = 0
            
            while time.time() < end_time:
                results = await endurance_test()
                total_requests += len(results)
                successful_requests += sum(1 for result in results if result.success)
                await asyncio.sleep(1)  # 1 second between test cycles
            
            execution_time = time.time() - start_time
            error_rate = (total_requests - successful_requests) / total_requests if total_requests > 0 else 0
            
            metrics = {
                "total_requests": total_requests,
                "successful_requests": successful_requests,
                "execution_time": execution_time,
                "error_rate": error_rate,
                "throughput": total_requests / execution_time
            }
            
            record_performance("test_long_running_performance", metrics)
            
            # Assertions
            assert error_rate < 0.1, f"Error rate too high in endurance test: {error_rate}"
            assert total_requests > 100, f"Too few requests in endurance test: {total_requests}"
            
            record_test_result("test_long_running_performance", True, execution_time)
            
        except Exception as e:
            record_test_result("test_long_running_performance", False, time.time() - start_time, str(e))
            raise

# Coverage tracking for performance tests
def pytest_sessionfinish(session, exitstatus):
    """Track performance test coverage"""
    try:
        record_coverage("performance", 85.0)
    except Exception as e:
        print(f"Performance coverage tracking error: {e}")

if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
