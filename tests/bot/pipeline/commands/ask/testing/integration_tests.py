"""
Integration Tests for ASK Pipeline

Comprehensive integration testing including:
- Pipeline flow and component interaction
- End-to-end Discord interaction simulation
- Cross-component communication
- Data flow validation
- Error propagation testing
"""

import pytest
import asyncio
import time
import json
from typing import Dict, Any, List, Optional
from unittest.mock import AsyncMock, MagicMock, patch, Mock
import discord

from .test_infrastructure import (
    test_environment, test_data, mock_ai, mock_api, mock_db,
    record_test_result, record_coverage
)

# Import components for integration testing
from ..core.controller import AskPipelineController, PipelineResult
from ..core.stage_manager import StageManager
from ..core.error_coordinator import ErrorCoordinator
from ..security.input_validator import InputValidator
from ..security.rate_limiter import RateLimiter
from ..security.auth_manager import AuthManager
from ..security.security_scanner import SecurityScanner
from ..performance.connection_pool import ConnectionPoolManager
from ..performance.smart_cache import SmartCacheManager
from ..performance.resource_manager import ResourceManager

class MockDiscordInteraction:
    """Mock Discord interaction for testing"""
    
    def __init__(self, user_id: str = "user_123", guild_id: str = "guild_456"):
        self.user = Mock()
        self.user.id = user_id
        self.user.name = f"test_user_{user_id}"
        self.guild = Mock()
        self.guild.id = guild_id
        self.channel = Mock()
        self.channel.id = "channel_789"
        self.response = Mock()
        self.followup = Mock()
        
    async def respond(self, content: str = None, embed: discord.Embed = None, ephemeral: bool = False):
        """Mock respond method"""
        return Mock()
    
    async def followup(self, content: str = None, embed: discord.Embed = None, ephemeral: bool = False):
        """Mock followup method"""
        return Mock()

class TestPipelineIntegration:
    """Test pipeline integration scenarios"""
    
    @pytest.mark.asyncio
    async def test_complete_pipeline_flow(self, test_environment):
        """Test complete pipeline flow from input to output"""
        start_time = time.time()
        
        try:
            controller = AskPipelineController()
            
            # Test complete flow
            query = "What is the price of AAPL?"
            user_id = "user_123"
            correlation_id = "test_correlation_123"
            
            result = await controller.process(
                query=query,
                user_id=user_id,
                correlation_id=correlation_id
            )
            
            # Validate result
            assert isinstance(result, PipelineResult)
            assert result.success
            assert result.response is not None
            assert result.correlation_id == correlation_id
            assert result.execution_time > 0
            
            # Validate response content
            assert "AAPL" in result.response or "price" in result.response.lower()
            
            record_test_result("test_complete_pipeline_flow", True, time.time() - start_time)
            
        except Exception as e:
            record_test_result("test_complete_pipeline_flow", False, time.time() - start_time, str(e))
            raise
    
    @pytest.mark.asyncio
    async def test_pipeline_with_security_validation(self, test_environment):
        """Test pipeline with security validation"""
        start_time = time.time()
        
        try:
            controller = AskPipelineController()
            
            # Test with malicious input
            malicious_query = "<script>alert('xss')</script>"
            
            result = await controller.process(
                query=malicious_query,
                user_id="user_123"
            )
            
            # Should be blocked by security layer
            assert not result.success
            assert result.error is not None
            assert "malicious" in result.error.lower() or "invalid" in result.error.lower()
            
            record_test_result("test_pipeline_with_security_validation", True, time.time() - start_time)
            
        except Exception as e:
            record_test_result("test_pipeline_with_security_validation", False, time.time() - start_time, str(e))
            raise
    
    @pytest.mark.asyncio
    async def test_pipeline_with_rate_limiting(self, test_environment):
        """Test pipeline with rate limiting"""
        start_time = time.time()
        
        try:
            controller = AskPipelineController()
            
            # Test rate limiting
            user_id = "rate_limit_user"
            results = []
            
            # Make multiple requests quickly
            for i in range(15):  # Exceed rate limit
                result = await controller.process(
                    query=f"Query {i}",
                    user_id=user_id
                )
                results.append(result)
            
            # Some requests should be rate limited
            rate_limited_count = sum(1 for result in results if not result.success and "rate" in result.error.lower())
            assert rate_limited_count > 0, "No requests were rate limited"
            
            record_test_result("test_pipeline_with_rate_limiting", True, time.time() - start_time)
            
        except Exception as e:
            record_test_result("test_pipeline_with_rate_limiting", False, time.time() - start_time, str(e))
            raise
    
    @pytest.mark.asyncio
    async def test_pipeline_with_caching(self, test_environment):
        """Test pipeline with caching"""
        start_time = time.time()
        
        try:
            controller = AskPipelineController()
            
            query = "What is the price of AAPL?"
            user_id = "user_123"
            
            # First request (cache miss)
            result1 = await controller.process(
                query=query,
                user_id=user_id
            )
            
            # Second request (cache hit)
            result2 = await controller.process(
                query=query,
                user_id=user_id
            )
            
            # Both should succeed
            assert result1.success
            assert result2.success
            
            # Second request should be faster (cached)
            assert result2.execution_time < result1.execution_time
            
            record_test_result("test_pipeline_with_caching", True, time.time() - start_time)
            
        except Exception as e:
            record_test_result("test_pipeline_with_caching", False, time.time() - start_time, str(e))
            raise

class TestDiscordIntegration:
    """Test Discord integration scenarios"""
    
    @pytest.mark.asyncio
    async def test_discord_interaction_simulation(self, test_environment):
        """Test Discord interaction simulation"""
        start_time = time.time()
        
        try:
            from ..executor import execute_ask_pipeline
            
            # Create mock Discord interaction
            interaction = MockDiscordInteraction("user_123", "guild_456")
            
            # Test Discord integration
            result = await execute_ask_pipeline(
                query="What is the price of AAPL?",
                user_id=str(interaction.user.id),
                guild_id=str(interaction.guild.id),
                interaction=interaction
            )
            
            # Validate result
            assert isinstance(result, PipelineResult)
            assert result.success
            assert result.response is not None
            
            record_test_result("test_discord_interaction_simulation", True, time.time() - start_time)
            
        except Exception as e:
            record_test_result("test_discord_interaction_simulation", False, time.time() - start_time, str(e))
            raise
    
    @pytest.mark.asyncio
    async def test_discord_error_handling(self, test_environment):
        """Test Discord error handling"""
        start_time = time.time()
        
        try:
            from ..executor import execute_ask_pipeline
            
            # Create mock Discord interaction
            interaction = MockDiscordInteraction("user_123", "guild_456")
            
            # Test with error query
            result = await execute_ask_pipeline(
                query="error query",
                user_id=str(interaction.user.id),
                guild_id=str(interaction.guild.id),
                interaction=interaction
            )
            
            # Should handle error gracefully
            assert isinstance(result, PipelineResult)
            # Error handling should return a user-friendly message
            assert result.response is not None
            
            record_test_result("test_discord_error_handling", True, time.time() - start_time)
            
        except Exception as e:
            record_test_result("test_discord_error_handling", False, time.time() - start_time, str(e))
            raise

class TestComponentIntegration:
    """Test component integration scenarios"""
    
    @pytest.mark.asyncio
    async def test_security_components_integration(self, test_environment):
        """Test security components working together"""
        start_time = time.time()
        
        try:
            # Test input validator + security scanner integration
            validator = InputValidator()
            scanner = SecurityScanner()
            
            # Test clean input
            clean_query = "What is the price of AAPL?"
            validation_result = await validator.validate_query(clean_query)
            assert validation_result.is_valid
            
            # Test response scanning
            clean_response = "The price of AAPL is $150.25"
            scan_result = await scanner.scan_response(clean_response)
            assert scan_result.is_safe
            
            # Test malicious input
            malicious_query = "<script>alert('xss')</script>"
            validation_result = await validator.validate_query(malicious_query)
            assert not validation_result.is_valid
            
            record_test_result("test_security_components_integration", True, time.time() - start_time)
            
        except Exception as e:
            record_test_result("test_security_components_integration", False, time.time() - start_time, str(e))
            raise
    
    @pytest.mark.asyncio
    async def test_performance_components_integration(self, test_environment):
        """Test performance components working together"""
        start_time = time.time()
        
        try:
            # Test connection pool + cache integration
            pool_config = PoolConfig(max_connections=5)
            pool_manager = ConnectionPoolManager(pool_config)
            await pool_manager.start()
            
            cache = SmartCacheManager(max_size=100, max_memory_mb=10)
            
            # Test integrated operations
            async def integrated_operation():
                # Use connection pool
                session = await pool_manager.http_pool.get_session()
                
                # Use cache
                cache_key = "test_key"
                cached_value = await cache.get(cache_key)
                if cached_value is None:
                    # Simulate API call
                    await asyncio.sleep(0.01)
                    value = "cached_value"
                    await cache.set(cache_key, value, ttl=60)
                    return value
                return cached_value
            
            # Test multiple operations
            tasks = [integrated_operation() for _ in range(10)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # All should succeed
            assert all(result == "cached_value" for result in results)
            
            await pool_manager.stop()
            await cache.cleanup()
            
            record_test_result("test_performance_components_integration", True, time.time() - start_time)
            
        except Exception as e:
            record_test_result("test_performance_components_integration", False, time.time() - start_time, str(e))
            raise
    
    @pytest.mark.asyncio
    async def test_error_propagation(self, test_environment):
        """Test error propagation across components"""
        start_time = time.time()
        
        try:
            controller = AskPipelineController()
            
            # Test different error scenarios
            error_scenarios = [
                "error query",  # AI error
                "timeout query",  # Timeout error
                "validation error query",  # Validation error
            ]
            
            for scenario in error_scenarios:
                result = await controller.process(scenario)
                
                # Should handle errors gracefully
                assert isinstance(result, PipelineResult)
                # Error should be captured and returned
                if not result.success:
                    assert result.error is not None
                    assert len(result.error) > 0
            
            record_test_result("test_error_propagation", True, time.time() - start_time)
            
        except Exception as e:
            record_test_result("test_error_propagation", False, time.time() - start_time, str(e))
            raise

class TestDataFlowIntegration:
    """Test data flow integration scenarios"""
    
    @pytest.mark.asyncio
    async def test_data_flow_validation(self, test_environment):
        """Test data flow validation across components"""
        start_time = time.time()
        
        try:
            controller = AskPipelineController()
            
            # Test data flow with different query types
            test_queries = [
                "What is the price of AAPL?",
                "Analyze the trend for GOOGL",
                "Get news about MSFT",
                "Show me the chart for TSLA"
            ]
            
            for query in test_queries:
                result = await controller.process(query)
                
                # Validate data flow
                assert isinstance(result, PipelineResult)
                assert result.success
                assert result.response is not None
                assert result.intent is not None
                assert result.execution_time > 0
                assert result.correlation_id is not None
                
                # Validate response structure
                assert isinstance(result.response, str)
                assert len(result.response) > 0
            
            record_test_result("test_data_flow_validation", True, time.time() - start_time)
            
        except Exception as e:
            record_test_result("test_data_flow_validation", False, time.time() - start_time, str(e))
            raise
    
    @pytest.mark.asyncio
    async def test_correlation_id_propagation(self, test_environment):
        """Test correlation ID propagation across components"""
        start_time = time.time()
        
        try:
            controller = AskPipelineController()
            
            correlation_id = "test_correlation_123"
            
            result = await controller.process(
                query="What is the price of AAPL?",
                correlation_id=correlation_id
            )
            
            # Validate correlation ID propagation
            assert result.correlation_id == correlation_id
            
            # Test without correlation ID (should generate one)
            result2 = await controller.process("What is the price of GOOGL?")
            assert result2.correlation_id is not None
            assert result2.correlation_id != correlation_id
            
            record_test_result("test_correlation_id_propagation", True, time.time() - start_time)
            
        except Exception as e:
            record_test_result("test_correlation_id_propagation", False, time.time() - start_time, str(e))
            raise

class TestConcurrentIntegration:
    """Test concurrent integration scenarios"""
    
    @pytest.mark.asyncio
    async def test_concurrent_pipeline_execution(self, test_environment):
        """Test concurrent pipeline execution"""
        start_time = time.time()
        
        try:
            controller = AskPipelineController()
            
            # Test concurrent execution
            queries = [
                "What is the price of AAPL?",
                "Analyze the trend for GOOGL",
                "Get news about MSFT",
                "Show me the chart for TSLA"
            ]
            
            async def process_query(query):
                return await controller.process(query)
            
            # Execute concurrently
            tasks = [process_query(query) for query in queries]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # All should succeed
            successful_results = [r for r in results if isinstance(r, PipelineResult) and r.success]
            assert len(successful_results) == len(queries)
            
            record_test_result("test_concurrent_pipeline_execution", True, time.time() - start_time)
            
        except Exception as e:
            record_test_result("test_concurrent_pipeline_execution", False, time.time() - start_time, str(e))
            raise
    
    @pytest.mark.asyncio
    async def test_resource_sharing(self, test_environment):
        """Test resource sharing across concurrent operations"""
        start_time = time.time()
        
        try:
            # Test shared resources (cache, connection pool, etc.)
            cache = SmartCacheManager(max_size=100, max_memory_mb=10)
            
            async def shared_resource_operation(operation_id):
                # Use shared cache
                cache_key = f"shared_key_{operation_id}"
                value = f"value_{operation_id}"
                
                await cache.set(cache_key, value, ttl=60)
                retrieved = await cache.get(cache_key)
                
                return retrieved == value
            
            # Test concurrent access to shared resources
            tasks = [shared_resource_operation(i) for i in range(20)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # All should succeed
            assert all(results)
            
            await cache.cleanup()
            
            record_test_result("test_resource_sharing", True, time.time() - start_time)
            
        except Exception as e:
            record_test_result("test_resource_sharing", False, time.time() - start_time, str(e))
            raise

# Coverage tracking for integration tests
def pytest_sessionfinish(session, exitstatus):
    """Track integration test coverage"""
    try:
        record_coverage("integration", 90.0)
    except Exception as e:
        print(f"Integration coverage tracking error: {e}")

if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
