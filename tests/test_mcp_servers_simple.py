#!/usr/bin/env python3
"""
Simple MCP Servers Test
Tests MCP servers directly without Docker dependency
"""

import asyncio
import json
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

async def test_mcp_servers_simple():
    """Test MCP servers configuration and client"""
    
    print("🧪 Testing MCP Servers Configuration...")
    
    try:
        # Test MCP client configuration
        from src.shared.mcp.mcp_client_config import mcp_config, MCPServerType
        
        print("\n📋 MCP Configuration:")
        enabled_servers = mcp_config.get_enabled_servers()
        print(f"Enabled Servers: {len(enabled_servers)}")
        
        for server in enabled_servers:
            print(f"\n🔧 {server.name} ({server.server_type.value}):")
            print(f"  URL: {server.url}")
            print(f"  Port: {server.port}")
            print(f"  Enabled: {server.enabled}")
            print(f"  Tools: {len(server.tools)}")
            print(f"  Sample tools: {server.tools[:3]}")
            print(f"  Description: {server.description}")
        
        # Test AlphaVantage (official server)
        print(f"\n🌐 AlphaVantage (Official):")
        alphavantage = mcp_config.get_server_by_type(MCPServerType.ALPHAVANTAGE)
        if alphavantage:
            print(f"  URL: {alphavantage.url}")
            print(f"  Enabled: {alphavantage.enabled}")
            print(f"  Tools: {len(alphavantage.tools)}")
            print(f"  Sample tools: {alphavantage.tools[:5]}")
        
        # Test Docker MCP client
        try:
            from src.shared.mcp.docker_mcp_client import get_docker_mcp_client, MCPToolCall, MCPServerType
            
            print(f"\n🔌 Testing MCP Client...")
            client = await get_docker_mcp_client()
            
            # Test health check
            health = await client.health_check()
            print(f"Overall Status: {health['overall_status']}")
            print(f"Total Tools: {health['total_tools']}")
            print(f"Enabled Servers: {health['enabled_servers']}")
            print(f"Unhealthy Servers: {health['unhealthy_servers']}")
            
            # Show server status
            print(f"\n📊 Server Status:")
            for server_name, status in health['servers'].items():
                print(f"  {server_name}: {status['status']} ({status['tools_count']} tools)")
            
            await client.close()
            
        except Exception as e:
            print(f"⚠️ MCP Client test failed: {e}")
            print("   This is expected if Docker containers aren't running")
        
        print(f"\n✅ MCP Servers configuration test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_mcp_servers_simple())
    sys.exit(0 if success else 1)
