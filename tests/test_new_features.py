#!/usr/bin/env python3
"""
Comprehensive Test Suite for New Trading Features
Tests all the newly implemented advanced trading capabilities
"""

import asyncio
import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FeatureTester:
    """Comprehensive test suite for new trading features"""
    
    def __init__(self):
        self.test_results = {}
        self.test_data = self._generate_test_data()
        
    def _generate_test_data(self):
        """Generate test data for testing"""
        # Generate sample OHLCV data
        dates = pd.date_range(start='2024-01-01', end='2024-01-31', freq='D')
        np.random.seed(42)
        
        # Generate realistic price data
        price = 100
        prices = []
        for _ in range(len(dates)):
            change = np.random.normal(0, 0.02)  # 2% daily volatility
            price *= (1 + change)
            prices.append(price)
        
        # Create OHLCV data
        data = pd.DataFrame({
            'date': dates,
            'open': [p * (1 + np.random.normal(0, 0.005)) for p in prices],
            'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
            'close': prices,
            'volume': np.random.randint(1000000, 5000000, len(dates))
        })
        
        data.set_index('date', inplace=True)
        return data
    
    async def test_real_time_data_streaming(self):
        """Test real-time data streaming system"""
        logger.info("🧪 Testing Real-Time Data Streaming...")
        
        try:
            from src.bot.real_time_data_stream import real_time_streamer, DataSource, MarketData
            
            # Initialize the streamer
            await real_time_streamer.initialize()
            
            # Test data callback
            received_data = []
            
            async def test_callback(data: MarketData):
                received_data.append(data)
                logger.info(f"Received data: {data.symbol} @ ${data.price:.2f}")
            
            # Test subscription
            success = await real_time_streamer.subscribe_to_symbol(
                "AAPL", test_callback, [DataSource.YAHOO_FINANCE]
            )
            
            if success:
                logger.info("✅ Real-time streaming subscription successful")
                self.test_results['realtime_streaming'] = True
            else:
                logger.error("❌ Real-time streaming subscription failed")
                self.test_results['realtime_streaming'] = False
            
            # Test health check
            health = await real_time_streamer.get_stream_health()
            logger.info(f"Stream health: {health}")
            
            # Cleanup
            await real_time_streamer.cleanup()
            
        except Exception as e:
            logger.error(f"❌ Real-time streaming test failed: {e}")
            self.test_results['realtime_streaming'] = False
    
    async def test_advanced_trading_strategies(self):
        """Test advanced trading strategies system"""
        logger.info("🧪 Testing Advanced Trading Strategies...")
        
        try:
            from src.bot.advanced_trading_strategies import advanced_strategies, StrategyType, SignalType
            
            # Initialize strategies
            await advanced_strategies.initialize()
            
            # Test strategy list
            strategies = await advanced_strategies.get_strategy_list()
            logger.info(f"Available strategies: {len(strategies)}")
            
            # Test RSI Momentum Strategy
            signals = await advanced_strategies.run_strategy(
                'momentum_rsi', 'AAPL', self.test_data
            )
            
            if signals:
                logger.info(f"✅ Generated {len(signals)} signals from RSI strategy")
                for signal in signals[:3]:  # Show first 3 signals
                    logger.info(f"  Signal: {signal.signal_type.value} @ ${signal.price:.2f} (confidence: {signal.confidence:.2f})")
            else:
                logger.warning("⚠️ No signals generated from RSI strategy")
            
            # Test backtesting
            backtest_result = await advanced_strategies.backtest_strategy(
                'momentum_rsi', 'AAPL', self.test_data, initial_capital=10000
            )
            
            if backtest_result:
                logger.info(f"✅ Backtest completed:")
                logger.info(f"  Total Return: {backtest_result.total_return:.2%}")
                logger.info(f"  Sharpe Ratio: {backtest_result.sharpe_ratio:.2f}")
                logger.info(f"  Max Drawdown: {backtest_result.max_drawdown:.2%}")
                logger.info(f"  Win Rate: {backtest_result.win_rate:.2%}")
                logger.info(f"  Total Trades: {backtest_result.total_trades}")
                self.test_results['trading_strategies'] = True
            else:
                logger.error("❌ Backtesting failed")
                self.test_results['trading_strategies'] = False
            
            # Test strategy optimization
            param_ranges = {
                'rsi_period': [10, 14, 20],
                'oversold_threshold': [25, 30, 35],
                'overbought_threshold': [65, 70, 75]
            }
            
            optimization_result = await advanced_strategies.optimize_strategy(
                'momentum_rsi', 'AAPL', self.test_data, param_ranges
            )
            
            if optimization_result:
                logger.info(f"✅ Strategy optimization completed:")
                logger.info(f"  Best parameters: {optimization_result['best_parameters']}")
                logger.info(f"  Best score: {optimization_result['best_score']:.4f}")
            
            # Cleanup
            await advanced_strategies.cleanup()
            
        except Exception as e:
            logger.error(f"❌ Trading strategies test failed: {e}")
            self.test_results['trading_strategies'] = False
    
    async def test_risk_management_system(self):
        """Test risk management system"""
        logger.info("🧪 Testing Risk Management System...")
        
        try:
            from src.bot.risk_management_system import risk_manager, Position, PositionType
            
            # Initialize risk manager
            await risk_manager.initialize()
            
            # Test position sizing calculation
            position_size = await risk_manager.calculate_position_size(
                'AAPL', 150.0, 140.0, 0.02  # 2% risk
            )
            logger.info(f"✅ Calculated position size: {position_size:.2f} shares")
            
            # Test stop loss calculation
            stop_loss = await risk_manager.calculate_stop_loss(
                'AAPL', 150.0, PositionType.LONG
            )
            logger.info(f"✅ Calculated stop loss: ${stop_loss:.2f}")
            
            # Test take profit calculation
            take_profit = await risk_manager.calculate_take_profit(
                'AAPL', 150.0, PositionType.LONG
            )
            logger.info(f"✅ Calculated take profit: ${take_profit:.2f}")
            
            # Test adding a position
            position = Position(
                symbol='AAPL',
                position_type=PositionType.LONG,
                quantity=100,
                entry_price=150.0,
                current_price=155.0,
                entry_time=datetime.now()
            )
            
            success = await risk_manager.add_position(position)
            if success:
                logger.info("✅ Position added successfully")
            else:
                logger.error("❌ Failed to add position")
            
            # Test position update
            await risk_manager.update_position('AAPL', 160.0)
            logger.info("✅ Position updated successfully")
            
            # Test risk metrics
            risk_metrics = await risk_manager.get_portfolio_risk_metrics()
            logger.info(f"✅ Risk metrics calculated:")
            logger.info(f"  Portfolio Value: ${risk_metrics.portfolio_value:,.2f}")
            logger.info(f"  Total Exposure: {risk_metrics.total_exposure:.2%}")
            logger.info(f"  Max Drawdown: {risk_metrics.max_drawdown:.2%}")
            logger.info(f"  VaR 95%: {risk_metrics.var_95:.2%}")
            logger.info(f"  Sharpe Ratio: {risk_metrics.sharpe_ratio:.2f}")
            
            # Test risk alerts
            alerts = await risk_manager.check_risk_alerts()
            logger.info(f"✅ Risk alerts check: {len(alerts)} alerts")
            
            # Test position summary
            summary = await risk_manager.get_position_risk_summary()
            logger.info(f"✅ Position summary: {summary['total_positions']} positions")
            
            self.test_results['risk_management'] = True
            
            # Cleanup
            await risk_manager.cleanup()
            
        except Exception as e:
            logger.error(f"❌ Risk management test failed: {e}")
            self.test_results['risk_management'] = False
    
    async def test_market_sentiment_analysis(self):
        """Test market sentiment analysis system"""
        logger.info("🧪 Testing Market Sentiment Analysis...")
        
        try:
            from src.bot.market_sentiment_analyzer import sentiment_analyzer, SentimentSource, SentimentType
            
            # Initialize sentiment analyzer
            await sentiment_analyzer.initialize()
            
            # Test sentiment analysis
            test_texts = [
                "AAPL is looking very bullish with strong momentum and positive earnings",
                "The market is crashing, sell everything immediately!",
                "Stocks are trading sideways with no clear direction",
                "Tesla is overvalued and due for a major correction",
                "Great buying opportunity in tech stocks right now"
            ]
            
            for i, text in enumerate(test_texts):
                sentiment_data = await sentiment_analyzer.analyze_sentiment(
                    'AAPL', text, SentimentSource.NEWS
                )
                
                if sentiment_data:
                    logger.info(f"✅ Text {i+1} sentiment: {sentiment_data.sentiment_type.value} (confidence: {sentiment_data.confidence:.2f})")
                else:
                    logger.error(f"❌ Failed to analyze sentiment for text {i+1}")
            
            # Test news sentiment analysis
            news_articles = [
                {
                    'title': 'Apple Reports Strong Q4 Earnings',
                    'content': 'Apple Inc. reported better-than-expected earnings for the fourth quarter, driven by strong iPhone sales and services revenue growth.',
                    'source': 'reuters',
                    'url': 'https://example.com/news1'
                },
                {
                    'title': 'Market Volatility Concerns Investors',
                    'content': 'Rising inflation and interest rate concerns are causing significant market volatility and investor anxiety.',
                    'source': 'bloomberg',
                    'url': 'https://example.com/news2'
                }
            ]
            
            news_sentiments = await sentiment_analyzer.analyze_news_sentiment('AAPL', news_articles)
            logger.info(f"✅ Analyzed {len(news_sentiments)} news articles")
            
            # Test social media sentiment
            social_posts = [
                {
                    'text': 'AAPL to the moon! 🚀🚀🚀',
                    'platform': 'twitter',
                    'id': '123456',
                    'author': 'trader123'
                },
                {
                    'text': 'Selling all my AAPL shares, market looks terrible',
                    'platform': 'reddit',
                    'id': '789012',
                    'author': 'bearish_trader'
                }
            ]
            
            social_sentiments = await sentiment_analyzer.analyze_social_media_sentiment('AAPL', social_posts)
            logger.info(f"✅ Analyzed {len(social_sentiments)} social media posts")
            
            # Test sentiment score
            sentiment_score = await sentiment_analyzer.get_sentiment_score('AAPL')
            if sentiment_score:
                logger.info(f"✅ Sentiment score for AAPL:")
                logger.info(f"  Overall: {sentiment_score.overall_sentiment.value}")
                logger.info(f"  Confidence: {sentiment_score.confidence:.2f}")
                logger.info(f"  Bullish: {sentiment_score.bullish_percentage:.1f}%")
                logger.info(f"  Bearish: {sentiment_score.bearish_percentage:.1f}%")
                logger.info(f"  Trend: {sentiment_score.trend_direction}")
            
            # Test market sentiment overview
            market_overview = await sentiment_analyzer.get_market_sentiment_overview()
            logger.info(f"✅ Market sentiment overview:")
            logger.info(f"  Overall: {market_overview.get('overall_sentiment', 'unknown')}")
            logger.info(f"  Fear/Greed: {market_overview.get('market_fear_greed', 50)}")
            logger.info(f"  Symbols analyzed: {market_overview.get('total_symbols_analyzed', 0)}")
            
            # Test fear/greed index
            fear_greed = await sentiment_analyzer.get_fear_greed_index()
            logger.info(f"✅ Fear/Greed Index: {fear_greed.get('fear_greed_index', 50)} ({fear_greed.get('sentiment_level', 'Unknown')})")
            
            self.test_results['sentiment_analysis'] = True
            
            # Cleanup
            await sentiment_analyzer.cleanup()
            
        except Exception as e:
            logger.error(f"❌ Sentiment analysis test failed: {e}")
            self.test_results['sentiment_analysis'] = False
    
    async def test_performance_analytics(self):
        """Test performance analytics system"""
        logger.info("🧪 Testing Performance Analytics...")
        
        try:
            from src.bot.performance_analytics import performance_analytics
            
            # Initialize performance analytics
            await performance_analytics.initialize()
            
            # Generate sample trades for testing
            sample_trades = []
            base_price = 100
            for i in range(20):
                entry_price = base_price + np.random.normal(0, 5)
                exit_price = entry_price + np.random.normal(0, 10)
                pnl = (exit_price - entry_price) / entry_price
                
                trade = {
                    'symbol': 'AAPL',
                    'strategy': 'test_strategy',
                    'entry_time': datetime.now() - timedelta(days=20-i),
                    'exit_time': datetime.now() - timedelta(days=19-i),
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'pnl': pnl,
                    'capital': 10000 + (i * 100)
                }
                sample_trades.append(trade)
            
            # Test performance metrics calculation
            performance_report = await performance_analytics.calculate_performance_metrics(sample_trades)
            
            if performance_report:
                logger.info(f"✅ Performance metrics calculated:")
                logger.info(f"  Total Return: {performance_report.total_return:.2%}")
                logger.info(f"  Annualized Return: {performance_report.annualized_return:.2%}")
                logger.info(f"  Sharpe Ratio: {performance_report.sharpe_ratio:.2f}")
                logger.info(f"  Sortino Ratio: {performance_report.sortino_ratio:.2f}")
                logger.info(f"  Max Drawdown: {performance_report.max_drawdown:.2%}")
                logger.info(f"  Calmar Ratio: {performance_report.calmar_ratio:.2f}")
                logger.info(f"  Win Rate: {performance_report.win_rate:.2%}")
                logger.info(f"  Profit Factor: {performance_report.profit_factor:.2f}")
                logger.info(f"  Volatility: {performance_report.volatility:.2%}")
                logger.info(f"  Total Trades: {performance_report.total_trades}")
                logger.info(f"  Winning Trades: {performance_report.winning_trades}")
                logger.info(f"  Losing Trades: {performance_report.losing_trades}")
                
                self.test_results['performance_analytics'] = True
            else:
                logger.error("❌ Failed to calculate performance metrics")
                self.test_results['performance_analytics'] = False
            
            # Test strategy comparison
            # Generate multiple strategy reports
            strategy_reports = []
            for strategy_name in ['strategy_1', 'strategy_2', 'strategy_3']:
                # Generate different performance for each strategy
                strategy_trades = []
                for i in range(15):
                    entry_price = base_price + np.random.normal(0, 5)
                    # Make strategies perform differently
                    if strategy_name == 'strategy_1':
                        exit_price = entry_price + np.random.normal(2, 8)  # Slightly positive
                    elif strategy_name == 'strategy_2':
                        exit_price = entry_price + np.random.normal(-1, 6)  # Slightly negative
                    else:
                        exit_price = entry_price + np.random.normal(0, 10)  # Neutral
                    
                    pnl = (exit_price - entry_price) / entry_price
                    
                    trade = {
                        'symbol': 'AAPL',
                        'strategy': strategy_name,
                        'entry_time': datetime.now() - timedelta(days=15-i),
                        'exit_time': datetime.now() - timedelta(days=14-i),
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'pnl': pnl,
                        'capital': 10000 + (i * 50)
                    }
                    strategy_trades.append(trade)
                
                strategy_report = await performance_analytics.calculate_performance_metrics(strategy_trades)
                if strategy_report:
                    strategy_reports.append(strategy_report)
            
            # Test strategy comparison
            if strategy_reports:
                comparison = await performance_analytics.compare_strategies(strategy_reports)
                logger.info(f"✅ Strategy comparison completed:")
                logger.info(f"  Total strategies: {comparison['summary']['total_strategies']}")
                logger.info(f"  Average return: {comparison['summary']['average_return']:.2%}")
                logger.info(f"  Best return: {comparison['summary']['best_return']:.2%}")
                logger.info(f"  Worst return: {comparison['summary']['worst_return']:.2%}")
            
            # Test performance report generation
            report_text = await performance_analytics.generate_performance_report(
                'test_strategy', 'AAPL', sample_trades
            )
            
            if report_text:
                logger.info("✅ Performance report generated successfully")
                logger.info("Report preview:")
                logger.info(report_text[:500] + "..." if len(report_text) > 500 else report_text)
            
            # Cleanup
            await performance_analytics.cleanup()
            
        except Exception as e:
            logger.error(f"❌ Performance analytics test failed: {e}")
            self.test_results['performance_analytics'] = False
    
    async def run_all_tests(self):
        """Run all feature tests"""
        logger.info("🚀 Starting Comprehensive Feature Testing...")
        logger.info("=" * 60)
        
        # Test all features
        await self.test_real_time_data_streaming()
        await self.test_advanced_trading_strategies()
        await self.test_risk_management_system()
        await self.test_market_sentiment_analysis()
        await self.test_performance_analytics()
        
        # Print test results summary
        logger.info("=" * 60)
        logger.info("📊 TEST RESULTS SUMMARY")
        logger.info("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        failed_tests = total_tests - passed_tests
        
        for feature, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"{status} {feature.replace('_', ' ').title()}")
        
        logger.info("=" * 60)
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {failed_tests}")
        logger.info(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests == 0:
            logger.info("🎉 ALL TESTS PASSED! New features are working correctly.")
        else:
            logger.warning(f"⚠️ {failed_tests} tests failed. Please check the logs for details.")
        
        return self.test_results

async def main():
    """Main test execution"""
    tester = FeatureTester()
    results = await tester.run_all_tests()
    return results

if __name__ == "__main__":
    # Run the tests
    results = asyncio.run(main())
