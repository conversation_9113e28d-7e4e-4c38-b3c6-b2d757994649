#!/usr/bin/env python3
"""
Comprehensive Unit Test Runner for ASK Pipeline

Runs all unit tests with detailed reporting and coverage analysis.
"""

import pytest
import sys
import os
import time
from pathlib import Path

def main():
    """Run all unit tests for ASK pipeline"""
    print("🧪 ASK Pipeline - Comprehensive Unit Test Suite")
    print("=" * 60)
    
    # Add project root to path
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    # Test directory
    test_dir = project_root / "src" / "bot" / "pipeline" / "commands" / "ask" / "tests"
    
    if not test_dir.exists():
        print(f"❌ Test directory not found: {test_dir}")
        return 1
    
    print(f"📁 Test directory: {test_dir}")
    print(f"🔍 Discovering tests...")
    
    # Test files to run
    test_files = [
        "test_intent_detector.py",
        "test_tool_orchestrator.py", 
        "test_response_generator.py",
        "test_error_handling.py"
    ]
    
    # Check which test files exist
    existing_tests = []
    for test_file in test_files:
        test_path = test_dir / test_file
        if test_path.exists():
            existing_tests.append(str(test_path))
            print(f"  ✅ Found: {test_file}")
        else:
            print(f"  ⚠️  Missing: {test_file}")
    
    if not existing_tests:
        print("❌ No test files found!")
        return 1
    
    print(f"\n🚀 Running {len(existing_tests)} test files...")
    print("-" * 60)
    
    start_time = time.time()
    
    # Run pytest with detailed output
    pytest_args = [
        "-v",  # Verbose output
        "-s",  # Don't capture output
        "--tb=short",  # Short traceback format
        "--durations=10",  # Show 10 slowest tests
        "-x",  # Stop on first failure
        "--disable-warnings",  # Disable warnings for cleaner output
    ] + existing_tests
    
    try:
        exit_code = pytest.main(pytest_args)
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return 1
    
    total_time = time.time() - start_time
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    if exit_code == 0:
        print("🎉 ALL TESTS PASSED!")
        print(f"✅ Total execution time: {total_time:.2f}s")
        print("\n🏆 Unit test suite is ready for production!")
    else:
        print(f"❌ Tests failed with exit code: {exit_code}")
        print(f"⏱️  Execution time: {total_time:.2f}s")
        print("\n🔧 Please fix failing tests before proceeding.")
    
    return exit_code

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
