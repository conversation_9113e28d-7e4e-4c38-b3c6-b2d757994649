#!/usr/bin/env python3
"""
Test script to verify mock data is properly controlled by environment settings.
Tests both development and production modes.
"""

import os
import sys
import asyncio
import logging
from typing import Dict, Any

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_mock_data_control():
    """Test mock data control in different environments"""
    
    print("🧪 TESTING MOCK DATA CONTROL")
    print("=" * 50)
    
    # Test 1: Development Environment (should allow mock data)
    print("\n📝 Test 1: Development Environment")
    print("-" * 40)
    
    os.environ['ENVIRONMENT'] = 'development'
    
    try:
        # Test AI processor mock historical data
        from src.shared.ai_services.ai_processor_robust import CleanAIProcessor
        processor = CleanAIProcessor()

        # Access the private method using getattr to test it
        mock_method = getattr(processor, '_generate_mock_historical_data', None)
        if mock_method:
            mock_prices = mock_method(100.0)
            if mock_prices:
                print("✅ AI Processor: Mock historical data generated in development")
                print(f"   Generated {len(mock_prices)} price points")
            else:
                print("❌ AI Processor: No mock data generated in development")
        else:
            print("❌ AI Processor: Mock method not found")
        
        # Test fallback provider
        from src.shared.data_providers.fallback_provider import FallbackProvider
        fallback = FallbackProvider()
        
        current_price_result = fallback.get_current_price("AAPL")
        if "error" not in current_price_result and current_price_result.get("mock_data"):
            print("✅ Fallback Provider: Mock current price data generated in development")
            print(f"   Price: ${current_price_result.get('price', 'N/A')}")
        else:
            print("❌ Fallback Provider: No mock current price data in development")
        
        historical_result = fallback.get_historical_data("AAPL", limit=5)
        if "error" not in historical_result and historical_result.get("mock_data"):
            print("✅ Fallback Provider: Mock historical data generated in development")
            print(f"   Generated {len(historical_result.get('data', []))} historical bars")
        else:
            print("❌ Fallback Provider: No mock historical data in development")
        
        # Test aggregator provider order
        from src.shared.data_providers.aggregator import DataProviderAggregator
        aggregator = DataProviderAggregator()
        
        provider_order = aggregator._get_default_provider_order()
        if 'fallback' in provider_order:
            print("✅ Aggregator: Fallback provider included in development")
            print(f"   Provider order: {provider_order}")
        else:
            print("❌ Aggregator: Fallback provider not included in development")
            
    except Exception as e:
        print(f"❌ Development test failed: {e}")
    
    # Test 2: Production Environment (should block mock data)
    print("\n📝 Test 2: Production Environment")
    print("-" * 40)
    
    os.environ['ENVIRONMENT'] = 'production'
    
    try:
        # Test AI processor mock historical data
        processor = CleanAIProcessor()

        # Access the private method using getattr to test it
        mock_method = getattr(processor, '_generate_mock_historical_data', None)
        if mock_method:
            mock_prices = mock_method(100.0)
            if not mock_prices:
                print("✅ AI Processor: Mock historical data blocked in production")
            else:
                print("❌ AI Processor: Mock data generated in production (should be blocked)")
                print(f"   Generated {len(mock_prices)} price points")
        else:
            print("❌ AI Processor: Mock method not found")
        
        # Test fallback provider
        fallback = FallbackProvider()
        
        current_price_result = fallback.get_current_price("AAPL")
        if "error" in current_price_result:
            print("✅ Fallback Provider: Mock current price data blocked in production")
            print(f"   Error: {current_price_result.get('error', 'N/A')}")
        else:
            print("❌ Fallback Provider: Mock current price data allowed in production")
        
        historical_result = fallback.get_historical_data("AAPL", limit=5)
        if "error" in historical_result:
            print("✅ Fallback Provider: Mock historical data blocked in production")
            print(f"   Error: {historical_result.get('error', 'N/A')}")
        else:
            print("❌ Fallback Provider: Mock historical data allowed in production")
        
        # Test aggregator provider order
        aggregator = DataProviderAggregator()
        
        provider_order = aggregator._get_default_provider_order()
        if 'fallback' not in provider_order:
            print("✅ Aggregator: Fallback provider excluded in production")
            print(f"   Provider order: {provider_order}")
        else:
            print("❌ Aggregator: Fallback provider included in production")
            
    except Exception as e:
        print(f"❌ Production test failed: {e}")
    
    # Test 3: Probability Service
    print("\n📝 Test 3: Probability Service")
    print("-" * 40)
    
    try:
        from src.analysis.probability.probability_response_service import ProbabilityResponseService
        prob_service = ProbabilityResponseService()
        
        # Test in development
        os.environ['ENVIRONMENT'] = 'development'
        dev_data = prob_service._create_default_historical_data(100.0, allow_synthetic=True)
        if not dev_data.empty:
            print("✅ Probability Service: Synthetic data allowed in development with flag")
        else:
            print("❌ Probability Service: Synthetic data blocked in development with flag")
        
        # Test in production without flag
        os.environ['ENVIRONMENT'] = 'production'
        prod_data = prob_service._create_default_historical_data(100.0, allow_synthetic=False)
        if prod_data.empty:
            print("✅ Probability Service: Synthetic data blocked in production without flag")
        else:
            print("❌ Probability Service: Synthetic data allowed in production without flag")
            
    except Exception as e:
        print(f"❌ Probability service test failed: {e}")
    
    print("\n🎯 SUMMARY")
    print("=" * 50)
    print("Mock data control tests completed.")
    print("✅ = Expected behavior")
    print("❌ = Unexpected behavior (needs investigation)")
    
    # Reset environment
    os.environ['ENVIRONMENT'] = 'development'

if __name__ == "__main__":
    asyncio.run(test_mock_data_control())
