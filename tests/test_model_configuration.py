#!/usr/bin/env python3
"""
Test script to verify the new model configuration is working correctly.
This will check if the system is using the new models instead of the old free ones.
"""

import os
import sys
import asyncio
from pathlib import Path
from typing import List

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

async def test_model_configuration():
    """Test that the new model configuration is being used"""
    
    print("🔧 Testing Model Configuration...")
    print("=" * 50)
    
    # Test 1: Check environment variables
    print("\n1. Environment Variables:")
    print(f"   AI_MODEL: {os.getenv('AI_MODEL', 'NOT SET')}")
    print(f"   MODEL_GLOBAL_FALLBACK: {os.getenv('MODEL_GLOBAL_FALLBACK', 'NOT SET')}")
    print(f"   MODEL_ANALYSIS: {os.getenv('MODEL_ANALYSIS', 'NOT SET')}")
    print(f"   MODEL_HEAVY: {os.getenv('MODEL_HEAVY', 'NOT SET')}")
    
    # Test 2: Check AI Client configuration
    print("\n2. AI Client Configuration:")
    try:
        from src.shared.ai_chat.ai_client import AIClient
        client = AIClient()
        print(f"   AI Client Model: {client.model}")
        
        # Check if it's using the old free model
        if "kimi-k2:free" in client.model:
            print("   ❌ STILL USING OLD FREE MODEL!")
        elif "kimi-k2-0905" in client.model:
            print("   ✅ Using updated Kimi model")
        elif "cogito-v2" in client.model:
            print("   ✅ Using DeepCogito model")
        else:
            print(f"   ⚠️  Using different model: {client.model}")
            
    except Exception as e:
        print(f"   ❌ Error loading AI Client: {e}")
    
    # Test 3: Check Pipeline Configuration
    print("\n3. Pipeline Configuration:")
    try:
        from src.bot.pipeline.commands.ask.stages.config import AskStageConfig
        config = AskStageConfig()
        print(f"   Pipeline Model: {config.model}")
        
        if "kimi-k2:free" in config.model:
            print("   ❌ PIPELINE STILL USING OLD FREE MODEL!")
        else:
            print("   ✅ Pipeline using updated model")
            
    except Exception as e:
        print(f"   ❌ Error loading Pipeline Config: {e}")
    
    # Test 4: Check Smart Model Router
    print("\n4. Smart Model Router:")
    try:
        from src.shared.ai_services.smart_model_router import SmartModelRouter
        router = SmartModelRouter()
        
        # Check if router has new models
        models = router.models
        print(f"   Available Models: {list(models.keys())}")
        
        # Check specific new models
        new_models_found = []
        for model_name, model_config in models.items():
            if "tongyi-deepresearch" in model_config.model_id:
                new_models_found.append("tongyi-deepresearch")
            elif "kimi-k2-0905" in model_config.model_id:
                new_models_found.append("kimi-k2-0905")
            elif "cogito-v2" in model_config.model_id:
                new_models_found.append("cogito-v2")
            elif "qwen-plus-thinking" in model_config.model_id:
                new_models_found.append("qwen-plus-thinking")
            elif "internvl3" in model_config.model_id:
                new_models_found.append("internvl3")
        
        if new_models_found:
            print(f"   ✅ New models found: {new_models_found}")
        else:
            print("   ⚠️  No new models found in router")
            
    except Exception as e:
        print(f"   ❌ Error loading Smart Model Router: {e}")
    
    # Test 5: Check Rate Limit Handler
    print("\n5. Rate Limit Handler:")
    try:
        from src.shared.ai_services.rate_limit_handler import rate_limit_handler
        providers = rate_limit_handler.providers
        print(f"   Available Providers: {list(providers.keys())}")
        
        # Check for new providers
        new_providers = ["alibaba", "opengvlab", "moonshotai", "qwen", "deepcogito"]
        found_providers = [p for p in new_providers if p in providers]
        
        if found_providers:
            print(f"   ✅ New providers found: {found_providers}")
        else:
            print("   ⚠️  No new providers found")
            
    except Exception as e:
        print(f"   ❌ Error loading Rate Limit Handler: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Configuration Test Complete!")
    
    # Summary
    print("\n📊 Summary:")
    print("   If you see ❌ 'STILL USING OLD FREE MODEL', the Docker containers need to restart")
    print("   If you see ✅ messages, the new configuration is working!")
    print("   The system should now use the new paid models instead of rate-limited free ones.")

if __name__ == "__main__":
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    asyncio.run(test_model_configuration())
