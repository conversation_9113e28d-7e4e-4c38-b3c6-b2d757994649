#!/usr/bin/env python3
"""
Test NVDA bias after applying the system prompt fixes
"""

import asyncio
import sys
import os
from collections import Counter

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_nvda_bias_after_fix():
    """Test if NVDA bias is reduced after system prompt fixes"""
    
    print("🧪 TESTING NVDA BIAS AFTER SYSTEM PROMPT FIXES")
    print("=" * 60)
    
    # Import the AI processor
    from src.shared.ai_services.ai_processor_robust import RobustFinancialAnalyzer
    
    # Create analyzer instance
    analyzer = RobustFinancialAnalyzer()
    
    # Test queries that previously showed heavy NVDA bias
    test_queries = [
        "What's the best tech stock to buy right now?",
        "I want to find a good AI stock for my portfolio",
        "Which stock has the most potential?",
        "Give me a trading recommendation",
        "What's a good stock for momentum trading?"
    ]
    
    all_symbols_mentioned = []
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📝 Query {i}: '{query}'")
        print("-" * 50)
        
        try:
            # Test the query processing using the general question handler
            response = await analyzer.answer_general_question(query)
            
            print(f"Response: {response[:300]}...")
            
            # Extract symbols mentioned in response
            import re
            symbols = re.findall(r'\b([A-Z]{2,5})\b', response)
            # Filter out common words that aren't stock symbols
            stock_symbols = [s for s in symbols if s in ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'AMD', 'META', 'SPY', 'QQQ', 'JPM', 'BAC', 'JNJ', 'PFE', 'XOM', 'CVX', 'PLTR', 'SNOW']]
            
            all_symbols_mentioned.extend(stock_symbols)
            print(f"Symbols found: {stock_symbols}")
            
        except Exception as e:
            print(f"Error: {e}")
    
    print(f"\n🎯 SYMBOL DISTRIBUTION AFTER FIX")
    print("-" * 40)
    
    if all_symbols_mentioned:
        total_symbol_counts = Counter(all_symbols_mentioned)
        total_mentions = sum(total_symbol_counts.values())
        
        for symbol, count in total_symbol_counts.most_common():
            percentage = (count / total_mentions) * 100
            print(f"  {symbol}: {count} mentions ({percentage:.1f}%)")
        
        # Check NVDA bias
        nvda_percentage = (total_symbol_counts.get('NVDA', 0) / total_mentions) * 100
        
        print(f"\n📊 NVDA BIAS ANALYSIS")
        print("-" * 40)
        
        if nvda_percentage > 40:
            print(f"❌ NVDA still dominates with {nvda_percentage:.1f}% of mentions")
            print("   System prompt fix may need further refinement")
        elif nvda_percentage > 20:
            print(f"⚠️  NVDA has {nvda_percentage:.1f}% of mentions (moderate bias)")
            print("   Some improvement but still room for better diversification")
        else:
            print(f"✅ NVDA has only {nvda_percentage:.1f}% of mentions (good diversification)")
            print("   System prompt fix successfully reduced NVDA bias!")
        
        # Check for diversification
        unique_symbols = len(total_symbol_counts)
        print(f"\n📈 DIVERSIFICATION METRICS")
        print("-" * 40)
        print(f"  Unique symbols mentioned: {unique_symbols}")
        print(f"  Average mentions per symbol: {total_mentions/unique_symbols:.1f}")
        
        if unique_symbols >= 5:
            print("✅ Good symbol diversification")
        elif unique_symbols >= 3:
            print("⚠️  Moderate symbol diversification")
        else:
            print("❌ Poor symbol diversification")
            
    else:
        print("No stock symbols found in responses")

if __name__ == "__main__":
    asyncio.run(test_nvda_bias_after_fix())
