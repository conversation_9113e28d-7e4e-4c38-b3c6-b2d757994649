#!/usr/bin/env python3
"""
Test script to verify lazy loading implementation and data pipeline improvements.
"""

import sys
import os
import time
import asyncio

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def test_lazy_import_performance():
    \"\"\"Test that lazy imports improve startup performance.\"\"\"
    print(\"Testing lazy import performance...\")
    
    # Measure import time without lazy loading
    start_time = time.time()
    try:
        import pandas as pd
        import numpy as np
        basic_import_time = time.time() - start_time
        print(f\"Basic imports took: {basic_import_time:.4f} seconds\")
    except ImportError:
        print(\"Basic imports not available for timing test\")
        basic_import_time = 0
    
    # Measure import time with lazy loading
    start_time = time.time()
    from src.shared.utils.lazy_import import lazy_import, get_lazy_module
    
    # Register modules
    lazy_import(\"pandas\", \"pandas\")
    lazy_import(\"numpy\", \"numpy\")
    
    lazy_import_time = time.time() - start_time
    print(f\"Lazy import registration took: {lazy_import_time:.4f} seconds\")
    
    # Test actual loading
    start_time = time.time()
    pd = get_lazy_module(\"pandas\")
    df = pd.DataFrame({'a': [1, 2, 3]})
    actual_load_time = time.time() - start_time
    print(f\"Actual lazy loading took: {actual_load_time:.4f} seconds\")
    
    print(f\"Lazy loading overhead: {actual_load_time - lazy_import_time:.4f} seconds\")
    print(\"Lazy import test completed.\\n\")

async def test_data_pipeline_improvements():
    \"\"\"Test improved data pipeline handling.\"\"\"
    print(\"Testing data pipeline improvements...\")
    
    # Test data validation and enhancement functions
    from src.bot.pipeline.commands.analyze.stages.fetch_data import (
        _check_data_sufficiency,
        _calculate_data_quality_metrics
    )
    
    # Test with insufficient data
    insufficient_data = {
        'price': 100.0,
        'symbol': 'TEST',
        'close': [100.0, 101.0, 102.0]  # Only 3 data points
    }
    
    sufficiency = _check_data_sufficiency(insufficient_data)
    print(f\"Data sufficiency for insufficient data: {sufficiency}\")
    
    # Test with sufficient data
    sufficient_data = {
        'price': 100.0,
        'symbol': 'TEST',
        'close': list(range(100, 120))  # 20 data points
    }
    
    sufficiency = _check_data_sufficiency(sufficient_data)
    print(f\"Data sufficiency for sufficient data: {sufficiency}\")
    
    # Test data quality metrics
    quality_metrics = _calculate_data_quality_metrics(sufficient_data)
    print(f\"Data quality metrics: {quality_metrics}\")
    
    print(\"Data pipeline test completed.\\n\")

def main():
    \"\"\"Main test function.\"\"\"
    print(\"Running optimization tests...\\n\")
    
    # Test lazy imports
    test_lazy_import_performance()
    
    # Test data pipeline improvements
    asyncio.run(test_data_pipeline_improvements())
    
    print(\"All tests completed successfully!\")

if __name__ == \"__main__\":
    main()