#!/usr/bin/env python3
"""
Test Real Rate Limiting Scenario
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv
load_dotenv()

async def test_real_rate_limit():
    """Test with actual rate limiting (will hit the 25 requests/day limit)"""
    
    print("🧪 Testing Real Rate Limiting Scenario...")
    print("⚠️  This will consume actual API calls and may hit rate limits")
    
    try:
        from src.bot.pipeline.commands.ask.pipeline import AskPipeline
        from src.shared.error_handling.logging import generate_correlation_id
        
        # Create pipeline
        pipeline = AskPipeline()
        
        # Test query that will trigger Alpha Vantage calls
        query = "What is the current price of Apple stock?"
        user_id = "test_user"
        correlation_id = generate_correlation_id()
        
        print(f"📝 Test Query: {query}")
        print("🔄 Running pipeline (this may hit rate limits)...")
        
        # Run the pipeline
        result = await pipeline.process(query, user_id, correlation_id)
        
        print(f"\n📊 Pipeline Result:")
        print(f"   Success: {result.success}")
        print(f"   Response: {result.response}")
        print(f"   Intent: {result.intent}")
        print(f"   Tools Used: {result.tools_used}")
        print(f"   Execution Time: {result.execution_time:.2f}s")
        print(f"   Confidence: {result.confidence:.2f}")
        
        # Check if response contains rate limiting message
        if "rate limit" in result.response.lower():
            print("✅ Rate limiting properly detected and handled")
            return True
        elif "Response generated successfully" in result.response:
            print("❌ Generic response instead of rate limit message")
            return False
        else:
            print("⚠️ Unexpected response format")
            print(f"   Response preview: {result.response[:200]}...")
            return False
        
    except Exception as e:
        print(f"❌ Error testing real rate limit: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_real_rate_limit())
    print(f"\n🎯 Test Result: {'✅ Success' if success else '❌ Failed'}")
    sys.exit(0 if success else 1)
