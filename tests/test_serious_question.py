#!/usr/bin/env python3
"""
Test the pipeline with a serious market question and evaluate response quality
"""

import asyncio
import sys
sys.path.append('.')

from src.shared.ai_services.enhanced_intent_detector import EnhancedIntentDetector
from src.shared.data_providers.aggregator import DataProviderAggregator
from src.shared.technical_analysis.enhanced_calculator import EnhancedTechnicalCalculator
from src.shared.ai_chat.ai_client import AIClientWrapper
from src.shared.ai_services.fact_verifier import TradingFactVerifier
from src.core.formatting.text_formatting import format_price

async def test_serious_question():
    print('🔍 Testing Serious Market Question')
    print('=' * 60)
    
    # Serious market question that a real trader might ask - no fake data
    query = 'What is the current technical outlook for NVDA? I am considering a position and want to understand the momentum, key support and resistance levels, and whether the current price action suggests a good entry point or if I should wait.'
    
    print(f'📝 Question: {query}')
    print()
    
    # Initialize components
    intent_detector = EnhancedIntentDetector()
    data_aggregator = DataProviderAggregator()
    tech_calculator = EnhancedTechnicalCalculator()
    ai_client = AIClientWrapper()
    fact_verifier = TradingFactVerifier()
    
    # Step 1: Intent Detection
    print('1️⃣ Intent Detection...')
    analysis = await intent_detector.analyze_intent(query)
    print(f'   Intent: {analysis.primary_intent.value}')
    symbols = analysis.entities.get('symbols', [])
    print(f'   Symbols: {symbols}')
    print(f'   Confidence: {analysis.confidence}')
    print()

    # Step 2: Data Fetching
    print('2️⃣ Data Fetching...')
    data_results = {}
    for symbol in symbols:
        try:
            result = await data_aggregator.get_ticker(symbol)
            if result and not result.get('error'):
                data_results[symbol] = result
                current_price = result.get("price") or result.get("current_price") or result.get("close", "N/A")
                if isinstance(current_price, (int, float)):
                    formatted_price = format_price(current_price)
                    print(f'   ✅ {symbol}: {formatted_price}')
                else:
                    print(f'   ✅ {symbol}: {current_price}')
            else:
                print(f'   ❌ {symbol}: Failed to fetch data - {result.get("error", "Unknown error")}')
                data_results[symbol] = result
        except Exception as e:
            print(f'   ❌ {symbol}: Error - {e}')
            data_results[symbol] = {"error": str(e)}
    print()
    
    # Step 3: Technical Analysis
    print('3️⃣ Technical Analysis...')
    tech_results = {}
    for symbol in symbols:
        try:
            hist_data = await data_aggregator.get_history(symbol, period='1mo')
            if hist_data and hist_data.get('success'):
                indicators_result = await tech_calculator.calculate_indicators_from_historical_data(hist_data['data'])
                if indicators_result.indicators:
                    tech_results[symbol] = indicators_result.indicators
                    print(f'   ✅ {symbol}: {len(indicators_result.indicators)} indicators calculated')
                    # Debug: Show what indicators we actually have
                    print(f'      Available indicators: {list(indicators_result.indicators.keys())[:10]}...')  # Show first 10
                else:
                    print(f'   ⚠️ {symbol}: No indicators calculated')
            else:
                print(f'   ⚠️ {symbol}: No historical data available')
        except Exception as e:
            print(f'   ❌ {symbol}: Error - {e}')
    print()
    
    # Step 4: AI Analysis
    print('4️⃣ AI Analysis Generation...')
    
    # Create comprehensive prompt with strong anti-hallucination rules
    prompt = f'''You are a professional trading analyst. Today is September 20, 2025.

QUERY: {query}

REAL DATA PROVIDED:
- Intent: {analysis.primary_intent.value}
- Symbols: {symbols}
- Market Data: {data_results}
- Technical Indicators (ALL AVAILABLE): {tech_results}

IMPORTANT: The technical indicators above contain ALL available data. Do not claim any indicators are "missing" if they appear in the data above.

🚨 ABSOLUTE RULES - VIOLATION WILL RESULT IN SYSTEM FAILURE 🚨

❌ NEVER DO THESE THINGS:
1. NEVER invent prices, dates, or numbers not in the data above
2. NEVER create fake support/resistance levels
3. NEVER make up RSI, MACD, or indicator values
4. NEVER invent earnings dates or financial events
5. NEVER use fake timestamps like "October 2023" or "Q3 2023"
6. NEVER create template responses with fictional data
7. NEVER mention "52-week high", "all-time high", or historical extremes not in the data
8. NEVER claim indicators are "missing" if they appear in the technical indicators data above
9. NEVER mention market cap, PE ratio, dividend yield, or other fundamental data not provided
10. NEVER mention 52-week ranges or historical price extremes

✅ ONLY DO THESE THINGS:
1. Use ONLY the real data provided in the REAL DATA section above
2. If data is missing, clearly state "Data not available"
3. Provide educational content about technical analysis concepts
4. Suggest where to get missing real-time data
5. Remember it is September 2025, not 2023
6. Format all prices with 2 decimal places (e.g., $176.67 not $176.6699981689453)

RESPONSE STRUCTURE:
1. **Available Data Summary**: List exactly what real data you have
2. **Data Limitations**: State what critical data is missing
3. **Analysis**: Use only the provided real data
4. **Next Steps**: How to get missing data for complete analysis

If you violate these rules by inventing any financial data, the response will be rejected.'''

    try:
        response = await ai_client.generate_response(prompt)
        if response:
            print(f'   ✅ Response Generated ({len(response)} characters)')
            print()
            print('📊 AI RESPONSE:')
            print('=' * 60)
            print(response)
            print('=' * 60)
            print()
            
            # Step 5: Fact Verification
            print('5️⃣ Fact Verification...')
            fact_check = await fact_verifier.verify_response(response, data_results, symbols, analysis.primary_intent.value)
            print(f'   Valid: {fact_check.is_valid}')
            print(f'   Confidence: {fact_check.confidence:.2f}')
            print(f'   Issues: {len(fact_check.issues_found)}')
            if fact_check.issues_found:
                for issue in fact_check.issues_found[:3]:
                    print(f'     - {issue}')
            
            return response, fact_check
        else:
            print('   ❌ Failed to generate response')
            return None, None
    except Exception as e:
        print(f'   ❌ Error: {e}')
        return None, None

if __name__ == "__main__":
    # Run the test
    response, fact_check = asyncio.run(test_serious_question())
