#!/usr/bin/env python3
"""
Test the new AI model   with real API calls
"""

import asyncio
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Set up environment
os.environ.setdefault('OPENROUTER_API_KEY', 'your_openrouter_key_here')

async def test_new_model():
    """Test the new AI model with real API calls."""
    print("🤖 Testing New AI Model: Alibaba Tongyi DeepResearch")
    print("=" * 60)
    
    try:
        from src.shared.ai_chat.ai_client import AIClientWrapper
        
        # Initialize AI client with new model
        # Create a mock context object
        class MockContext:
            def __init__(self):
                self.pipeline_id = "model_test"
        
        context = MockContext()
        ai_client = AIClientWrapper(context)
        
        # Override the model to use Tongyi DeepResearch
        ai_client.model = "deepcogito/cogito-v2-preview-deepseek-671b"
        
        print("✅ AI client initialized successfully")
        print(f"🤖 Model: {ai_client.model}")
        
        # Test queries
        test_queries = [
            "waddup",
            "What's the current market sentiment?",
            "Give me a technical analysis of AAPL",
            "How are you doing today?",
            "What's the RSI for MSFT?"
        ]
        
        print(f"\n🧪 Testing {len(test_queries)} queries...")
        print("=" * 60)
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n🔍 Test {i}: {query}")
            
            try:
                # Generate response
                response = await ai_client.generate_response(query)
                
                if response:
                    print(f"✅ Response: {response[:200]}...")
                    print(f"📊 Response length: {len(response)} characters")
                else:
                    print("❌ No response generated")
                    
            except Exception as e:
                print(f"❌ Error: {e}")
        
        print("\n🎉 Model test completed!")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure the AI client module is available")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    asyncio.run(test_new_model())
