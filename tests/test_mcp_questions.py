#!/usr/bin/env python3
"""
Test MCP Server with Specific Stock Analysis Questions
Tests the enhanced MCP pipeline with the three target questions.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_mcp_questions():
    """Test the enhanced MCP pipeline with the three specific questions."""
    print("🧪 Testing MCP Server with Stock Analysis Questions...")
    print("=" * 70)
    
    try:
        from src.bot.pipeline.commands.ask2.pipeline import Ask2Pipeline
        
        # Initialize pipeline
        pipeline = Ask2Pipeline()
        print("✅ Enhanced pipeline initialized")
        
        # The three test questions
        test_questions = [
            "What is the current market sentiment on Tesla (TSLA) over the past week based on price action and volume trends?",
            "Using the latest data, is Apple (AAPL) showing bullish or bearish signals on the daily timeframe with RSI and MACD indicators?",
            "If I wanted to swing trade NVIDIA (NVDA) for the next 5 trading days, what would be the recommended entry, stop loss, and profit target levels?"
        ]
        
        results = []
        for i, question in enumerate(test_questions, 1):
            print(f"\n🔍 Test Question {i}: {question}")
            print("-" * 70)
            
            start_time = asyncio.get_event_loop().time()
            result = await pipeline.process_query(question)
            end_time = asyncio.get_event_loop().time()
            processing_time = end_time - start_time
            
            print(f"✅ Success: {result.success}")
            print(f"📊 MCP Used: {result.mcp_used}")
            print(f"🔄 Fallback Used: {result.fallback_used}")
            print(f"⭐ Data Quality: {getattr(result, 'data_quality', 'N/A')}")
            print(f"⏱️ Processing Time: {processing_time:.2f}s")
            print(f"📝 Response Length: {len(getattr(result, 'response', ''))} chars")
            
            if hasattr(result, 'error') and result.error:
                print(f"❌ Error: {result.error}")
            
            # Show full response
            response = getattr(result, 'response', 'No response')
            print(f"\n💬 Full Response:\n{response}\n")
            
            results.append({
                'question': question,
                'success': result.success,
                'mcp_used': result.mcp_used,
                'processing_time': processing_time,
                'response': response
            })
        
        await pipeline.close()
        
        # Summary
        print("\n" + "=" * 70)
        print("📊 TEST SUMMARY")
        print("=" * 70)
        
        successful = sum(1 for r in results if r['success'])
        total = len(results)
        
        for i, res in enumerate(results, 1):
            status = "✅ SUCCESS" if res['success'] else "❌ FAILED"
            mcp_status = "🟢 MCP" if res['mcp_used'] else "🔴 FALLBACK"
            print(f"Question {i}: {status} | {mcp_status} | Time: {res['processing_time']:.2f}s")
        
        print(f"\nOverall: {successful}/{total} questions successful ({successful/total*100:.1f}%)")
        
        if successful == total:
            print("🎉 All questions processed successfully! MCP server is working properly.")
        else:
            print("⚠️ Some questions failed. Check API keys and MCP server status.")
        
        # Check API key
        api_key = os.getenv('ALPHA_VANTAGE_API_KEY')
        if not api_key:
            print("\n⚠️ Note: ALPHA_VANTAGE_API_KEY not set - some features may use fallbacks.")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error - ensure src/bot/pipeline/commands/ask/enhanced_mcp_pipeline.py exists: {e}")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

async def main():
    """Run the MCP questions test."""
    print("🚀 MCP Stock Analysis Questions Test")
    print("=" * 70)
    
    success = await test_mcp_questions()
    print(f"\n{'🎉' if success else '⚠️'} Test {'completed successfully!' if success else 'had issues.'}")

if __name__ == "__main__":
    asyncio.run(main())