#!/usr/bin/env python3
"""
Comprehensive Test Suite Runner for ASK Pipeline

Runs all tests (unit + integration) with detailed reporting and coverage analysis.
"""

import pytest
import sys
import os
import time
from pathlib import Path

def main():
    """Run all tests for ASK pipeline"""
    print("🧪 ASK Pipeline - Complete Test Suite")
    print("=" * 60)
    
    # Add project root to path
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    # Test files to run
    test_files = [
        # Unit tests
        "src/bot/pipeline/commands/ask/tests/test_intent_detector.py",
        "src/bot/pipeline/commands/ask/tests/test_tool_orchestrator.py", 
        "src/bot/pipeline/commands/ask/tests/test_response_generator.py",
        "src/bot/pipeline/commands/ask/tests/test_error_handling.py",
        
        # Integration tests
        "test_ask_pipeline_simple_integration.py",
        
        # Comprehensive tests
        "test_ask_pipeline_comprehensive.py",
        "test_error_handling_system.py"
    ]
    
    print(f"📁 Discovering tests...")
    
    # Check which test files exist
    existing_tests = []
    for test_file in test_files:
        test_path = project_root / test_file
        if test_path.exists():
            existing_tests.append(str(test_path))
            print(f"  ✅ Found: {test_file}")
        else:
            print(f"  ⚠️  Missing: {test_file}")
    
    if not existing_tests:
        print("❌ No test files found!")
        return 1
    
    print(f"\n🚀 Running {len(existing_tests)} test files...")
    print("-" * 60)
    
    start_time = time.time()
    
    # Run pytest with comprehensive options
    pytest_args = [
        "-v",  # Verbose output
        "-s",  # Don't capture output
        "--tb=short",  # Short traceback format
        "--durations=10",  # Show 10 slowest tests
        "--disable-warnings",  # Disable warnings for cleaner output
        "--maxfail=5",  # Stop after 5 failures
    ] + existing_tests
    
    try:
        exit_code = pytest.main(pytest_args)
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return 1
    
    total_time = time.time() - start_time
    
    print("\n" + "=" * 60)
    print("📊 COMPREHENSIVE TEST SUMMARY")
    print("=" * 60)
    
    if exit_code == 0:
        print("🎉 ALL TESTS PASSED!")
        print(f"✅ Total execution time: {total_time:.2f}s")
        print(f"✅ Test files executed: {len(existing_tests)}")
        print("\n🏆 ASK Pipeline is ready for production!")
        print("\n📋 Test Coverage Summary:")
        print("   ✅ Unit Tests: Intent Detection, Tool Orchestration, Response Generation, Error Handling")
        print("   ✅ Integration Tests: End-to-end pipeline, Performance, Error handling")
        print("   ✅ Comprehensive Tests: Full pipeline validation")
        print("   ✅ Error Handling Tests: Fallback systems, Circuit breakers")
        
    else:
        print(f"❌ Tests failed with exit code: {exit_code}")
        print(f"⏱️  Execution time: {total_time:.2f}s")
        print("\n🔧 Please fix failing tests before proceeding.")
    
    return exit_code

def run_performance_benchmark():
    """Run performance benchmark tests"""
    print("\n🏃 Performance Benchmark")
    print("-" * 30)
    
    try:
        # Simple performance test
        import asyncio
        sys.path.append('.')
        from src.bot.pipeline.commands.ask.pipeline import AskPipeline, PipelineResult
        
        async def benchmark():
            pipeline = AskPipeline()
            
            queries = [
                "Hello!",
                "What's AAPL price?",
                "How are markets today?",
                "Analyze TSLA stock",
                "Show me Bitcoin performance"
            ]
            
            total_time = 0
            successful = 0
            
            for i, query in enumerate(queries):
                try:
                    start = time.time()
                    result = await pipeline.process(query, None, f"bench_{i}")
                    end = time.time()
                    
                    execution_time = end - start
                    total_time += execution_time
                    successful += 1
                    
                    print(f"  Query {i+1}: {execution_time:.2f}s - {'✅' if result.success else '❌'}")
                    
                except Exception as e:
                    print(f"  Query {i+1}: ❌ Failed - {e}")
            
            if successful > 0:
                avg_time = total_time / successful
                print(f"\n📊 Benchmark Results:")
                print(f"   Average time: {avg_time:.2f}s")
                print(f"   Success rate: {successful}/{len(queries)} ({successful/len(queries)*100:.1f}%)")
                print(f"   Total time: {total_time:.2f}s")
                
                if avg_time < 5.0:
                    print("   🚀 Performance: EXCELLENT")
                elif avg_time < 10.0:
                    print("   ✅ Performance: GOOD")
                else:
                    print("   ⚠️  Performance: NEEDS IMPROVEMENT")
        
        asyncio.run(benchmark())
        
    except Exception as e:
        print(f"❌ Benchmark failed: {e}")

if __name__ == "__main__":
    print("🧪 ASK Pipeline - Complete Test Suite Runner")
    print("=" * 60)
    
    # Run all tests
    exit_code = main()
    
    # Run performance benchmark if tests passed
    if exit_code == 0:
        run_performance_benchmark()
    
    print(f"\n📊 Test suite completed with exit code: {exit_code}")
    sys.exit(exit_code)
