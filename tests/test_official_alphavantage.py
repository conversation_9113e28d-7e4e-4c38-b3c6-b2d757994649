#!/usr/bin/env python3
"""
Test Official Alpha Vantage MCP Server
Tests the official hosted Alpha Vantage MCP server
"""

import asyncio
import json
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_official_alphavantage():
    """Test the official Alpha Vantage MCP server"""
    
    print("🧪 Testing Official Alpha Vantage MCP Server...")
    
    # Check if API key is set
    api_key = os.getenv("ALPHA_VANTAGE_API_KEY")
    if not api_key:
        print("❌ ALPHA_VANTAGE_API_KEY not set")
        print("   Get your free API key from: https://www.alphavantage.co/support/#api-key")
        return False
    
    print(f"✅ API Key found: {api_key[:8]}...")
    
    try:
        # Test MCP client configuration
        from src.shared.mcp.mcp_client_config import mcp_config, MCPServerType
        
        print("\n📋 MCP Configuration:")
        alphavantage_server = mcp_config.get_server_by_type(MCPServerType.ALPHAVANTAGE)
        if alphavantage_server:
            print(f"  Server: {alphavantage_server.name}")
            print(f"  URL: {alphavantage_server.url}")
            print(f"  Enabled: {alphavantage_server.enabled}")
            print(f"  Tools: {len(alphavantage_server.tools)}")
            print(f"  Sample tools: {alphavantage_server.tools[:5]}")
        
        # Test Docker MCP client
        try:
            from src.shared.mcp.docker_mcp_client import get_docker_mcp_client, MCPToolCall, MCPServerType
            
            print("\n🔌 Testing MCP Client...")
            client = await get_docker_mcp_client()
            
            # Test health check
            health = await client.health_check()
            print(f"Overall Status: {health['overall_status']}")
            print(f"Total Tools: {health['total_tools']}")
            
            # Test AlphaVantage tools
            print("\n📈 Testing AlphaVantage GLOBAL_QUOTE tool...")
            tool_call = MCPToolCall(
                tool_name="GLOBAL_QUOTE",
                parameters={"symbol": "AAPL"},
                server_type=MCPServerType.ALPHAVANTAGE
            )
            
            result = await client.call_tool(tool_call)
            if result.success:
                print(f"✅ Stock quote result: {result.data}")
            else:
                print(f"❌ Stock quote failed: {result.error}")
            
            await client.close()
            
        except Exception as e:
            print(f"⚠️ MCP Client test failed: {e}")
            print("   This is expected if containers aren't running")
        
        print("\n✅ Official Alpha Vantage MCP Server test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_official_alphavantage())
    sys.exit(0 if success else 1)