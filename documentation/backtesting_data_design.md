# Backtesting Data Structures Design

## Overview
This design defines the data models and structures for historical and simulated trade data in `src/shared/trading/strategies`. It builds on the existing `market_data` dict from fetch_data.py (OHLCV historical, technical indicators) to support backtesting with Backtrader. Structures use Pydantic for validation, pandas for time-series handling, and SQLAlchemy for Postgres storage if needed. Goal: Enable strategy simulation on historical data, position tracking, and performance metrics.

## Core Data Models

### 1. Historical Data Model
Represents fetched historical OHLCV data for backtesting.

```python
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime
import pandas as pd

class HistoricalBar(BaseModel):
    """Single OHLCV bar."""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: Optional[float] = None
    symbol: str

class HistoricalData(BaseModel):
    """Collection of bars for a symbol."""
    symbol: str
    timeframe: str  # e.g., '1d', '1h'
    bars: List[HistoricalBar]
    start_date: datetime
    end_date: datetime
    source: str  # e.g., 'polygon'

    @classmethod
    def from_market_data(cls, market_data: Dict, symbol: str, timeframe: str = '1d') -> 'HistoricalData':
        """Create from pipeline market_data['historical']."""
        bars = []
        for bar in market_data.get('historical', []):
            bars.append(HistoricalBar(
                timestamp=datetime.fromisoformat(bar['date'].replace('Z', '+00:00') if 'Z' in bar['date'] else bar['date']),
                open=bar['open'],
                high=bar['high'],
                low=bar['low'],
                close=bar['close'],
                volume=bar.get('volume'),
                symbol=symbol
            ))
        if bars:
            return cls(
                symbol=symbol,
                timeframe=timeframe,
                bars=bars,
                start_date=min(b.timestamp for b in bars),
                end_date=max(b.timestamp for b in bars),
                source=market_data.get('provider', 'unknown')
            )
        return cls(symbol=symbol, timeframe=timeframe, bars=[], start_date=datetime.now(), end_date=datetime.now(), source='unknown')

    def to_dataframe(self) -> pd.DataFrame:
        """Convert to pandas for Backtrader feed."""
        if not self.bars:
            return pd.DataFrame()
        data = [{'datetime': bar.timestamp, 'open': bar.open, 'high': bar.high, 
                 'low': bar.low, 'close': bar.close, 'volume': bar.volume or 0, 
                 'openinterest': 0} for bar in self.bars]
        df = pd.DataFrame(data)
        df['datetime'] = pd.to_datetime(df['datetime'])
        df.set_index('datetime', inplace=True)
        return df
```

- Integration: Call `HistoricalData.from_market_data(context.processing_results['market_data'], ticker)` in pipeline.
- Storage: Serialize `bars` as JSON in Postgres table `historical_data` for reuse.

### 2. Trade Record Model
For simulated trades during backtesting.

```python
class TradeRecord(BaseModel):
    """Single simulated trade."""
    id: Optional[int] = None
    symbol: str
    entry_time: datetime
    exit_time: Optional[datetime] = None
    entry_price: float
    exit_price: Optional[float] = None
    quantity: int
    side: str  # 'buy' or 'sell'
    strategy: str  # e.g., 'sma_crossover'
    pnl: Optional[float] = None
    pnl_pct: Optional[float] = None
    status: str  # 'open', 'closed'

class PortfolioSnapshot(BaseModel):
    """Portfolio state at a point in time."""
    timestamp: datetime
    cash: float
    positions: Dict[str, float]  # symbol -> quantity
    total_value: float
    unrealized_pnl: float
```

- Use: In Backtrader strategy, override `notify_trade` to create TradeRecord and append to list.
- Storage: Insert to Postgres `trades` table (id auto, backtest_id FK).

### 3. Strategy Definition Model
Defines a trading strategy for backtesting.

```python
class StrategySignal(BaseModel):
    """Signal generated by strategy."""
    symbol: str
    action: str  # 'buy', 'sell', 'hold'
    price: float
    timestamp: datetime
    confidence: float  # 0-1

class TradingStrategy(BaseModel):
    """Base strategy config."""
    name: str
    parameters: Dict[str, Any]  # e.g., {'sma_short': 10, 'sma_long': 30}
    indicators: List[str]  # e.g., ['sma', 'rsi']
    risk_per_trade: float = 0.02  # 2% of portfolio

# Example: SMA Crossover Strategy
class SMACrossoverStrategy(TradingStrategy):
    short_window: int = 10
    long_window: int = 30
```

- Integration: Load from config.yaml or DB, pass parameters to Backtrader strategy class.
- Storage: `strategies` table for persistence.

### 4. Backtest Results Model
Aggregates performance metrics.

```python
class BacktestResult(BaseModel):
    """Backtest output."""
    strategy: str
    symbol: str
    start_date: datetime
    end_date: datetime
    initial_capital: float
    final_value: float
    total_return: float
    total_trades: int
    win_rate: float
    max_drawdown: float
    sharpe_ratio: float
    trades: List[TradeRecord]
    equity_curve: List[PortfolioSnapshot]  # For plotting

    def to_dataframe(self) -> pd.DataFrame:
        """For analysis/visualization."""
        if self.trades:
            return pd.DataFrame([t.dict() for t in self.trades])
        return pd.DataFrame()
```

## Integration with Existing System
- **Data Input**: In ask/analyze pipeline, after fetch_data, create HistoricalData and pass to backtest if command requests it.
- **Backtrader Feed**: `cerebro.adddata(bt.feeds.PandasData(dataname=historical.to_dataframe()))`.
- **Strategy Execution**: Custom Backtrader strategy imports from ai_services for indicators (e.g., technical_analysis_processor.compute_sma()).
- **Output**: Save BacktestResult to Postgres `backtest_results` table, use for P&L in performance_analytics.
- **Optimization**: Backtrader's `optstrategy` with TradingStrategy.parameters.

## Storage Schema (Postgres)
- `historical_data`: id, symbol, timeframe, bars_json, start_date, end_date, source.
- `trades`: id, backtest_id, symbol, entry_time, exit_time, entry_price, exit_price, quantity, side, strategy, pnl, pnl_pct, status.
- `backtest_results`: id, strategy, symbol, start_date, end_date, initial_capital, final_value, total_return, total_trades, win_rate, max_drawdown, sharpe_ratio.
- `strategies`: id, name, parameters_json, risk_per_trade.

This design ensures compatibility with async pipeline, scalable storage, and easy extension for portfolio/multi-asset backtesting. Next: Implement engine.