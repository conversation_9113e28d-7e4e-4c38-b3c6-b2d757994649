# Project File Manifest for `tradingview-automatio`

This file lists all files and folders for auditing purposes. Use the 'Audit Notes' section below to keep notes.

**Summary:** 274 directories, 1087 files, ~193,502 non-empty lines.

### File Tree
```text
tradingview-automatio/
├── .cursor/
│   └── config.json  (13 lines)
├── .cursorignore  (2 lines)
├── .dockerignore  (24 lines)
├── .gitignore  (63 lines)
├── .kiro/
│   └── specs/
│       └── commands-audit/
├── DOCKER_BUILD_OPTIMIZATION.md  (265 lines)
├── Dockerfile  (71 lines)
├── PROJECT_OVERVIEW.md  (242 lines)
├── README.md  (55 lines)
├── alembic/
│   ├── env.py  (52 lines)
│   └── versions/
│       └── .gitkeep  (1 lines)
├── alembic.ini  (38 lines)
├── architecture_detailed.json  (4068 lines)
├── architecture_pretty.json  (3223 lines)
├── backups/
│   └── stub-removed/
│       ├── analyze.py  (31 lines)
│       ├── help.py  (60 lines)
│       ├── portfolio.py  (37 lines)
│       ├── recommendations.py  (31 lines)
│       ├── watchlist.py  (39 lines)
│       └── zones.py  (31 lines)
├── check_table_structure.py  (87 lines)
├── check_tables.py  (102 lines)
├── claude.html  (817 lines)
├── cleanup_validation_report.txt  (52 lines)
├── codebase_explorer_requirements.txt  (2 lines)
├── comprehensive_market_analysis_report_20250916_001933.json  (1994 lines)
├── comprehensive_test.py  (162 lines)
├── config/
│   ├── ai_models.yaml  (207 lines)
│   ├── environments/
│   ├── performance_monitoring.conf  (40 lines)
│   ├── schemas/
│   └── services/
│       ├── ai_models.yaml  (207 lines)
│       └── performance_monitoring.conf  (40 lines)
├── config_api.py  (262 lines)
├── create_ai_query_interpreter.py  (197 lines)
├── create_fixed_pipeline.py  (124 lines)
├── create_watchlist_tables.py  (69 lines)
├── create_working_pipeline.py  (107 lines)
├── current_test_files.txt  (130 lines)
├── dashboard/
│   ├── auto_update.sh  (53 lines)
│   ├── dashboard.pid  (1 lines)
│   ├── dashboard_generator.py  (367 lines)
│   ├── fix_docker_network.py  (201 lines)
│   ├── monitor_services.py  (175 lines)
│   ├── performance_dashboard.html  (355 lines)
│   ├── pipeline_analyzer.py  (215 lines)
│   ├── pipeline_data.json  (421 lines)
│   ├── system_dashboard.html  (990 lines)
│   ├── test.txt  (1 lines)
│   └── update_dashboard.sh  (28 lines)
├── dashboard_requirements.txt  (15 lines)
├── data/
│   ├── context/
│   │   ├── conv_discord:conv_-2997810244074675534_1757641467.153141.json  (13 lines)
│   │   ├── conv_discord:conv_-302142993699364046_1757641523.138229.json  (13 lines)
│   │   ├── conv_discord:conv_-4078157315345074310_1757644518.60983.json  (13 lines)
│   │   ├── conv_discord:conv_-5387016997699176859_1757644598.085874.json  (13 lines)
│   │   ├── conv_discord:conv_-8003830481273157522_1757641563.529192.json  (13 lines)
│   │   ├── conv_discord:conv_6241505781410727370_1757644456.861526.json  (13 lines)
│   │   ├── conv_discord:conv_701543426776952118_1757641897.618009.json  (13 lines)
│   │   ├── conv_discord:conv_7999110414914176335_1757641950.212137.json  (13 lines)
│   │   ├── platform_mappings.json  (17 lines)
│   │   ├── user_01712a8341d64ca9ae62cd587e6ba462.json  (11 lines)
│   │   ├── user_05d765e5af214a7dae9b532efa179d30.json  (11 lines)
│   │   ├── user_1ddc99a5a9054ca6a352a7593dcdf332.json  (24 lines)
│   │   ├── user_4218863a215744e8855c184fcb18670f.json  (11 lines)
│   │   ├── user_5295572a71014768b632a1a036a9dc89.json  (11 lines)
│   │   ├── user_5f842e6b604b4d3999e59434ea478771.json  (11 lines)
│   │   ├── user_66be084406a642e4b9c05d6ef1a604c7.json  (11 lines)
│   │   ├── user_7906548e940748448e7a10b0419ce9c6.json  (24 lines)
│   │   ├── user_d49780f64205402e96b8c0eb82a11c46.json  (11 lines)
│   │   ├── user_efa09e4e1d034153b19d978f38277f11.json  (11 lines)
│   │   └── user_f2f17b905d4c41ed871cdae63e3f9ea5.json  (24 lines)
│   ├── models/
│   │   └── sentiment/
│   │       ├── financial_terms.json  (16 lines)
│   │       ├── sentiment_weights.json  (8 lines)
│   │       └── word_embeddings.json  (134 lines)
│   ├── response_metrics.json  (8 lines)
│   ├── tickers/
│   │   ├── all_tickers.txt  (6659 lines)
│   │   ├── nasdaq_tickers.txt  (4001 lines)
│   │   └── nyse_tickers.txt  (3115 lines)
│   └── watchlists/
│       └── test_user_123_watchlist.json  (54 lines)
├── debug_ast.py  (37 lines)
├── debug_data_format.py  (37 lines)
├── debug_pattern_matching.py  (26 lines)
├── debug_sentiment.py  (18 lines)
├── debug_sentiment_detailed.py  (25 lines)
├── debug_sentiment_model.py  (17 lines)
├── demo_dashboard.py  (132 lines)
├── demo_enhanced_analysis.py  (238 lines)
├── docker/
│   ├── README.md  (176 lines)
│   ├── compose/
│   │   ├── config/
│   │   ├── data/
│   │   ├── development.optimized.yml  (138 lines)
│   │   ├── development.yml  (178 lines)
│   │   ├── logs/
│   │   ├── production.yml  (214 lines)
│   │   ├── services/
│   │   ├── src/
│   │   └── tests/
│   ├── config/
│   │   └── .gitkeep  (1 lines)
│   └── dockerfiles/
│       ├── app.Dockerfile  (51 lines)
│       └── services/
│           ├── api.Dockerfile  (36 lines)
│           ├── discord-bot.Dockerfile  (34 lines)
│           ├── monitor.Dockerfile  (28 lines)
│           ├── processor.Dockerfile  (28 lines)
│           ├── tradingview-ingest.Dockerfile  (41 lines)
│           ├── webhook-ingest.Dockerfile  (36 lines)
│           └── webhook.Dockerfile  (41 lines)
├── docker-compose.production.yml  (214 lines)
├── docker-compose.yml  (190 lines)
├── docker-entrypoint.sh  (52 lines)
├── docker-nuke  (21 lines)
├── docs/
│   ├── 1_webhook_ingestion.md  (88 lines)
│   ├── 2_discord_bot.md  (47 lines)
│   ├── 3_ai_pipeline.md  (44 lines)
│   ├── 4_data_and_database.md  (31 lines)
│   ├── 5_core_components.md  (30 lines)
│   ├── AI_ENHANCEMENT_RECOMMENDATIONS.md  (176 lines)
│   ├── AI_IMPLEMENTATION_GUIDE.md  (158 lines)
│   ├── ARCHITECTURE_DECISION.md  (81 lines)
│   ├── AUTOMATION_ARCHITECTURE.md  (418 lines)
│   ├── COMMAND_PIPELINE_AUDIT_COMPLETE.md  (200 lines)
│   ├── COMPREHENSIVE_TEST_REPORT.md  (193 lines)
│   ├── PERFORMANCE_OPTIMIZATION_IMPLEMENTATION_COMPLETE.md  (182 lines)
│   ├── README.md  (75 lines)
│   ├── REDIS_CONTAINER_SECURITY_AUDIT.md  (159 lines)
│   ├── REGEX_TO_AI_AUDIT.md  (166 lines)
│   ├── WEBHOOK_NGROK_SETUP_GUIDE.md  (205 lines)
│   ├── api/
│   │   └── .gitkeep  (1 lines)
│   ├── archive/
│   │   ├── AI_ANSWER_QUALITY_ANALYSIS.md  (117 lines)
│   │   ├── AI_DEBUGGER_SUMMARY.md  (124 lines)
│   │   ├── AI_ENHANCEMENT_RECOMMENDATIONS.md  (176 lines)
│   │   ├── AI_HALLUCINATION_DATA_INTEGRITY_AUDIT.md  (158 lines)
│   │   ├── AI_IMPLEMENTATION_GUIDE.md  (158 lines)
│   │   ├── AI_MODEL_OPTIMIZATION_IMPLEMENTATION_SUMMARY.md  (170 lines)
│   │   ├── AI_POWERED_QUERY_INTERPRETER_SUMMARY.md  (104 lines)
│   │   ├── AI_PRICE_HALLUCINATION_FIX_SUMMARY.md  (72 lines)
│   │   ├── AI_PROCESSOR_CONSOLIDATION_PLAN.md  (104 lines)
│   │   ├── AI_QUERY_INTERPRETER_FIXES_SUMMARY.md  (101 lines)
│   │   ├── ALERT_FORMAT_UPGRADE_SUMMARY.md  (91 lines)
│   │   ├── ANALYSIS_SELF_EVALUATION_SYSTEM.md  (85 lines)
│   │   ├── ASK_COMMAND_ENHANCEMENTS.md  (43 lines)
│   │   ├── ASK_VS_ANALYZE_COMMAND_AUDIT.md  (124 lines)
│   │   ├── AUTOMATION_IMPLEMENTATION_CHECKLIST.md  (243 lines)
│   │   ├── BACKUP_deleted_files_log.md  (45 lines)
│   │   ├── CANONICAL_MODULES_GUIDE.md  (72 lines)
│   │   ├── CODEBASE_EXPLORER_SUMMARY.md  (101 lines)
│   │   ├── CODE_DEDUPLICATION_PLAN.md  (293 lines)
│   │   ├── COMMANDS.md  (238 lines)
│   │   ├── COMMANDS_ANALYSIS.md  (112 lines)
│   │   ├── COMMAND_AUDIT_REPORT.md  (139 lines)
│   │   ├── COMPLETE_ANALYSIS_SYSTEM.md  (100 lines)
│   │   ├── COMPREHENSIVE_ANALYSIS_AUDIT.md  (202 lines)
│   │   ├── COMPREHENSIVE_CODEBASE_AUDIT_2025.md  (299 lines)
│   │   ├── COMPREHENSIVE_CODEBASE_AUDIT_REPORT.md  (267 lines)
│   │   ├── COMPREHENSIVE_ENVIRONMENT_FIXES_SUMMARY.md  (144 lines)
│   │   ├── COMPREHENSIVE_SECURITY_AUDIT.md  (55 lines)
│   │   ├── COMPREHENSIVE_TEST_REPORT.md  (193 lines)
│   │   ├── CONSOLIDATION_COMPLETE_SUMMARY.md  (122 lines)
│   │   ├── CONSOLIDATION_PLAN.md  (65 lines)
│   │   ├── CRITICAL_ARCHITECTURE_AUDIT.md  (32 lines)
│   │   ├── CRITICAL_DUPLICATION_AUDIT.md  (175 lines)
│   │   ├── CRITICAL_FIXES_SUMMARY.md  (48 lines)
│   │   ├── CURRENT_STATUS.md  (42 lines)
│   │   ├── DASHBOARD_SUMMARY.md  (69 lines)
│   │   ├── DATABASE_CONSOLIDATION_PLAN.md  (115 lines)
│   │   ├── DATA_PROVIDER_MIGRATION_PLAN.md  (73 lines)
│   │   ├── DEPLOYMENT_GUIDE.md  (330 lines)
│   │   ├── DISCLAIMER_FIX_SUMMARY.md  (44 lines)
│   │   ├── DOCKER.md  (27 lines)
│   │   ├── DOCKER_CONSOLIDATION_PLAN.md  (154 lines)
│   │   ├── DOCKER_HEALTH_CHECK_AUDIT.md  (75 lines)
│   │   ├── DOCKER_SUMMARY.md  (86 lines)
│   │   ├── DUPLICATION_AUDIT_ACTION_PLAN.md  (135 lines)
│   │   ├── ENHANCED_AI_CONTEXT_AND_CLASSIFICATION.md  (101 lines)
│   │   ├── ENHANCED_ANALYSIS_IMPLEMENTATION.md  (244 lines)
│   │   ├── ENHANCED_HYBRID_FINAL_SUMMARY.md  (123 lines)
│   │   ├── ENHANCEMENT_CHECKLIST.md  (283 lines)
│   │   ├── ENVIRONMENT_FIX_REPORT.md  (59 lines)
│   │   ├── ENV_VARIABLES.md  (53 lines)
│   │   ├── FAILURE_ANALYSIS_AND_FIX.md  (86 lines)
│   │   ├── FALLBACK_REMEDIATION_SUMMARY.md  (161 lines)
│   │   ├── FALLBACK_VALUE_ANALYSIS.md  (154 lines)
│   │   ├── FINAL_AUDIT_REPORT.md  (70 lines)
│   │   ├── FINAL_FIX_SUMMARY.md  (68 lines)
│   │   ├── FINAL_LOG_ANALYSIS.md  (61 lines)
│   │   ├── FINAL_STATUS_AND_TEST.md  (61 lines)
│   │   ├── FINAL_TEST_RESULTS.md  (69 lines)
│   │   ├── FINAL_WORKING_STATUS.md  (65 lines)
│   │   ├── Gemini.md  (158 lines)
│   │   ├── HEALTH_MONITORING.md  (131 lines)
│   │   ├── HYBRID_APPROACH_SOLUTION.md  (118 lines)
│   │   ├── IMPLEMENTATION_STRATEGY.md  (158 lines)
│   │   ├── IMPLEMENTATION_SUMMARY.md  (104 lines)
│   │   ├── INTELLIGENT_GRADING_SUMMARY.md  (58 lines)
│   │   ├── LEGACY_CODE_EXTERNAL_IMPACT_ANALYSIS.md  (145 lines)
│   │   ├── LEGACY_CODE_REMOVAL_PLAN.md  (121 lines)
│   │   ├── LICENSE.md  (17 lines)
│   │   ├── METRICS_DOCUMENTATION.md  (284 lines)
│   │   ├── MIGRATION_GUIDE.md  (133 lines)
│   │   ├── NETWORK_ARCHITECTURE.md  (108 lines)
│   │   ├── PERFORMANCE_FIXES_SUMMARY.md  (70 lines)
│   │   ├── PERFORMANCE_IMPROVEMENTS.md  (133 lines)
│   │   ├── PERFORMANCE_OPTIMIZATION_IMPLEMENTATION_COMPLETE.md  (182 lines)
│   │   ├── PERFORMANCE_OPTIMIZATION_SUMMARY.md  (95 lines)
│   │   ├── PHASE2_COMPLETION_SUMMARY.md  (90 lines)
│   │   ├── PIPELINE.md  (145 lines)
│   │   ├── PIPELINE_MONITORING.md  (316 lines)
│   │   ├── PRODUCTION_DEPLOYMENT.md  (144 lines)
│   │   ├── PROFESSIONAL_ASSESSMENT_RESPONSE.md  (123 lines)
│   │   ├── PROJECT_STATUS_SUMMARY.md  (60 lines)
│   │   ├── README_ARCHITECTURE_UPDATE.md  (39 lines)
│   │   ├── README_CODEBASE_EXPLORER.md  (157 lines)
│   │   ├── README_DEBUG_CONSOLE.md  (76 lines)
│   │   ├── REDIS_CONTAINER_SECURITY_AUDIT.md  (159 lines)
│   │   ├── REFACTORING_GUIDE.md  (102 lines)
│   │   ├── REFACTORING_STATUS.md  (170 lines)
│   │   ├── REGEX_TO_AI_AUDIT.md  (166 lines)
│   │   ├── SECRETS.md  (49 lines)
│   │   ├── SECURITY.md  (140 lines)
│   │   ├── SECURITY_ARCHITECTURE.md  (135 lines)
│   │   ├── SECURITY_AUDIT_REPORT.md  (55 lines)
│   │   ├── SECURITY_CHECKLIST.md  (94 lines)
│   │   ├── SHARED_MODULE_IMPROVEMENTS.md  (164 lines)
│   │   ├── SUPABASE_INTEGRATION.md  (135 lines)
│   │   ├── SYSTEM_ANALYSIS_CRITIQUE.md  (125 lines)
│   │   ├── TECHNICAL_ANALYSIS_CAPABILITIES_AUDIT.md  (158 lines)
│   │   ├── TECHNICAL_ANALYSIS_CONSOLIDATION_PLAN.md  (133 lines)
│   │   ├── TEST_CONSOLIDATION_ANALYSIS.md  (91 lines)
│   │   ├── TEST_SUMMARY.md  (148 lines)
│   │   ├── UNIFIED_TASKS.md  (155 lines)
│   │   ├── USAGE.md  (136 lines)
│   │   ├── WEBHOOK_NGROK_SETUP_GUIDE.md  (205 lines)
│   │   ├── ZONES_ENHANCEMENT_SUMMARY.md  (67 lines)
│   │   ├── analysis_summary.md  (76 lines)
│   │   ├── api_container_requirements.md  (66 lines)
│   │   ├── comprehensive_test_report.md  (137 lines)
│   │   ├── config_consolidation_summary.md  (74 lines)
│   │   ├── container_debug_todo.md  (65 lines)
│   │   ├── container_requirements_summary.md  (58 lines)
│   │   ├── create_tables_step_by_step.md  (90 lines)
│   │   ├── critical_issues_report.md  (89 lines)
│   │   ├── design.md  (225 lines)
│   │   ├── discord_bot_container_requirements.md  (86 lines)
│   │   ├── folder_structure_analysis.md  (172 lines)
│   │   ├── immediatetasklist..md  (146 lines)
│   │   ├── index.md  (9 lines)
│   │   ├── market_data_api.md  (211 lines)
│   │   ├── newestchecklist.md  (185 lines)
│   │   ├── newfolders.md  (37 lines)
│   │   ├── pipeline_monitoring_report.md  (89 lines)
│   │   ├── pipeline_performance_baseline.md  (161 lines)
│   │   ├── redis_container_requirements.md  (43 lines)
│   │   ├── requirements.md  (39 lines)
│   │   ├── rules.md  (49 lines)
│   │   ├── task.md  (109 lines)
│   │   ├── task2.md  (185 lines)
│   │   ├── tasks.bot.md  (57 lines)
│   │   ├── tasks.md  (166 lines)
│   │   ├── tasks123.md  (232 lines)
│   │   ├── test_audit_tracker.md  (182 lines)
│   │   ├── test_checklist.md  (58 lines)
│   │   ├── test_files_audit.md  (182 lines)
│   │   ├── test_results_post_restart.md  (104 lines)
│   │   ├── todo.md  (46 lines)
│   │   ├── tomorrow.md  (328 lines)
│   │   ├── updated_comprehensive_test_report.md  (175 lines)
│   │   └── webhook_ingest_container_requirements.md  (82 lines)
│   ├── audit/
│   │   └── .gitkeep  (1 lines)
│   ├── cleanup/
│   ├── consolidated/
│   ├── system_audit/
│   │   └── .gitkeep  (1 lines)
│   ├── testing/
│   │   └── .gitkeep  (1 lines)
│   └── user/
│       └── .gitkeep  (1 lines)
├── example_pipeline_integration.py  (100 lines)
├── examples/
│   ├── advanced/
│   │   └── debug_event_integration.py  (95 lines)
│   ├── basic/
│   │   └── database_usage.py  (71 lines)
│   ├── intermediate/
│   │   └── ask_command_with_evaluation.py  (140 lines)
│   └── templates/
├── final_docs_cleanup.py  (43 lines)
├── final_working_solution.py  (225 lines)
├── fix_ai_parsing_errors.py  (129 lines)
├── fix_ai_price_hallucination.py  (148 lines)
├── fix_ai_processor.py  (184 lines)
├── fix_ai_processor_constructor.py  (110 lines)
├── fix_ai_query_interpretation.py  (187 lines)
├── fix_api_keys.py  (81 lines)
├── fix_ask_command_simple.py  (63 lines)
├── fix_chatbot_complete.py  (220 lines)
├── fix_circuit_breaker_error.py  (23 lines)
├── fix_clean_responses.py  (184 lines)
├── fix_context_parameter.py  (25 lines)
├── fix_data_provider.py  (61 lines)
├── fix_discord_final.py  (59 lines)
├── fix_discord_helpers.py  (70 lines)
├── fix_discord_interaction.py  (93 lines)
├── fix_discord_interaction_final.py  (74 lines)
├── fix_discord_send.py  (50 lines)
├── fix_f_string.py  (20 lines)
├── fix_logger_import.py  (20 lines)
├── fix_missing_method.py  (111 lines)
├── fix_mock_issue.py  (73 lines)
├── fix_pattern_matching_order.py  (112 lines)
├── fix_pipeline_ai.py  (86 lines)
├── fix_pipeline_data_structure.py  (28 lines)
├── fix_pipeline_export.py  (17 lines)
├── fix_pipeline_export_final.py  (20 lines)
├── fix_pipeline_grader.py  (44 lines)
├── fix_processor_exception.py  (184 lines)
├── fix_real_processing.py  (217 lines)
├── fix_symbol_extraction.py  (45 lines)
├── fix_symbol_extraction_ai.py  (109 lines)
├── fix_timing_only.py  (30 lines)
├── fix_trading_keywords.py  (25 lines)
├── fix_user_id_parameter.py  (25 lines)
├── fix_username_parameter.py  (25 lines)
├── fix_validation_final.py  (257 lines)
├── fix_validation_logic.py  (265 lines)
├── fixed_ai_service_wrapper.py  (470 lines)
├── folder_cleanup_plan.md  (97 lines)
├── generate_and_send_report.py  (75 lines)
├── generate_architecture.py  (294 lines)
├── generate_detailed_architecture.py  (256 lines)
├── import_test.py  (25 lines)
├── improve_pattern_matching.py  (23 lines)
├── markdown_backup/
├── market_analysis_summary_20250916_001933.txt  (7 lines)
├── nginx/
│   ├── logs/
│   ├── nginx.conf  (87 lines)
│   ├── ssl/
│   └── webhook.conf  (51 lines)
├── ngrok.yml  (11 lines)
├── nvda_price_target_analysis.py  (139 lines)
├── nvda_price_target_analysis_modular.py  (268 lines)
├── optimize_bot_performance.py  (116 lines)
├── optimize_performance.py  (96 lines)
├── performance_optimization.py  (68 lines)
├── pipeline_events.py  (358 lines)
├── postgres/
│   └── logs/
├── pyproject.toml  (29 lines)
├── pytest.ini  (20 lines)
├── pytest_comprehensive.ini  (44 lines)
├── quick_start_db.py  (62 lines)
├── redis/
│   └── logs/
├── redis.conf  (91 lines)
├── requirements/
│   ├── README.md  (155 lines)
│   ├── containers/
│   │   ├── Dockerfile.api  (36 lines)
│   │   ├── Dockerfile.discord_bot  (34 lines)
│   │   ├── Dockerfile.webhook_ingest  (36 lines)
│   │   ├── api_minimal_requirements.txt  (26 lines)
│   │   ├── discord_bot_minimal_requirements.txt  (30 lines)
│   │   ├── docker-compose.optimized.yml  (138 lines)
│   │   ├── redis_minimal_requirements.txt  (3 lines)
│   │   └── webhook_ingest_minimal_requirements.txt  (22 lines)
│   ├── environments/
│   │   ├── base.txt  (30 lines)
│   │   ├── development.txt  (37 lines)
│   │   └── production.txt  (29 lines)
│   └── services/
│       ├── api.txt  (27 lines)
│       ├── discord-bot.txt  (23 lines)
│       └── webhook-ingest.txt  (26 lines)
├── requirements.txt  (71 lines)
├── response_metrics.json  (36 lines)
├── scripts/
│   ├── additional_files_to_delete.sh  (48 lines)
│   ├── apply_performance_optimizations.py  (330 lines)
│   ├── auto_cleanup.sh  (26 lines)
│   ├── check_before_delete.sh  (80 lines)
│   ├── check_docker_configs.sh  (50 lines)
│   ├── cleanup_pycache.sh  (12 lines)
│   ├── consolidate_supabase.sh  (53 lines)
│   ├── consolidate_watchlist.sh  (223 lines)
│   ├── data/
│   │   └── .gitkeep  (1 lines)
│   ├── docker_security_scan.sh  (88 lines)
│   ├── enhance_pipeline.sh  (600 lines)
│   ├── final_cleanup.sh  (76 lines)
│   ├── fix_data_providers.py  (146 lines)
│   ├── fix_docker_compose_secrets.sh  (75 lines)
│   ├── fix_env_variables.sh  (36 lines)
│   ├── fix_security_issues.sh  (318 lines)
│   ├── generate_deprecation_report.py  (270 lines)
│   ├── generate_docker_secrets.sh  (111 lines)
│   ├── generate_secrets.py  (111 lines)
│   ├── generate_self_signed_cert.sh  (28 lines)
│   ├── integrate_pipeline_monitoring.sh  (511 lines)
│   ├── list_files_for_deletion.sh  (80 lines)
│   ├── logs/
│   ├── manage_migrations.py  (87 lines)
│   ├── manage_secrets.py  (116 lines)
│   ├── monitor_deprecation.py  (118 lines)
│   ├── print_zones.py  (23 lines)
│   ├── redis_management.py  (247 lines)
│   ├── redis_security_audit.py  (284 lines)
│   ├── run_pipeline_monitoring.sh  (199 lines)
│   ├── run_security_checks.sh  (38 lines)
│   ├── run_security_tests.py  (32 lines)
│   ├── run_tests.py  (88 lines)
│   ├── safe_hardcoded_fix.py  (138 lines)
│   ├── security_cleanup.py  (186 lines)
│   ├── security_test.py  (211 lines)
│   ├── setup_letsencrypt.sh  (92 lines)
│   ├── start_performance_dashboard.sh  (26 lines)
│   ├── test_ai_memory.py  (344 lines)
│   ├── test_db_in_docker.sh  (39 lines)
│   ├── test_fallback_remediation.py  (174 lines)
│   ├── test_full_analysis.py  (63 lines)
│   ├── update_debug_console.py  (168 lines)
│   └── update_imports.py  (124 lines)
├── security-requirements.txt  (7 lines)
├── simple_db_test.py  (78 lines)
├── simple_pipeline_emitter.py  (155 lines)
├── simple_test.py  (74 lines)
├── src/
│   ├── __init__.py  (0 lines)
│   ├── __pycache__/
│   ├── analysis/
│   │   ├── __init__.py  (18 lines)
│   │   ├── __pycache__/
│   │   ├── ai/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── calculators/
│   │   │   │   ├── __init__.py  (0 lines)
│   │   │   │   └── sentiment_calculator.py  (82 lines)
│   │   │   ├── enhancement_strategy.py  (226 lines)
│   │   │   └── recommendation_engine.py  (375 lines)
│   │   ├── enhanced_evaluator.py  (700 lines)
│   │   ├── fundamental/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── calculators/
│   │   │   │   ├── __init__.py  (0 lines)
│   │   │   │   ├── growth_calculator.py  (226 lines)
│   │   │   │   └── pe_calculator.py  (144 lines)
│   │   │   └── metrics.py  (135 lines)
│   │   ├── orchestration/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── analysis_orchestrator.py  (289 lines)
│   │   │   └── enhancement_strategy.py  (178 lines)
│   │   ├── probability/
│   │   │   ├── __init__.py  (3 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── monte_carlo_simulator.py  (372 lines)
│   │   │   ├── probability_engine.py  (607 lines)
│   │   │   └── probability_response_service.py  (215 lines)
│   │   ├── risk/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── assessment.py  (131 lines)
│   │   │   └── calculators/
│   │   │       ├── __init__.py  (0 lines)
│   │   │       ├── __pycache__/
│   │   │       ├── beta_calculator.py  (171 lines)
│   │   │       └── volatility_calculator.py  (143 lines)
│   │   ├── technical/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   └── __pycache__/
│   │   ├── templates/
│   │   │   ├── __init__.py  (1 lines)
│   │   │   └── analysis_response_template.py  (198 lines)
│   │   └── utils/
│   │       ├── __init__.py  (0 lines)
│   │       └── data_validators.py  (190 lines)
│   ├── api/
│   │   ├── __init__.py  (0 lines)
│   │   ├── __pycache__/
│   │   ├── analytics/
│   │   │   └── __init__.py  (0 lines)
│   │   ├── config.py  (48 lines)
│   │   ├── data/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── cache.py  (366 lines)
│   │   │   ├── cache_warming_scheduler.py  (235 lines)
│   │   │   ├── constants.py  (18 lines)
│   │   │   ├── market_data_service.py  (192 lines)
│   │   │   ├── metrics.py  (553 lines)
│   │   │   ├── providers/
│   │   │   │   ├── __init__.py  (0 lines)
│   │   │   │   ├── __pycache__/
│   │   │   │   ├── alpha_vantage.py  (145 lines)
│   │   │   │   ├── base.py  (238 lines)
│   │   │   │   ├── data_source_manager.py  (217 lines)
│   │   │   │   ├── finnhub.py  (217 lines)
│   │   │   │   ├── modules/
│   │   │   │   │   ├── __init__.py  (49 lines)
│   │   │   │   │   ├── __pycache__/
│   │   │   │   │   ├── auditing.py  (147 lines)
│   │   │   │   │   ├── config.py  (141 lines)
│   │   │   │   │   ├── rate_limiting.py  (103 lines)
│   │   │   │   │   └── validation.py  (244 lines)
│   │   │   │   └── polygon.py  (319 lines)
│   │   │   └── scheduled_tasks.py  (215 lines)
│   │   ├── main.py  (139 lines)
│   │   ├── middleware/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── security.py  (182 lines)
│   │   │   └── security_utils.py  (52 lines)
│   │   ├── routers/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   └── market_data.py  (169 lines)
│   │   ├── routes/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── analytics.py  (299 lines)
│   │   │   ├── bot_health.py  (236 lines)
│   │   │   ├── dashboard.py  (355 lines)
│   │   │   ├── debug.py  (58 lines)
│   │   │   ├── feedback.py  (57 lines)
│   │   │   ├── health.py  (131 lines)
│   │   │   ├── market_data.py  (259 lines)
│   │   │   └── metrics.py  (35 lines)
│   │   ├── schemas/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── feedback_schema.py  (72 lines)
│   │   │   └── metrics_schema.py  (46 lines)
│   │   └── webhooks/
│   │       └── __init__.py  (0 lines)
│   ├── bot/
│   │   ├── __init__.py  (0 lines)
│   │   ├── __main__.py  (75 lines)
│   │   ├── __pycache__/
│   │   ├── audit/
│   │   │   ├── __init__.py  (10 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── rate_limiter.py  (204 lines)
│   │   │   ├── request_visualizer.py  (70 lines)
│   │   │   ├── request_visualizer.py.fixed  (70 lines)
│   │   │   ├── request_visualizer_patch.py  (52 lines)
│   │   │   └── session_manager.py  (68 lines)
│   │   ├── client.py  (1113 lines)
│   │   ├── client_audit_integration.py  (160 lines)
│   │   ├── client_with_monitoring.py  (111 lines)
│   │   ├── core/
│   │   │   ├── __pycache__/
│   │   │   ├── bot.py  (139 lines)
│   │   │   ├── error_handler.py  (32 lines)
│   │   │   └── services.py  (120 lines)
│   │   ├── database_manager.py  (166 lines)
│   │   ├── enhancements/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── discord_ux.py  (357 lines)
│   │   │   └── pipeline_visualizer.py  (437 lines)
│   │   ├── events/
│   │   │   └── __init__.py  (0 lines)
│   │   ├── extensions/
│   │   │   ├── __init__.py  (3 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── alerts.py  (403 lines)
│   │   │   ├── analyze.py  (467 lines)
│   │   │   ├── ask.py  (421 lines)
│   │   │   ├── batch_analyze.py  (462 lines)
│   │   │   ├── error_handler.py  (49 lines)
│   │   │   ├── help.py  (570 lines)
│   │   │   ├── performance_monitor.py  (286 lines)
│   │   │   ├── portfolio.py  (564 lines)
│   │   │   ├── recommendations.py  (651 lines)
│   │   │   ├── status.py  (70 lines)
│   │   │   ├── utility.py  (29 lines)
│   │   │   ├── watchlist.py  (582 lines)
│   │   │   └── zones.py  (671 lines)
│   │   ├── main.py  (106 lines)
│   │   ├── metrics_collector.py  (221 lines)
│   │   ├── monitoring/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   └── health_monitor.py  (346 lines)
│   │   ├── permissions.py  (287 lines)
│   │   ├── pipeline/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── ask/
│   │   │   │   ├── __init__.py  (0 lines)
│   │   │   │   └── stages/
│   │   │   │       ├── __init__.py  (0 lines)
│   │   │   │       └── conversation_memory_service.py  (420 lines)
│   │   │   ├── commands/
│   │   │   │   ├── __init__.py  (0 lines)
│   │   │   │   ├── __pycache__/
│   │   │   │   ├── analyze/
│   │   │   │   │   ├── __init__.py  (0 lines)
│   │   │   │   │   ├── __pycache__/
│   │   │   │   │   ├── parallel_pipeline.py  (568 lines)
│   │   │   │   │   ├── pipeline.py  (414 lines)
│   │   │   │   │   └── stages/
│   │   │   │   │       ├── __init__.py  (13 lines)
│   │   │   │   │       ├── __pycache__/
│   │   │   │   │       ├── enhanced_analysis.py  (91 lines)
│   │   │   │   │       ├── fetch_data.py  (140 lines)
│   │   │   │   │       ├── price_targets.py  (54 lines)
│   │   │   │   │       ├── report_generator.py  (116 lines)
│   │   │   │   │       ├── report_template.py  (215 lines)
│   │   │   │   │       └── technical_analysis.py  (61 lines)
│   │   │   │   ├── ask/
│   │   │   │   │   ├── __init__.py  (7 lines)
│   │   │   │   │   ├── __pycache__/
│   │   │   │   │   ├── batch_processor.py  (320 lines)
│   │   │   │   │   ├── config.py  (569 lines)
│   │   │   │   │   ├── error_handler.py  (31 lines)
│   │   │   │   │   ├── events.py  (34 lines)
│   │   │   │   │   ├── executor.py  (109 lines)
│   │   │   │   │   ├── executor_with_grading.py  (162 lines)
│   │   │   │   │   ├── modules/
│   │   │   │   │   │   ├── __init__.py  (3 lines)
│   │   │   │   │   │   ├── config/
│   │   │   │   │   │   │   └── __init__.py  (3 lines)
│   │   │   │   │   │   ├── models/
│   │   │   │   │   │   │   ├── __init__.py  (3 lines)
│   │   │   │   │   │   │   └── data_models.py  (212 lines)
│   │   │   │   │   │   ├── services/
│   │   │   │   │   │   │   └── __init__.py  (3 lines)
│   │   │   │   │   │   ├── tests/
│   │   │   │   │   │   │   └── __init__.py  (3 lines)
│   │   │   │   │   │   └── utils/
│   │   │   │   │   │       ├── __init__.py  (115 lines)
│   │   │   │   │   │       ├── retry.py  (252 lines)
│   │   │   │   │   │       ├── validation.py  (255 lines)
│   │   │   │   │   │       └── validators.py  (198 lines)
│   │   │   │   │   ├── pipeline.py  (318 lines)
│   │   │   │   │   ├── stages/
│   │   │   │   │   │   ├── __init__.py  (24 lines)
│   │   │   │   │   │   ├── __pycache__/
│   │   │   │   │   │   ├── advanced_classifier.py  (394 lines)
│   │   │   │   │   │   ├── ai_cache.py  (389 lines)
│   │   │   │   │   │   ├── ai_controlled_analysis.py  (488 lines)
│   │   │   │   │   │   ├── ai_models_config.yaml  (41 lines)
│   │   │   │   │   │   ├── ai_models_config_loader.py  (206 lines)
│   │   │   │   │   │   ├── ai_routing_service.py  (467 lines)
│   │   │   │   │   │   ├── ai_symbol_extractor.py  (263 lines)
│   │   │   │   │   │   ├── ask_sections.py  (917 lines)
│   │   │   │   │   │   ├── botlogs.txt  (186 lines)
│   │   │   │   │   │   ├── config.py  (194 lines)
│   │   │   │   │   │   ├── conversation_memory_service.py  (446 lines)
│   │   │   │   │   │   ├── core/
│   │   │   │   │   │   │   ├── __init__.py  (16 lines)
│   │   │   │   │   │   │   ├── __pycache__/
│   │   │   │   │   │   │   ├── ai_client.py  (32 lines)
│   │   │   │   │   │   │   ├── base.py  (113 lines)
│   │   │   │   │   │   │   ├── enhanced_ai_client.py  (287 lines)
│   │   │   │   │   │   │   ├── error_handler.py  (233 lines)
│   │   │   │   │   │   │   ├── market_context_processor.py  (365 lines)
│   │   │   │   │   │   │   ├── response_parser.py  (21 lines)
│   │   │   │   │   │   │   └── technical_analysis_processor.py  (483 lines)
│   │   │   │   │   │   ├── cross_platform_context.py  (386 lines)
│   │   │   │   │   │   ├── depth_style_analyzer.py  (479 lines)
│   │   │   │   │   │   ├── discord_formatter.py  (318 lines)
│   │   │   │   │   │   ├── enhanced_ai_analysis.py  (672 lines)
│   │   │   │   │   │   ├── enhanced_analyzer.py  (199 lines)
│   │   │   │   │   │   ├── enhanced_context.py  (463 lines)
│   │   │   │   │   │   ├── language_detector.py  (240 lines)
│   │   │   │   │   │   ├── market_context_service.py  (390 lines)
│   │   │   │   │   │   ├── ml_sentiment_analyzer.py  (312 lines)
│   │   │   │   │   │   ├── models.py  (69 lines)
│   │   │   │   │   │   ├── pipeline_sections.py  (301 lines)
│   │   │   │   │   │   ├── postprocessor/
│   │   │   │   │   │   │   ├── __init__.py  (14 lines)
│   │   │   │   │   │   │   ├── memory_updater.py  (21 lines)
│   │   │   │   │   │   │   ├── metrics_collector.py  (21 lines)
│   │   │   │   │   │   │   ├── response_formatter.py  (16 lines)
│   │   │   │   │   │   │   └── response_generator.py  (293 lines)
│   │   │   │   │   │   ├── preprocessor/
│   │   │   │   │   │   │   ├── __init__.py  (15 lines)
│   │   │   │   │   │   │   ├── context_builder.py  (21 lines)
│   │   │   │   │   │   │   ├── context_processor.py  (208 lines)
│   │   │   │   │   │   │   ├── input_processor.py  (122 lines)
│   │   │   │   │   │   │   ├── input_validator.py  (19 lines)
│   │   │   │   │   │   │   └── prompt_formatter.py  (16 lines)
│   │   │   │   │   │   ├── prompts.py  (385 lines)
│   │   │   │   │   │   ├── query_analyzer.py  (481 lines)
│   │   │   │   │   │   ├── quick_commands.py  (340 lines)
│   │   │   │   │   │   ├── response_audit.py  (219 lines)
│   │   │   │   │   │   ├── response_templates.py  (110 lines)
│   │   │   │   │   │   ├── response_validator.py  (402 lines)
│   │   │   │   │   │   ├── structured_response_generator.py  (282 lines)
│   │   │   │   │   │   ├── test_ai_models_config.py  (165 lines)
│   │   │   │   │   │   ├── test_infrastructure.py  (153 lines)
│   │   │   │   │   │   ├── utils/
│   │   │   │   │   │   │   ├── __init__.py  (15 lines)
│   │   │   │   │   │   │   ├── cache_integration.py  (98 lines)
│   │   │   │   │   │   │   ├── fallback_handler.py  (17 lines)
│   │   │   │   │   │   │   └── rate_limiter.py  (20 lines)
│   │   │   │   │   │   ├── voice_processor.py  (155 lines)
│   │   │   │   │   │   └── zero_hallucination_generator.py  (259 lines)
│   │   │   │   │   ├── test_modular_system.py  (44 lines)
│   │   │   │   │   └── utility.py  (33 lines)
│   │   │   │   └── watchlist/
│   │   │   │       ├── __init__.py  (0 lines)
│   │   │   │       └── stages/
│   │   │   │           └── __init__.py  (0 lines)
│   │   │   ├── core/
│   │   │   │   ├── __init__.py  (0 lines)
│   │   │   │   ├── __pycache__/
│   │   │   │   ├── circuit_breaker.py  (268 lines)
│   │   │   │   ├── context_manager.py  (266 lines)
│   │   │   │   ├── parallel_pipeline.py  (198 lines)
│   │   │   │   ├── pipeline_engine.py  (375 lines)
│   │   │   │   └── pipeline_optimizer.py  (256 lines)
│   │   │   ├── data/
│   │   │   │   └── __init__.py  (0 lines)
│   │   │   ├── logs/
│   │   │   │   └── __init__.py  (0 lines)
│   │   │   ├── monitoring/
│   │   │   │   └── __init__.py  (0 lines)
│   │   │   ├── performance_optimizer.py  (244 lines)
│   │   │   ├── shared/
│   │   │   │   ├── __init__.py  (0 lines)
│   │   │   │   ├── data_collectors/
│   │   │   │   │   └── __init__.py  (0 lines)
│   │   │   │   ├── formatters/
│   │   │   │   │   └── __init__.py  (0 lines)
│   │   │   │   └── validators/
│   │   │   │       └── __init__.py  (0 lines)
│   │   │   ├── test_pipeline.py  (96 lines)
│   │   │   └── utils/
│   │   │       ├── __init__.py  (0 lines)
│   │   │       ├── __pycache__/
│   │   │       ├── circuit_breaker.py  (165 lines)
│   │   │       └── metrics.py  (216 lines)
│   │   ├── pipeline_framework.py  (175 lines)
│   │   ├── rate_limiter.py  (295 lines)
│   │   ├── security/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── __pycache__/
│   │   │   └── advanced_security.py  (360 lines)
│   │   ├── services/
│   │   ├── setup_audit.py  (241 lines)
│   │   ├── token_validator.py  (139 lines)
│   │   ├── update_imports.py  (39 lines)
│   │   ├── utils/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── component_checker.py  (50 lines)
│   │   │   ├── disclaimer_manager.py  (149 lines)
│   │   │   ├── enhanced_input_validator.py  (383 lines)
│   │   │   ├── error_handler.py  (181 lines)
│   │   │   ├── input_sanitizer.py  (240 lines)
│   │   │   └── rate_limiter.py  (107 lines)
│   │   ├── watchlist_alerts.py  (452 lines)
│   │   └── watchlist_realtime.py  (361 lines)
│   ├── core/
│   │   ├── __init__.py  (38 lines)
│   │   ├── __pycache__/
│   │   ├── automation/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── analysis_scheduler.py  (403 lines)
│   │   │   ├── discord_handler.py  (248 lines)
│   │   │   ├── report_engine.py  (958 lines)
│   │   │   ├── report_formatter.py  (673 lines)
│   │   │   └── report_scheduler.py  (511 lines)
│   │   ├── config_manager.py  (491 lines)
│   │   ├── data_quality_validator.py  (181 lines)
│   │   ├── enums/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   └── stock_analysis.py  (59 lines)
│   │   ├── exceptions.py  (80 lines)
│   │   ├── feedback_mechanism.py  (441 lines)
│   │   ├── formatting/
│   │   │   ├── __init__.py  (9 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── analysis_template.py  (239 lines)
│   │   │   ├── response_templates.py  (198 lines)
│   │   │   ├── technical_analysis.py  (261 lines)
│   │   │   └── text_formatting.py  (100 lines)
│   │   ├── logger.py  (22 lines)
│   │   ├── market_calendar.py  (440 lines)
│   │   ├── monitoring_pkg/
│   │   │   ├── __init__.py  (4 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── bot_monitor.py  (289 lines)
│   │   │   └── performance_tracker.py  (131 lines)
│   │   ├── pipeline_engine.py  (188 lines)
│   │   ├── prompts/
│   │   │   ├── __init__.py  (26 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── models.py  (87 lines)
│   │   │   ├── prompt_manager.py  (386 lines)
│   │   │   └── templates/
│   │   │       ├── __init__.py  (3 lines)
│   │   │       └── system_prompt.txt  (56 lines)
│   │   ├── response_generator.py  (199 lines)
│   │   ├── risk_management/
│   │   │   ├── __init__.py  (19 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── atr_calculator.py  (508 lines)
│   │   │   └── compliance_framework.py  (370 lines)
│   │   ├── scheduler.py  (61 lines)
│   │   ├── secure_cache.py  (140 lines)
│   │   ├── trade_scanner.py  (154 lines)
│   │   ├── utils.py  (205 lines)
│   │   ├── validation/
│   │   │   ├── __init__.py  (3 lines)
│   │   │   ├── __pycache__/
│   │   │   └── financial_validator.py  (391 lines)
│   │   └── watchlist/
│   │       └── __init__.py  (6 lines)
│   ├── data/
│   │   ├── __init__.py  (32 lines)
│   │   ├── __pycache__/
│   │   ├── cache/
│   │   │   ├── __init__.py  (12 lines)
│   │   │   ├── __pycache__/
│   │   │   └── manager.py  (244 lines)
│   │   └── models/
│   │       ├── __init__.py  (16 lines)
│   │       ├── indicators.py  (56 lines)
│   │       └── stock_data.py  (95 lines)
│   ├── database/
│   │   ├── __init__.py  (0 lines)
│   │   ├── __pycache__/
│   │   ├── config.py  (84 lines)
│   │   ├── migrations/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   └── env.py  (80 lines)
│   │   ├── models/
│   │   │   ├── __init__.py  (20 lines)
│   │   │   ├── alerts.py  (69 lines)
│   │   │   ├── analysis.py  (67 lines)
│   │   │   ├── interactions.py  (57 lines)
│   │   │   └── market_data.py  (50 lines)
│   │   ├── query_optimizer.py  (349 lines)
│   │   ├── query_wrapper.py  (210 lines)
│   │   ├── repositories/
│   │   │   └── __init__.py  (0 lines)
│   │   ├── unified_client.py  (209 lines)
│   │   └── unified_db.py  (197 lines)
│   ├── logs/
│   │   └── __init__.py  (0 lines)
│   ├── main.py  (216 lines)
│   ├── security/
│   │   ├── __init__.py  (32 lines)
│   │   └── middleware.py  (196 lines)
│   ├── services/
│   │   ├── __init__.py  (0 lines)
│   │   └── analytics_service.py  (81 lines)
│   ├── shared/
│   │   ├── __init__.py  (3 lines)
│   │   ├── __pycache__/
│   │   ├── ai/
│   │   │   ├── __init__.py  (3 lines)
│   │   │   ├── depth_controller.py  (191 lines)
│   │   │   ├── model_fine_tuner.py  (376 lines)
│   │   │   └── recommendation_engine.py  (483 lines)
│   │   ├── ai_chat/
│   │   │   ├── __init__.py  (31 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── ai_client.py  (287 lines)
│   │   │   ├── config.py  (52 lines)
│   │   │   ├── data_fetcher.py  (122 lines)
│   │   │   ├── fallbacks.py  (35 lines)
│   │   │   ├── models.py  (29 lines)
│   │   │   ├── processor.py  (171 lines)
│   │   │   └── response_formatter.py  (330 lines)
│   │   ├── ai_debugger/
│   │   │   ├── __init__.py  (8 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── live_ai_debugger.py  (394 lines)
│   │   │   └── local_pattern_debugger.py  (299 lines)
│   │   ├── ai_services/
│   │   │   ├── __init__.py  (3 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── ai_chat_processor.py  (116 lines)
│   │   │   ├── ai_processor_clean.py  (422 lines)
│   │   │   ├── ai_processor_robust.py  (1237 lines)
│   │   │   ├── ai_service_wrapper.py  (243 lines)
│   │   │   ├── circuit_breaker.py  (131 lines)
│   │   │   ├── fallback_handler.py  (267 lines)
│   │   │   ├── fast_price_lookup.py  (112 lines)
│   │   │   ├── intelligent_chatbot.py  (539 lines)
│   │   │   ├── openrouter_key.py  (7 lines)
│   │   │   ├── performance_optimizer.py  (103 lines)
│   │   │   ├── query_cache.py  (140 lines)
│   │   │   ├── query_router.py  (327 lines)
│   │   │   ├── response_synthesizer.py  (265 lines)
│   │   │   ├── simple_query_analyzer.py  (195 lines)
│   │   │   ├── smart_model_router.py  (335 lines)
│   │   │   ├── timeout_manager.py  (82 lines)
│   │   │   └── tool_registry.py  (326 lines)
│   │   ├── analytics/
│   │   │   ├── __init__.py  (3 lines)
│   │   │   └── performance_tracker.py  (402 lines)
│   │   ├── background/
│   │   │   ├── __init__.py  (4 lines)
│   │   │   ├── celery_app.py  (64 lines)
│   │   │   └── tasks/
│   │   │       ├── __init__.py  (3 lines)
│   │   │       ├── indicators.py  (68 lines)
│   │   │       └── market_intelligence.py  (356 lines)
│   │   ├── cache/
│   │   │   ├── __pycache__/
│   │   │   └── cache_service.py  (178 lines)
│   │   ├── config/
│   │   │   ├── __pycache__/
│   │   │   └── config_manager.py  (167 lines)
│   │   ├── configuration/
│   │   │   ├── __init__.py  (32 lines)
│   │   │   ├── __pycache__/
│   │   │   └── validators.py  (233 lines)
│   │   ├── data_providers/
│   │   │   ├── __init__.py  (73 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── aggregator.py  (242 lines)
│   │   │   ├── alpaca_provider.py  (301 lines)
│   │   │   ├── alpha_vantage.py  (323 lines)
│   │   │   ├── base.py  (58 lines)
│   │   │   ├── fallback_provider.py  (91 lines)
│   │   │   ├── finnhub_provider.py  (175 lines)
│   │   │   ├── polygon_provider.py  (196 lines)
│   │   │   ├── unified_base.py  (234 lines)
│   │   │   └── yfinance_provider.py  (217 lines)
│   │   ├── data_validation.py  (623 lines)
│   │   ├── database/
│   │   │   ├── __init__.py  (38 lines)
│   │   │   ├── __pycache__/
│   │   │   └── usage_example.py  (58 lines)
│   │   ├── error_handling/
│   │   │   ├── __init__.py  (13 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── fallback.py  (260 lines)
│   │   │   ├── logging.py  (177 lines)
│   │   │   └── retry.py  (369 lines)
│   │   ├── market_analysis/
│   │   │   ├── __init__.py  (19 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── confidence_scorer.py  (225 lines)
│   │   │   ├── signal_analyzer.py  (72 lines)
│   │   │   ├── signals.py  (66 lines)
│   │   │   ├── unified_signal_analyzer.py  (325 lines)
│   │   │   └── utils.py  (91 lines)
│   │   ├── metrics/
│   │   │   ├── __pycache__/
│   │   │   ├── metrics_service.py  (187 lines)
│   │   │   ├── naming_conventions.py  (318 lines)
│   │   │   └── unified_metrics_service.py  (417 lines)
│   │   ├── monitoring/
│   │   │   ├── __init__.py  (22 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── intelligent_grader.py  (180 lines)
│   │   │   ├── performance_monitor.py  (137 lines)
│   │   │   ├── pipeline_grader.py  (498 lines)
│   │   │   ├── pipeline_monitor.py  (13 lines)
│   │   │   └── step_logger.py  (138 lines)
│   │   ├── redis/
│   │   │   ├── __init__.py  (5 lines)
│   │   │   ├── __pycache__/
│   │   │   └── redis_manager.py  (206 lines)
│   │   ├── sentiment/
│   │   │   ├── __init__.py  (3 lines)
│   │   │   └── sentiment_analyzer.py  (520 lines)
│   │   ├── services/
│   │   │   ├── __pycache__/
│   │   │   ├── enhanced_performance_optimizer.py  (452 lines)
│   │   │   ├── optimization_service.py  (258 lines)
│   │   │   └── performance_monitor.py  (225 lines)
│   │   ├── technical_analysis/
│   │   │   ├── __init__.py  (68 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── calculator.py  (214 lines)
│   │   │   ├── config.py  (142 lines)
│   │   │   ├── enhanced_indicators.py  (515 lines)
│   │   │   ├── indicators.py  (293 lines)
│   │   │   ├── multi_timeframe_analyzer.py  (613 lines)
│   │   │   ├── options_greeks_calculator.py  (221 lines)
│   │   │   ├── signal_generator.py  (109 lines)
│   │   │   ├── strategy_calculator.py  (317 lines)
│   │   │   ├── test_indicators.py  (59 lines)
│   │   │   ├── unified_calculator.py  (194 lines)
│   │   │   ├── volume_analyzer.py  (421 lines)
│   │   │   └── zones.py  (1096 lines)
│   │   ├── utils/
│   │   │   ├── __init__.py  (5 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── deprecation.py  (16 lines)
│   │   │   ├── discord_helpers.py  (239 lines)
│   │   │   └── symbol_extraction.py  (344 lines)
│   │   └── watchlist/
│   │       ├── __init__.py  (3 lines)
│   │       ├── __pycache__/
│   │       ├── base_manager.py  (296 lines)
│   │       ├── bot_manager.py  (37 lines)
│   │       ├── models.py  (26 lines)
│   │       ├── supabase_manager.py  (292 lines)
│   │       └── webhook_manager.py  (30 lines)
│   ├── templates/
│   │   ├── __init__.py  (0 lines)
│   │   └── analysis_response.py  (196 lines)
│   └── utils/
│       └── .gitkeep  (1 lines)
├── start_ai_automation.py  (215 lines)
├── start_bot.py  (52 lines)
├── start_dashboard.py  (82 lines)
├── start_enhanced.sh  (85 lines)
├── start_enhanced_bot.py  (151 lines)
├── system_api.py  (387 lines)
├── system_dashboard.html  (889 lines)
├── temp_logger_fix.py  (16 lines)
├── test_ai_answer_quality_final.py  (419 lines)
├── test_ai_controlled_analysis.py  (245 lines)
├── test_ai_data_constraint.py  (113 lines)
├── test_ai_improvements.py  (73 lines)
├── test_ai_vs_regex.py  (73 lines)
├── test_answer_quality.py  (294 lines)
├── test_architectural_improvements.py  (227 lines)
├── test_ask_command_hallucination_fix.py  (77 lines)
├── test_ast_parsing.py  (135 lines)
├── test_consolidated_architecture.py  (129 lines)
├── test_core_pipeline.py  (104 lines)
├── test_dashboard.py  (130 lines)
├── test_data_binding_fix.py  (129 lines)
├── test_data_issue.py  (51 lines)
├── test_discord_ask.py  (82 lines)
├── test_docker_processor.py  (39 lines)
├── test_docker_setup.sh  (49 lines)
├── test_enhanced_hybrid_approach.py  (314 lines)
├── test_enhanced_hybrid_consolidated.py  (383 lines)
├── test_enhanced_validation.py  (278 lines)
├── test_fixed_validation.py  (179 lines)
├── test_hallucinated_response.py  (105 lines)
├── test_hybrid_ai.py  (37 lines)
├── test_hybrid_approach.py  (246 lines)
├── test_live_commands.py  (52 lines)
├── test_monte_carlo_demo.py  (87 lines)
├── test_professional_standards.py  (267 lines)
├── test_real_ai_responses.py  (302 lines)
├── test_real_ai_system.py  (94 lines)
├── test_real_system.py  (133 lines)
├── test_results/
│   ├── detailed_logs/
│   │   ├── ai_enhanced_test_runner.py  (326 lines)
│   │   ├── comprehensive_test_logger.py  (387 lines)
│   │   ├── comprehensive_test_runner.py  (405 lines)
│   │   ├── data_flow_data_OP_000003.json  (19 lines)
│   │   ├── data_flow_data_OP_000037.json  (16 lines)
│   │   ├── data_flow_data_OP_000038.json  (16 lines)
│   │   ├── data_flow_data_OP_000044.json  (16 lines)
│   │   ├── data_flow_data_OP_000050.json  (16 lines)
│   │   ├── data_flow_data_OP_000051.json  (16 lines)
│   │   ├── data_flow_data_OP_000057.json  (220 lines)
│   │   ├── final_test_report_test_session_1757706260.json  (205 lines)
│   │   ├── final_test_report_test_session_1757706366.json  (205 lines)
│   │   ├── pytest_comprehensive_logging.py  (289 lines)
│   │   ├── test_summary_test_session_1757706260.json  (252 lines)
│   │   └── test_summary_test_session_1757706366.json  (252 lines)
│   └── individual_tests/
│       └── test_analysis_results.txt  (227 lines)
├── test_robust_processor.py  (52 lines)
├── test_security_middleware.py  (162 lines)
├── test_simple_ai.py  (92 lines)
├── test_supabase_docker.sh  (74 lines)
├── test_zero_hallucination.py  (228 lines)
├── tests/
│   ├── __pycache__/
│   ├── archive/
│   ├── broken/
│   ├── comprehensive_market_analysis_tester.py  (662 lines)
│   ├── conftest.py  (141 lines)
│   ├── data/
│   │   ├── context/
│   │   └── models/
│   │       └── sentiment/
│   ├── e2e/
│   │   ├── __pycache__/
│   │   └── test_bot_commands.py  (504 lines)
│   ├── failing_tests.txt  (38 lines)
│   ├── generate_report.py  (70 lines)
│   ├── generate_test_grades.py  (101 lines)
│   ├── integration/
│   │   ├── __pycache__/
│   │   ├── test_alpha_vantage_provider.py  (163 lines)
│   │   ├── test_cross_platform_context.py  (204 lines)
│   │   ├── test_enhanced_ai_context.py  (201 lines)
│   │   ├── test_market_data_service.py  (81 lines)
│   │   ├── test_polygon_provider.py  (85 lines)
│   │   ├── test_qqq_options_estimation.py  (315 lines)
│   │   ├── test_supabase_integration.py  (52 lines)
│   │   └── test_supertrend_analysis.py  (89 lines)
│   ├── interactive_market_analysis_tester.py  (287 lines)
│   ├── live_command_tester.py  (478 lines)
│   ├── live_market_analysis_tester.py  (480 lines)
│   ├── load/
│   │   ├── __pycache__/
│   │   └── test_bot_load.py  (365 lines)
│   ├── logs/
│   │   └── pipeline_grades/
│   │       ├── complex_pipeline_complex_pipeline_1757882598_131280474249680_20250914_164318.json  (227 lines)
│   │       ├── high_quality_pipeline_high_quality_pipeline_1757882555_132533445883232_20250914_164235.json  (83 lines)
│   │       ├── high_quality_pipeline_high_quality_pipeline_1757882586_131280475074880_20250914_164306.json  (83 lines)
│   │       ├── low_quality_pipeline_low_quality_pipeline_1757882563_132533445883232_20250914_164243.json  (83 lines)
│   │       ├── low_quality_pipeline_low_quality_pipeline_1757882595_131280474249680_20250914_164315.json  (83 lines)
│   │       ├── medium_quality_pipeline_medium_quality_pipeline_1757882558_132533445883232_20250914_164238.json  (83 lines)
│   │       └── medium_quality_pipeline_medium_quality_pipeline_1757882591_131280474249680_20250914_164311.json  (83 lines)
│   ├── market_analysis_capability_test.py  (400 lines)
│   ├── refactored/
│   │   └── test_correlation_integration.py  (155 lines)
│   ├── run_tests.py  (146 lines)
│   ├── standalone_command_tester.py  (397 lines)
│   ├── test_advanced_security.py  (102 lines)
│   ├── test_ai_alert_parser.py  (198 lines)
│   ├── test_ai_automation.py  (195 lines)
│   ├── test_ai_chat_processor.py  (229 lines)
│   ├── test_ai_debugger_demo.py  (122 lines)
│   ├── test_ai_pipeline.py  (36 lines)
│   ├── test_ai_response.py  (76 lines)
│   ├── test_ai_routing_service_fix.py  (158 lines)
│   ├── test_alpaca_data.py  (146 lines)
│   ├── test_alpha_vantage_config.py  (64 lines)
│   ├── test_alpha_vantage_integration.py  (219 lines)
│   ├── test_analysis.py  (65 lines)
│   ├── test_analyze_pipeline.py  (59 lines)
│   ├── test_api_endpoints.py  (277 lines)
│   ├── test_ask_command.py  (49 lines)
│   ├── test_async_database.py  (69 lines)
│   ├── test_audit_visualization.py  (374 lines)
│   ├── test_automation.py  (203 lines)
│   ├── test_backward_compatibility.py  (127 lines)
│   ├── test_batch_processing.py  (104 lines)
│   ├── test_bot_pipeline_system.py  (449 lines)
│   ├── test_bot_real_data.py  (76 lines)
│   ├── test_cache_metrics.py  (180 lines)
│   ├── test_cache_warming.py  (148 lines)
│   ├── test_comprehensive.py  (198 lines)
│   ├── test_comprehensive_ai_validation.py  (562 lines)
│   ├── test_comprehensive_pipeline.py  (239 lines)
│   ├── test_comprehensive_symbol_extraction.py  (203 lines)
│   ├── test_config.py  (77 lines)
│   ├── test_config_integration.py  (130 lines)
│   ├── test_consolidated_providers.py  (218 lines)
│   ├── test_contextual_logger.py  (88 lines)
│   ├── test_core_system_components.py  (453 lines)
│   ├── test_correlation_standalone.py  (128 lines)
│   ├── test_correlation_wrappers.py  (179 lines)
│   ├── test_critical_fixes.py  (221 lines)
│   ├── test_critical_pipeline_fixes.py  (211 lines)
│   ├── test_current_credentials.py  (100 lines)
│   ├── test_data_gap_detection.py  (274 lines)
│   ├── test_data_provider.py  (36 lines)
│   ├── test_data_provider_integration_fix.py  (235 lines)
│   ├── test_data_provider_system.py  (426 lines)
│   ├── test_data_providers.py  (146 lines)
│   ├── test_data_quality_scoring.py  (337 lines)
│   ├── test_database_connection.py  (24 lines)
│   ├── test_db_manager.py  (95 lines)
│   ├── test_debug_logging.py  (52 lines)
│   ├── test_different_questions.py  (68 lines)
│   ├── test_discord_integration.py  (144 lines)
│   ├── test_discord_interaction.py  (59 lines)
│   ├── test_discord_optimizations.py  (155 lines)
│   ├── test_enhanced_analysis.py  (265 lines)
│   ├── test_enhanced_analysis_mock.py  (191 lines)
│   ├── test_enhanced_engines_only.py  (139 lines)
│   ├── test_enhanced_indicators.py  (276 lines)
│   ├── test_enhanced_parser.py  (234 lines)
│   ├── test_enhanced_stage_only.py  (113 lines)
│   ├── test_fallback_remediation.py  (163 lines)
│   ├── test_feedback_mechanism.py  (149 lines)
│   ├── test_final_enhancements.py  (262 lines)
│   ├── test_finnhub_provider.py  (80 lines)
│   ├── test_fixed_pipeline.py  (102 lines)
│   ├── test_fixes.py  (103 lines)
│   ├── test_full_analysis.py  (63 lines)
│   ├── test_imports.py  (84 lines)
│   ├── test_integrated_analysis.py  (102 lines)
│   ├── test_live_data.py  (193 lines)
│   ├── test_local_debugger_demo.py  (107 lines)
│   ├── test_market_api.py  (36 lines)
│   ├── test_market_calendar.py  (306 lines)
│   ├── test_market_hours.py  (70 lines)
│   ├── test_market_hours_fix.py  (147 lines)
│   ├── test_message_length_enforcement.py  (141 lines)
│   ├── test_metrics_api.py  (45 lines)
│   ├── test_mock_fix.py  (42 lines)
│   ├── test_multi_symbol_integration.py  (209 lines)
│   ├── test_multi_timeframe.py  (161 lines)
│   ├── test_new_credentials.py  (62 lines)
│   ├── test_optimizations.py  (140 lines)
│   ├── test_options_greeks.py  (104 lines)
│   ├── test_outlier_detection.py  (323 lines)
│   ├── test_performance_optimization.py  (79 lines)
│   ├── test_performance_optimizations.py  (255 lines)
│   ├── test_pipeline_completion_focused.py  (332 lines)
│   ├── test_pipeline_completion_issues.py  (552 lines)
│   ├── test_pipeline_monitoring.py  (169 lines)
│   ├── test_pipeline_optimization.py  (291 lines)
│   ├── test_pipeline_visualization.py  (217 lines)
│   ├── test_probability_engine_components.py  (58 lines)
│   ├── test_probability_response_service.py  (64 lines)
│   ├── test_production_deployment.py  (155 lines)
│   ├── test_prompt_system.py  (160 lines)
│   ├── test_provider_attribution.py  (293 lines)
│   ├── test_provider_status.py  (68 lines)
│   ├── test_quick_enhancements.py  (261 lines)
│   ├── test_real_data_providers.py  (240 lines)
│   ├── test_real_data_quality.py  (201 lines)
│   ├── test_recommendation_engine.py  (250 lines)
│   ├── test_redis_docker.py  (42 lines)
│   ├── test_redis_manager.py  (54 lines)
│   ├── test_refactored_bot.py  (34 lines)
│   ├── test_report_engine_mock.py  (65 lines)
│   ├── test_response_audit.py  (0 lines)
│   ├── test_response_depth_fix.py  (149 lines)
│   ├── test_response_generator.py  (78 lines)
│   ├── test_response_template_fix.py  (191 lines)
│   ├── test_results/
│   │   ├── comprehensive_ai_validation_20250915_230845.json  (1013 lines)
│   │   ├── standalone_test_20250915_223937.json  (108 lines)
│   │   ├── standalone_test_20250915_224510.json  (108 lines)
│   │   ├── standalone_test_20250915_225021.json  (108 lines)
│   │   └── system_integration_20250915_231026.json  (167 lines)
│   ├── test_show_full_report.py  (143 lines)
│   ├── test_simple_audit.py  (506 lines)
│   ├── test_specific_symbol.py  (97 lines)
│   ├── test_stale_data_detection.py  (329 lines)
│   ├── test_supabase_connection.py  (62 lines)
│   ├── test_supply_demand_zones.py  (259 lines)
│   ├── test_suspicious_data_detection.py  (109 lines)
│   ├── test_symbol_extraction_fix.py  (116 lines)
│   ├── test_system_integration.py  (353 lines)
│   ├── test_technical_analysis_integration.py  (79 lines)
│   ├── test_technical_indicators.py  (182 lines)
│   ├── test_template_format_fix_comprehensive.py  (188 lines)
│   ├── test_unified_symbol_extraction.py  (139 lines)
│   ├── test_uniform_alerts.py  (87 lines)
│   ├── test_volume_analyzer.py  (195 lines)
│   ├── test_webhook.py  (43 lines)
│   ├── test_webhook_integration.py  (384 lines)
│   ├── test_webhook_unique.py  (50 lines)
│   ├── test_zone_integration_real_data.py  (309 lines)
│   ├── unit/
│   │   ├── __pycache__/
│   │   ├── test_ml_sentiment_analyzer.py  (164 lines)
│   │   ├── test_options_greeks_calculator.py  (109 lines)
│   │   └── test_strategy_calculator.py  (175 lines)
│   └── working/
├── tradingview-ingest/
│   ├── Dockerfile  (41 lines)
│   ├── algo.pine  (3359 lines)
│   ├── config/
│   │   └── tradingview_config.py  (64 lines)
│   ├── logs/
│   │   └── .gitkeep  (1 lines)
│   ├── migrations/
│   │   ├── 01_create_webhook_tables.sql  (77 lines)
│   │   └── 02_fix_supabase_security_issues.sql  (80 lines)
│   ├── nginx/
│   │   └── webhook.conf  (34 lines)
│   ├── ngrok.yml  (7 lines)
│   ├── requirements.txt  (19 lines)
│   ├── requirements.webhook.txt  (7 lines)
│   ├── src/
│   │   ├── __init__.py  (1 lines)
│   │   ├── __pycache__/
│   │   ├── ai_alert_parser.py  (279 lines)
│   │   ├── alert_engine.py  (285 lines)
│   │   ├── analyzer.py  (266 lines)
│   │   ├── automated_analyzer.py  (452 lines)
│   │   ├── config.py  (81 lines)
│   │   ├── config_dir/
│   │   │   ├── __init__.py  (20 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── config.py  (27 lines)
│   │   │   └── legacy_flags.py  (19 lines)
│   │   ├── data_parser.py  (218 lines)
│   │   ├── database/
│   │   │   ├── __init__.py  (23 lines)
│   │   │   ├── __pycache__/
│   │   │   └── operations.py  (311 lines)
│   │   ├── debug/
│   │   │   ├── __init__.py  (100 lines)
│   │   │   ├── database.py  (179 lines)
│   │   │   ├── performance.py  (135 lines)
│   │   │   ├── visualizer.py  (141 lines)
│   │   │   └── webhook_processor.py  (73 lines)
│   │   ├── discord_notifier.py  (154 lines)
│   │   ├── error_handler.py  (227 lines)
│   │   ├── main.py  (360 lines)
│   │   ├── metrics/
│   │   │   ├── __init__.py  (9 lines)
│   │   │   └── __pycache__/
│   │   ├── models/
│   │   │   ├── __init__.py  (26 lines)
│   │   │   ├── __pycache__/
│   │   │   └── base.py  (110 lines)
│   │   ├── models.py  (77 lines)
│   │   ├── parser.py  (474 lines)
│   │   ├── security.py  (235 lines)
│   │   ├── shared/
│   │   │   ├── __init__.py  (4 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── database/
│   │   │   │   ├── __init__.py  (52 lines)
│   │   │   │   └── __pycache__/
│   │   │   ├── redis/
│   │   │   │   ├── __init__.py  (50 lines)
│   │   │   │   └── __pycache__/
│   │   │   └── watchlist/
│   │   │       ├── __init__.py  (3 lines)
│   │   │       └── ingest_watchlist_manager.py  (30 lines)
│   │   ├── simple_ingestion.py  (178 lines)
│   │   ├── storage_manager.py  (900 lines)
│   │   ├── supabase_client.py  (78 lines)
│   │   ├── text_parser.py  (25 lines)
│   │   ├── visualizer.py  (325 lines)
│   │   ├── watchlist_manager.py  (15 lines)
│   │   ├── webhook_core.py  (197 lines)
│   │   ├── webhook_processor.py  (375 lines)
│   │   └── webhook_receiver.py  (374 lines)
│   ├── start_ngrok.sh  (36 lines)
│   ├── test_crypto_simple.py  (124 lines)
│   ├── test_high_volume.py  (215 lines)
│   ├── test_multi_ticker.py  (204 lines)
│   ├── test_uniform_alerts.py  (87 lines)
│   ├── test_webhook.py  (130 lines)
│   └── tests/
│       ├── load_test_no_legacy.py  (163 lines)
│       ├── test_config.py  (61 lines)
│       ├── test_external_integrations.py  (154 lines)
│       └── test_no_legacy_data_processing.py  (88 lines)
├── unified_docs/
├── visualize_recent_query.py  (125 lines)
├── working_db_solution.py  (100 lines)
├── working_solution.py  (205 lines)
└── working_with_existing_tables.py  (136 lines)
```

### Audit Notes

Use this section to add detailed notes on specific files or directories.

- **`src/`**: 
- **`config.yaml`**: 
- **`...`**: 
