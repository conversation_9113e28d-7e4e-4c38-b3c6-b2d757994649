{"name": "tradingview-automatio", "type": "project", "path": ".", "children": [{"name": "src", "type": "directory", "path": "src", "children": [{"name": "data", "type": "directory", "path": "src/data", "children": [{"name": "models", "type": "directory", "path": "src/data/models", "children": [{"name": "stock_data.py", "type": "file", "path": "src/data/models/stock_data.py", "error": "name 'base' is not defined"}, {"name": "indicators.py", "type": "file", "path": "src/data/models/indicators.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/data/models/__init__.py"}]}, {"name": "__init__.py", "type": "file", "path": "src/data/__init__.py"}, {"name": "cache", "type": "directory", "path": "src/data/cache", "children": [{"name": "manager.py", "type": "file", "path": "src/data/cache/manager.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/data/cache/__init__.py"}]}]}, {"name": "security", "type": "directory", "path": "src/security", "children": [{"name": "middleware.py", "type": "file", "path": "src/security/middleware.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/security/__init__.py"}]}, {"name": "main.py", "type": "file", "path": "src/main.py", "error": "'Import' object has no attribute 'body'"}, {"name": "services", "type": "directory", "path": "src/services", "children": [{"name": "analytics_service.py", "type": "file", "path": "src/services/analytics_service.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/services/__init__.py"}]}, {"name": "bot", "type": "directory", "path": "src/bot", "children": [{"name": "watchlist_alerts.py", "type": "file", "path": "src/bot/watchlist_alerts.py", "error": "name 'base' is not defined"}, {"name": "extensions", "type": "directory", "path": "src/bot/extensions", "children": [{"name": "analyze.py", "type": "file", "path": "src/bot/extensions/analyze.py", "error": "name 'base' is not defined"}, {"name": "watchlist.py", "type": "file", "path": "src/bot/extensions/watchlist.py", "error": "name 'base' is not defined"}, {"name": "portfolio.py", "type": "file", "path": "src/bot/extensions/portfolio.py", "error": "name 'base' is not defined"}, {"name": "utility.py", "type": "file", "path": "src/bot/extensions/utility.py", "error": "name 'base' is not defined"}, {"name": "zones.py", "type": "file", "path": "src/bot/extensions/zones.py", "error": "name 'base' is not defined"}, {"name": "ask.py", "type": "file", "path": "src/bot/extensions/ask.py", "error": "name 'base' is not defined"}, {"name": "alerts.py", "type": "file", "path": "src/bot/extensions/alerts.py", "error": "name 'base' is not defined"}, {"name": "batch_analyze.py", "type": "file", "path": "src/bot/extensions/batch_analyze.py", "error": "name 'base' is not defined"}, {"name": "help.py", "type": "file", "path": "src/bot/extensions/help.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/bot/extensions/__init__.py"}, {"name": "status.py", "type": "file", "path": "src/bot/extensions/status.py", "error": "name 'base' is not defined"}, {"name": "performance_monitor.py", "type": "file", "path": "src/bot/extensions/performance_monitor.py", "error": "name 'base' is not defined"}, {"name": "error_handler.py", "type": "file", "path": "src/bot/extensions/error_handler.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "recommendations.py", "type": "file", "path": "src/bot/extensions/recommendations.py", "error": "name 'base' is not defined"}]}, {"name": "__main__.py", "type": "file", "path": "src/bot/__main__.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "security", "type": "directory", "path": "src/bot/security", "children": [{"name": "__init__.py", "type": "file", "path": "src/bot/security/__init__.py"}, {"name": "advanced_security.py", "type": "file", "path": "src/bot/security/advanced_security.py", "error": "name 'base' is not defined"}]}, {"name": "pipeline", "type": "directory", "path": "src/bot/pipeline", "children": [{"name": "data", "type": "directory", "path": "src/bot/pipeline/data", "children": [{"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/data/__init__.py"}]}, {"name": "test_pipeline.py", "type": "file", "path": "src/bot/pipeline/test_pipeline.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "ask", "type": "directory", "path": "src/bot/pipeline/ask", "children": [{"name": "stages", "type": "directory", "path": "src/bot/pipeline/ask/stages", "children": [{"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/ask/stages/__init__.py"}, {"name": "conversation_memory_service.py", "type": "file", "path": "src/bot/pipeline/ask/stages/conversation_memory_service.py", "error": "name 'base' is not defined"}]}, {"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/ask/__init__.py"}]}, {"name": "commands", "type": "directory", "path": "src/bot/pipeline/commands", "children": [{"name": "watchlist", "type": "directory", "path": "src/bot/pipeline/commands/watchlist", "children": [{"name": "stages", "type": "directory", "path": "src/bot/pipeline/commands/watchlist/stages", "children": [{"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/commands/watchlist/stages/__init__.py"}]}, {"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/commands/watchlist/__init__.py"}]}, {"name": "ask", "type": "directory", "path": "src/bot/pipeline/commands/ask", "children": [{"name": "executor.py", "type": "file", "path": "src/bot/pipeline/commands/ask/executor.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "pipeline.py", "type": "file", "path": "src/bot/pipeline/commands/ask/pipeline.py", "error": "name 'base' is not defined"}, {"name": "utility.py", "type": "file", "path": "src/bot/pipeline/commands/ask/utility.py", "error": "name 'base' is not defined"}, {"name": "config.py", "type": "file", "path": "src/bot/pipeline/commands/ask/config.py", "error": "name 'base' is not defined"}, {"name": "stages", "type": "directory", "path": "src/bot/pipeline/commands/ask/stages", "children": [{"name": "voice_processor.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/voice_processor.py", "error": "name 'base' is not defined"}, {"name": "test_infrastructure.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/test_infrastructure.py", "error": "name 'base' is not defined"}, {"name": "structured_response_generator.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/structured_response_generator.py", "error": "name 'base' is not defined"}, {"name": "prompts.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/prompts.py", "commands": [], "pipelines": [], "imports": [], "classes": [], "functions": []}, {"name": "ai_cache.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/ai_cache.py", "error": "name 'base' is not defined"}, {"name": "ai_models_config.yaml", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/ai_models_config.yaml"}, {"name": "pipeline_sections.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/pipeline_sections.py", "error": "name 'base' is not defined"}, {"name": "market_context_service.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/market_context_service.py", "error": "name 'base' is not defined"}, {"name": "query_analyzer.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/query_analyzer.py", "error": "name 'base' is not defined"}, {"name": "advanced_classifier.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/advanced_classifier.py", "error": "name 'base' is not defined"}, {"name": "zero_hallucination_generator.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/zero_hallucination_generator.py", "error": "name 'base' is not defined"}, {"name": "response_audit.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/response_audit.py", "error": "name 'base' is not defined"}, {"name": "depth_style_analyzer.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/depth_style_analyzer.py", "error": "name 'base' is not defined"}, {"name": "config.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/config.py", "error": "name 'base' is not defined"}, {"name": "models.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/models.py", "error": "name 'base' is not defined"}, {"name": "ai_models_config_loader.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/ai_models_config_loader.py", "error": "name 'base' is not defined"}, {"name": "discord_formatter.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/discord_formatter.py", "error": "name 'base' is not defined"}, {"name": "ai_controlled_analysis.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/ai_controlled_analysis.py", "error": "name 'base' is not defined"}, {"name": "ask_sections.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/ask_sections.py", "error": "name 'base' is not defined"}, {"name": "utils", "type": "directory", "path": "src/bot/pipeline/commands/ask/stages/utils", "children": [{"name": "fallback_handler.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/utils/fallback_handler.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/utils/__init__.py"}, {"name": "cache_integration.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/utils/cache_integration.py", "error": "name 'base' is not defined"}, {"name": "rate_limiter.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/utils/rate_limiter.py", "error": "name 'base' is not defined"}]}, {"name": "enhanced_context.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/enhanced_context.py", "error": "name 'base' is not defined"}, {"name": "preprocessor", "type": "directory", "path": "src/bot/pipeline/commands/ask/stages/preprocessor", "children": [{"name": "context_builder.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/preprocessor/context_builder.py", "error": "name 'base' is not defined"}, {"name": "prompt_formatter.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/preprocessor/prompt_formatter.py", "error": "name 'base' is not defined"}, {"name": "input_validator.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/preprocessor/input_validator.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/preprocessor/__init__.py"}, {"name": "input_processor.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/preprocessor/input_processor.py", "error": "name 'base' is not defined"}, {"name": "context_processor.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/preprocessor/context_processor.py", "error": "name 'base' is not defined"}]}, {"name": "enhanced_analyzer.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/enhanced_analyzer.py", "error": "name 'base' is not defined"}, {"name": "test_ai_models_config.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/test_ai_models_config.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "botlogs.txt", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/botlogs.txt"}, {"name": "core", "type": "directory", "path": "src/bot/pipeline/commands/ask/stages/core", "children": [{"name": "response_parser.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/core/response_parser.py", "error": "name 'base' is not defined"}, {"name": "market_context_processor.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/core/market_context_processor.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/core/__init__.py"}, {"name": "enhanced_ai_client.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/core/enhanced_ai_client.py", "error": "name 'base' is not defined"}, {"name": "base.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/core/base.py", "error": "name 'base' is not defined"}, {"name": "ai_client.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/core/ai_client.py", "error": "name 'base' is not defined"}, {"name": "technical_analysis_processor.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/core/technical_analysis_processor.py", "error": "name 'base' is not defined"}, {"name": "error_handler.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/core/error_handler.py", "error": "name 'base' is not defined"}]}, {"name": "language_detector.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/language_detector.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/__init__.py"}, {"name": "ai_symbol_extractor.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/ai_symbol_extractor.py", "error": "name 'base' is not defined"}, {"name": "conversation_memory_service.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/conversation_memory_service.py", "error": "name 'base' is not defined"}, {"name": "postprocessor", "type": "directory", "path": "src/bot/pipeline/commands/ask/stages/postprocessor", "children": [{"name": "metrics_collector.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/postprocessor/metrics_collector.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/postprocessor/__init__.py"}, {"name": "response_formatter.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/postprocessor/response_formatter.py", "error": "name 'base' is not defined"}, {"name": "response_generator.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/postprocessor/response_generator.py", "error": "name 'base' is not defined"}, {"name": "memory_updater.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/postprocessor/memory_updater.py", "error": "name 'base' is not defined"}]}, {"name": "ml_sentiment_analyzer.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/ml_sentiment_analyzer.py", "error": "name 'base' is not defined"}, {"name": "enhanced_ai_analysis.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/enhanced_ai_analysis.py", "error": "name 'base' is not defined"}, {"name": "ai_routing_service.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/ai_routing_service.py", "error": "name 'base' is not defined"}, {"name": "response_validator.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/response_validator.py", "error": "name 'base' is not defined"}, {"name": "quick_commands.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/quick_commands.py", "error": "name 'base' is not defined"}, {"name": "response_templates.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/response_templates.py", "error": "name 'base' is not defined"}, {"name": "cross_platform_context.py", "type": "file", "path": "src/bot/pipeline/commands/ask/stages/cross_platform_context.py", "error": "name 'base' is not defined"}]}, {"name": "modules", "type": "directory", "path": "src/bot/pipeline/commands/ask/modules", "children": [{"name": "config", "type": "directory", "path": "src/bot/pipeline/commands/ask/modules/config", "children": [{"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/commands/ask/modules/config/__init__.py"}]}, {"name": "services", "type": "directory", "path": "src/bot/pipeline/commands/ask/modules/services", "children": [{"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/commands/ask/modules/services/__init__.py"}]}, {"name": "models", "type": "directory", "path": "src/bot/pipeline/commands/ask/modules/models", "children": [{"name": "data_models.py", "type": "file", "path": "src/bot/pipeline/commands/ask/modules/models/data_models.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/commands/ask/modules/models/__init__.py"}]}, {"name": "utils", "type": "directory", "path": "src/bot/pipeline/commands/ask/modules/utils", "children": [{"name": "validation.py", "type": "file", "path": "src/bot/pipeline/commands/ask/modules/utils/validation.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/commands/ask/modules/utils/__init__.py"}, {"name": "retry.py", "type": "file", "path": "src/bot/pipeline/commands/ask/modules/utils/retry.py", "error": "name 'base' is not defined"}, {"name": "validators.py", "type": "file", "path": "src/bot/pipeline/commands/ask/modules/utils/validators.py", "error": "'Expr' object has no attribute 'body'"}]}, {"name": "tests", "type": "directory", "path": "src/bot/pipeline/commands/ask/modules/tests", "children": [{"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/commands/ask/modules/tests/__init__.py"}]}, {"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/commands/ask/modules/__init__.py"}]}, {"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/commands/ask/__init__.py"}, {"name": "test_modular_system.py", "type": "file", "path": "src/bot/pipeline/commands/ask/test_modular_system.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "batch_processor.py", "type": "file", "path": "src/bot/pipeline/commands/ask/batch_processor.py", "error": "name 'base' is not defined"}, {"name": "config.yaml", "type": "file", "path": "src/bot/pipeline/commands/ask/config.yaml"}, {"name": "executor_with_grading.py", "type": "file", "path": "src/bot/pipeline/commands/ask/executor_with_grading.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "events.py", "type": "file", "path": "src/bot/pipeline/commands/ask/events.py", "error": "name 'base' is not defined"}, {"name": "error_handler.py", "type": "file", "path": "src/bot/pipeline/commands/ask/error_handler.py", "error": "name 'base' is not defined"}]}, {"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/commands/__init__.py"}, {"name": "analyze", "type": "directory", "path": "src/bot/pipeline/commands/analyze", "children": [{"name": "pipeline.py", "type": "file", "path": "src/bot/pipeline/commands/analyze/pipeline.py", "error": "name 'base' is not defined"}, {"name": "stages", "type": "directory", "path": "src/bot/pipeline/commands/analyze/stages", "children": [{"name": "report_generator.py", "type": "file", "path": "src/bot/pipeline/commands/analyze/stages/report_generator.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "report_template.py", "type": "file", "path": "src/bot/pipeline/commands/analyze/stages/report_template.py", "error": "name 'base' is not defined"}, {"name": "enhanced_analysis.py", "type": "file", "path": "src/bot/pipeline/commands/analyze/stages/enhanced_analysis.py", "error": "name 'base' is not defined"}, {"name": "fetch_data.py", "type": "file", "path": "src/bot/pipeline/commands/analyze/stages/fetch_data.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "technical_analysis.py", "type": "file", "path": "src/bot/pipeline/commands/analyze/stages/technical_analysis.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/commands/analyze/stages/__init__.py"}, {"name": "price_targets.py", "type": "file", "path": "src/bot/pipeline/commands/analyze/stages/price_targets.py", "error": "'Expr' object has no attribute 'body'"}]}, {"name": "parallel_pipeline.py", "type": "file", "path": "src/bot/pipeline/commands/analyze/parallel_pipeline.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/commands/analyze/__init__.py"}]}]}, {"name": "utils", "type": "directory", "path": "src/bot/pipeline/utils", "children": [{"name": "metrics.py", "type": "file", "path": "src/bot/pipeline/utils/metrics.py", "error": "name 'base' is not defined"}, {"name": "circuit_breaker.py", "type": "file", "path": "src/bot/pipeline/utils/circuit_breaker.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/utils/__init__.py"}]}, {"name": "monitoring", "type": "directory", "path": "src/bot/pipeline/monitoring", "children": [{"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/monitoring/__init__.py"}]}, {"name": "logs", "type": "directory", "path": "src/bot/pipeline/logs", "children": [{"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/logs/__init__.py"}]}, {"name": "core", "type": "directory", "path": "src/bot/pipeline/core", "children": [{"name": "circuit_breaker.py", "type": "file", "path": "src/bot/pipeline/core/circuit_breaker.py", "error": "name 'base' is not defined"}, {"name": "parallel_pipeline.py", "type": "file", "path": "src/bot/pipeline/core/parallel_pipeline.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/core/__init__.py"}, {"name": "pipeline_optimizer.py", "type": "file", "path": "src/bot/pipeline/core/pipeline_optimizer.py", "error": "name 'base' is not defined"}, {"name": "context_manager.py", "type": "file", "path": "src/bot/pipeline/core/context_manager.py", "error": "name 'base' is not defined"}, {"name": "pipeline_engine.py", "type": "file", "path": "src/bot/pipeline/core/pipeline_engine.py", "error": "name 'base' is not defined"}]}, {"name": "shared", "type": "directory", "path": "src/bot/pipeline/shared", "children": [{"name": "data_collectors", "type": "directory", "path": "src/bot/pipeline/shared/data_collectors", "children": [{"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/shared/data_collectors/__init__.py"}]}, {"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/shared/__init__.py"}, {"name": "validators", "type": "directory", "path": "src/bot/pipeline/shared/validators", "children": [{"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/shared/validators/__init__.py"}]}, {"name": "formatters", "type": "directory", "path": "src/bot/pipeline/shared/formatters", "children": [{"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/shared/formatters/__init__.py"}]}]}, {"name": "__init__.py", "type": "file", "path": "src/bot/pipeline/__init__.py"}, {"name": "performance_optimizer.py", "type": "file", "path": "src/bot/pipeline/performance_optimizer.py", "error": "name 'base' is not defined"}]}, {"name": "client.py", "type": "file", "path": "src/bot/client.py", "error": "name 'base' is not defined"}, {"name": "main.py", "type": "file", "path": "src/bot/main.py", "error": "name 'base' is not defined"}, {"name": "services", "type": "directory", "path": "src/bot/services", "children": []}, {"name": "audit", "type": "directory", "path": "src/bot/audit", "children": [{"name": "session_manager.py", "type": "file", "path": "src/bot/audit/session_manager.py", "error": "name 'base' is not defined"}, {"name": "request_visualizer_patch.py", "type": "file", "path": "src/bot/audit/request_visualizer_patch.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "request_visualizer.py", "type": "file", "path": "src/bot/audit/request_visualizer.py", "error": "name 'base' is not defined"}, {"name": "request_visualizer.py.fixed", "type": "file", "path": "src/bot/audit/request_visualizer.py.fixed"}, {"name": "__init__.py", "type": "file", "path": "src/bot/audit/__init__.py"}, {"name": "rate_limiter.py", "type": "file", "path": "src/bot/audit/rate_limiter.py", "error": "name 'base' is not defined"}]}, {"name": "enhancements", "type": "directory", "path": "src/bot/enhancements", "children": [{"name": "pipeline_visualizer.py", "type": "file", "path": "src/bot/enhancements/pipeline_visualizer.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/bot/enhancements/__init__.py"}, {"name": "discord_ux.py", "type": "file", "path": "src/bot/enhancements/discord_ux.py", "error": "name 'base' is not defined"}]}, {"name": "setup_audit.py", "type": "file", "path": "src/bot/setup_audit.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "metrics_collector.py", "type": "file", "path": "src/bot/metrics_collector.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "pipeline_framework.py", "type": "file", "path": "src/bot/pipeline_framework.py", "error": "name 'base' is not defined"}, {"name": "utils", "type": "directory", "path": "src/bot/utils", "children": [{"name": "component_checker.py", "type": "file", "path": "src/bot/utils/component_checker.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/bot/utils/__init__.py"}, {"name": "enhanced_input_validator.py", "type": "file", "path": "src/bot/utils/enhanced_input_validator.py", "error": "name 'base' is not defined"}, {"name": "input_sanitizer.py", "type": "file", "path": "src/bot/utils/input_sanitizer.py", "error": "name 'base' is not defined"}, {"name": "disclaimer_manager.py", "type": "file", "path": "src/bot/utils/disclaimer_manager.py", "error": "name 'base' is not defined"}, {"name": "rate_limiter.py", "type": "file", "path": "src/bot/utils/rate_limiter.py", "error": "name 'base' is not defined"}, {"name": "error_handler.py", "type": "file", "path": "src/bot/utils/error_handler.py", "error": "name 'base' is not defined"}]}, {"name": "monitoring", "type": "directory", "path": "src/bot/monitoring", "children": [{"name": "__init__.py", "type": "file", "path": "src/bot/monitoring/__init__.py"}, {"name": "health_monitor.py", "type": "file", "path": "src/bot/monitoring/health_monitor.py", "error": "name 'base' is not defined"}]}, {"name": "client_with_monitoring.py", "type": "file", "path": "src/bot/client_with_monitoring.py", "error": "name 'base' is not defined"}, {"name": "core", "type": "directory", "path": "src/bot/core", "children": [{"name": "services.py", "type": "file", "path": "src/bot/core/services.py", "error": "name 'base' is not defined"}, {"name": "bot.py", "type": "file", "path": "src/bot/core/bot.py", "error": "name 'base' is not defined"}, {"name": "error_handler.py", "type": "file", "path": "src/bot/core/error_handler.py", "error": "'Expr' object has no attribute 'body'"}]}, {"name": "token_validator.py", "type": "file", "path": "src/bot/token_validator.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/bot/__init__.py"}, {"name": "client_audit_integration.py", "type": "file", "path": "src/bot/client_audit_integration.py", "error": "name 'base' is not defined"}, {"name": "update_imports.py", "type": "file", "path": "src/bot/update_imports.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "permissions.py", "type": "file", "path": "src/bot/permissions.py", "error": "name 'base' is not defined"}, {"name": "events", "type": "directory", "path": "src/bot/events", "children": [{"name": "__init__.py", "type": "file", "path": "src/bot/events/__init__.py"}]}, {"name": "database_manager.py", "type": "file", "path": "src/bot/database_manager.py", "error": "name 'base' is not defined"}, {"name": "rate_limiter.py", "type": "file", "path": "src/bot/rate_limiter.py", "error": "name 'base' is not defined"}, {"name": "watchlist_realtime.py", "type": "file", "path": "src/bot/watchlist_realtime.py", "error": "name 'base' is not defined"}]}, {"name": "api", "type": "directory", "path": "src/api", "children": [{"name": "data", "type": "directory", "path": "src/api/data", "children": [{"name": "providers", "type": "directory", "path": "src/api/data/providers", "children": [{"name": "data_source_manager.py", "type": "file", "path": "src/api/data/providers/data_source_manager.py", "error": "name 'base' is not defined"}, {"name": "modules", "type": "directory", "path": "src/api/data/providers/modules", "children": [{"name": "validation.py", "type": "file", "path": "src/api/data/providers/modules/validation.py", "error": "name 'base' is not defined"}, {"name": "config.py", "type": "file", "path": "src/api/data/providers/modules/config.py", "error": "name 'base' is not defined"}, {"name": "auditing.py", "type": "file", "path": "src/api/data/providers/modules/auditing.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/api/data/providers/modules/__init__.py"}, {"name": "rate_limiting.py", "type": "file", "path": "src/api/data/providers/modules/rate_limiting.py", "error": "name 'base' is not defined"}]}, {"name": "alpha_vantage.py", "type": "file", "path": "src/api/data/providers/alpha_vantage.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/api/data/providers/__init__.py"}, {"name": "base.py", "type": "file", "path": "src/api/data/providers/base.py", "error": "name 'base' is not defined"}, {"name": "finnhub.py", "type": "file", "path": "src/api/data/providers/finnhub.py", "error": "name 'base' is not defined"}, {"name": "polygon.py", "type": "file", "path": "src/api/data/providers/polygon.py", "error": "name 'base' is not defined"}]}, {"name": "metrics.py", "type": "file", "path": "src/api/data/metrics.py", "error": "name 'base' is not defined"}, {"name": "constants.py", "type": "file", "path": "src/api/data/constants.py", "commands": [], "pipelines": [], "imports": [], "classes": [], "functions": []}, {"name": "scheduled_tasks.py", "type": "file", "path": "src/api/data/scheduled_tasks.py", "error": "name 'base' is not defined"}, {"name": "market_data_service.py", "type": "file", "path": "src/api/data/market_data_service.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/api/data/__init__.py"}, {"name": "cache.py", "type": "file", "path": "src/api/data/cache.py", "error": "name 'base' is not defined"}, {"name": "cache_warming_scheduler.py", "type": "file", "path": "src/api/data/cache_warming_scheduler.py", "error": "name 'base' is not defined"}]}, {"name": "analytics", "type": "directory", "path": "src/api/analytics", "children": [{"name": "__init__.py", "type": "file", "path": "src/api/analytics/__init__.py"}]}, {"name": "main.py", "type": "file", "path": "src/api/main.py", "error": "'Import' object has no attribute 'body'"}, {"name": "routes", "type": "directory", "path": "src/api/routes", "children": [{"name": "metrics.py", "type": "file", "path": "src/api/routes/metrics.py", "error": "'ImportFrom' object has no attribute 'body'"}, {"name": "analytics.py", "type": "file", "path": "src/api/routes/analytics.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "debug.py", "type": "file", "path": "src/api/routes/debug.py", "error": "'ImportFrom' object has no attribute 'body'"}, {"name": "feedback.py", "type": "file", "path": "src/api/routes/feedback.py", "error": "'ImportFrom' object has no attribute 'body'"}, {"name": "bot_health.py", "type": "file", "path": "src/api/routes/bot_health.py", "error": "name 'base' is not defined"}, {"name": "market_data.py", "type": "file", "path": "src/api/routes/market_data.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/api/routes/__init__.py"}, {"name": "health.py", "type": "file", "path": "src/api/routes/health.py", "error": "'ImportFrom' object has no attribute 'body'"}, {"name": "dashboard.py", "type": "file", "path": "src/api/routes/dashboard.py", "error": "'ImportFrom' object has no attribute 'body'"}]}, {"name": "config.py", "type": "file", "path": "src/api/config.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "webhooks", "type": "directory", "path": "src/api/webhooks", "children": [{"name": "__init__.py", "type": "file", "path": "src/api/webhooks/__init__.py"}]}, {"name": "routers", "type": "directory", "path": "src/api/routers", "children": [{"name": "market_data.py", "type": "file", "path": "src/api/routers/market_data.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "__init__.py", "type": "file", "path": "src/api/routers/__init__.py"}]}, {"name": "__init__.py", "type": "file", "path": "src/api/__init__.py"}, {"name": "schemas", "type": "directory", "path": "src/api/schemas", "children": [{"name": "feedback_schema.py", "type": "file", "path": "src/api/schemas/feedback_schema.py", "error": "name 'base' is not defined"}, {"name": "metrics_schema.py", "type": "file", "path": "src/api/schemas/metrics_schema.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/api/schemas/__init__.py"}]}, {"name": "middleware", "type": "directory", "path": "src/api/middleware", "children": [{"name": "security.py", "type": "file", "path": "src/api/middleware/security.py", "error": "name 'base' is not defined"}, {"name": "security_utils.py", "type": "file", "path": "src/api/middleware/security_utils.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "__init__.py", "type": "file", "path": "src/api/middleware/__init__.py"}]}]}, {"name": "analysis", "type": "directory", "path": "src/analysis", "children": [{"name": "probability", "type": "directory", "path": "src/analysis/probability", "children": [{"name": "monte_carlo_simulator.py", "type": "file", "path": "src/analysis/probability/monte_carlo_simulator.py", "error": "name 'base' is not defined"}, {"name": "probability_engine.py", "type": "file", "path": "src/analysis/probability/probability_engine.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/analysis/probability/__init__.py"}, {"name": "probability_response_service.py", "type": "file", "path": "src/analysis/probability/probability_response_service.py", "error": "name 'base' is not defined"}]}, {"name": "ai", "type": "directory", "path": "src/analysis/ai", "children": [{"name": "calculators", "type": "directory", "path": "src/analysis/ai/calculators", "children": [{"name": "__init__.py", "type": "file", "path": "src/analysis/ai/calculators/__init__.py"}, {"name": "sentiment_calculator.py", "type": "file", "path": "src/analysis/ai/calculators/sentiment_calculator.py", "error": "name 'base' is not defined"}]}, {"name": "enhancement_strategy.py", "type": "file", "path": "src/analysis/ai/enhancement_strategy.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/analysis/ai/__init__.py"}, {"name": "recommendation_engine.py", "type": "file", "path": "src/analysis/ai/recommendation_engine.py", "error": "name 'base' is not defined"}]}, {"name": "technical", "type": "directory", "path": "src/analysis/technical", "children": [{"name": "__init__.py", "type": "file", "path": "src/analysis/technical/__init__.py"}]}, {"name": "orchestration", "type": "directory", "path": "src/analysis/orchestration", "children": [{"name": "enhancement_strategy.py", "type": "file", "path": "src/analysis/orchestration/enhancement_strategy.py", "error": "name 'base' is not defined"}, {"name": "analysis_orchestrator.py", "type": "file", "path": "src/analysis/orchestration/analysis_orchestrator.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/analysis/orchestration/__init__.py"}]}, {"name": "enhanced_evaluator.py", "type": "file", "path": "src/analysis/enhanced_evaluator.py", "error": "name 'base' is not defined"}, {"name": "utils", "type": "directory", "path": "src/analysis/utils", "children": [{"name": "data_validators.py", "type": "file", "path": "src/analysis/utils/data_validators.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/analysis/utils/__init__.py"}]}, {"name": "__init__.py", "type": "file", "path": "src/analysis/__init__.py"}, {"name": "risk", "type": "directory", "path": "src/analysis/risk", "children": [{"name": "assessment.py", "type": "file", "path": "src/analysis/risk/assessment.py", "error": "name 'base' is not defined"}, {"name": "calculators", "type": "directory", "path": "src/analysis/risk/calculators", "children": [{"name": "beta_calculator.py", "type": "file", "path": "src/analysis/risk/calculators/beta_calculator.py", "error": "name 'base' is not defined"}, {"name": "volatility_calculator.py", "type": "file", "path": "src/analysis/risk/calculators/volatility_calculator.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/analysis/risk/calculators/__init__.py"}]}, {"name": "__init__.py", "type": "file", "path": "src/analysis/risk/__init__.py"}]}, {"name": "templates", "type": "directory", "path": "src/analysis/templates", "children": [{"name": "analysis_response_template.py", "type": "file", "path": "src/analysis/templates/analysis_response_template.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/analysis/templates/__init__.py"}]}, {"name": "fundamental", "type": "directory", "path": "src/analysis/fundamental", "children": [{"name": "metrics.py", "type": "file", "path": "src/analysis/fundamental/metrics.py", "error": "name 'base' is not defined"}, {"name": "calculators", "type": "directory", "path": "src/analysis/fundamental/calculators", "children": [{"name": "pe_calculator.py", "type": "file", "path": "src/analysis/fundamental/calculators/pe_calculator.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/analysis/fundamental/calculators/__init__.py"}, {"name": "growth_calculator.py", "type": "file", "path": "src/analysis/fundamental/calculators/growth_calculator.py", "error": "name 'base' is not defined"}]}, {"name": "__init__.py", "type": "file", "path": "src/analysis/fundamental/__init__.py"}]}]}, {"name": "utils", "type": "directory", "path": "src/utils", "children": []}, {"name": "logs", "type": "directory", "path": "src/logs", "children": [{"name": "__init__.py", "type": "file", "path": "src/logs/__init__.py"}, {"name": "app.log", "type": "file", "path": "src/logs/app.log"}]}, {"name": "core", "type": "directory", "path": "src/core", "children": [{"name": "feedback_mechanism.py", "type": "file", "path": "src/core/feedback_mechanism.py", "error": "name 'base' is not defined"}, {"name": "validation", "type": "directory", "path": "src/core/validation", "children": [{"name": "financial_validator.py", "type": "file", "path": "src/core/validation/financial_validator.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/core/validation/__init__.py"}]}, {"name": "exceptions.py", "type": "file", "path": "src/core/exceptions.py", "error": "name 'base' is not defined"}, {"name": "scheduler.py", "type": "file", "path": "src/core/scheduler.py", "error": "'Import' object has no attribute 'body'"}, {"name": "watchlist", "type": "directory", "path": "src/core/watchlist", "children": [{"name": "__init__.py", "type": "file", "path": "src/core/watchlist/__init__.py"}]}, {"name": "logger.py", "type": "file", "path": "src/core/logger.py", "commands": [], "pipelines": [], "imports": [{"type": "from_import", "module": "src.shared.error_handling.logging", "name": "get_logger", "alias": null}, {"type": "from_import", "module": "src.shared.error_handling.logging", "name": "configure_logging", "alias": null}, {"type": "from_import", "module": "src.shared.error_handling.logging", "name": "generate_correlation_id", "alias": null}, {"type": "from_import", "module": "src.shared.error_handling.logging", "name": "get_pipeline_logger", "alias": null}, {"type": "from_import", "module": "src.shared.error_handling.logging", "name": "get_trading_logger", "alias": null}, {"type": "from_import", "module": "src.shared.error_handling.logging", "name": "log_request", "alias": null}, {"type": "from_import", "module": "src.shared.error_handling.logging", "name": "log_error", "alias": null}], "classes": [], "functions": []}, {"name": "utils.py", "type": "file", "path": "src/core/utils.py", "error": "'Import' object has no attribute 'body'"}, {"name": "enums", "type": "directory", "path": "src/core/enums", "children": [{"name": "stock_analysis.py", "type": "file", "path": "src/core/enums/stock_analysis.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/core/enums/__init__.py"}]}, {"name": "prompts", "type": "directory", "path": "src/core/prompts", "children": [{"name": "models.py", "type": "file", "path": "src/core/prompts/models.py", "error": "name 'base' is not defined"}, {"name": "prompt_manager.py", "type": "file", "path": "src/core/prompts/prompt_manager.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/core/prompts/__init__.py"}, {"name": "templates", "type": "directory", "path": "src/core/prompts/templates", "children": [{"name": "__init__.py", "type": "file", "path": "src/core/prompts/templates/__init__.py"}, {"name": "system_prompt.txt", "type": "file", "path": "src/core/prompts/templates/system_prompt.txt"}]}]}, {"name": "formatting", "type": "directory", "path": "src/core/formatting", "children": [{"name": "text_formatting.py", "type": "file", "path": "src/core/formatting/text_formatting.py", "error": "'ImportFrom' object has no attribute 'body'"}, {"name": "analysis_template.py", "type": "file", "path": "src/core/formatting/analysis_template.py", "error": "name 'base' is not defined"}, {"name": "technical_analysis.py", "type": "file", "path": "src/core/formatting/technical_analysis.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/core/formatting/__init__.py"}, {"name": "response_templates.py", "type": "file", "path": "src/core/formatting/response_templates.py", "error": "name 'base' is not defined"}]}, {"name": "trade_scanner.py", "type": "file", "path": "src/core/trade_scanner.py", "error": "name 'base' is not defined"}, {"name": "config_manager.py", "type": "file", "path": "src/core/config_manager.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/core/__init__.py"}, {"name": "automation", "type": "directory", "path": "src/core/automation", "children": [{"name": "report_engine.py", "type": "file", "path": "src/core/automation/report_engine.py", "error": "name 'base' is not defined"}, {"name": "analysis_scheduler.py", "type": "file", "path": "src/core/automation/analysis_scheduler.py", "error": "name 'base' is not defined"}, {"name": "report_scheduler.py", "type": "file", "path": "src/core/automation/report_scheduler.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/core/automation/__init__.py"}, {"name": "discord_handler.py", "type": "file", "path": "src/core/automation/discord_handler.py", "error": "name 'base' is not defined"}, {"name": "report_formatter.py", "type": "file", "path": "src/core/automation/report_formatter.py", "error": "name 'base' is not defined"}]}, {"name": "response_generator.py", "type": "file", "path": "src/core/response_generator.py", "error": "name 'base' is not defined"}, {"name": "market_calendar.py", "type": "file", "path": "src/core/market_calendar.py", "error": "name 'base' is not defined"}, {"name": "risk_management", "type": "directory", "path": "src/core/risk_management", "children": [{"name": "atr_calculator.py", "type": "file", "path": "src/core/risk_management/atr_calculator.py", "error": "name 'base' is not defined"}, {"name": "compliance_framework.py", "type": "file", "path": "src/core/risk_management/compliance_framework.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/core/risk_management/__init__.py"}]}, {"name": "data_quality_validator.py", "type": "file", "path": "src/core/data_quality_validator.py", "error": "name 'base' is not defined"}, {"name": "pipeline_engine.py", "type": "file", "path": "src/core/pipeline_engine.py", "error": "name 'base' is not defined"}, {"name": "monitoring_pkg", "type": "directory", "path": "src/core/monitoring_pkg", "children": [{"name": "bot_monitor.py", "type": "file", "path": "src/core/monitoring_pkg/bot_monitor.py", "error": "name 'base' is not defined"}, {"name": "performance_tracker.py", "type": "file", "path": "src/core/monitoring_pkg/performance_tracker.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/core/monitoring_pkg/__init__.py"}]}, {"name": "secure_cache.py", "type": "file", "path": "src/core/secure_cache.py", "error": "name 'base' is not defined"}]}, {"name": "shared", "type": "directory", "path": "src/shared", "children": [{"name": "background", "type": "directory", "path": "src/shared/background", "children": [{"name": "celery_app.py", "type": "file", "path": "src/shared/background/celery_app.py", "commands": [], "pipelines": [], "imports": [{"type": "import", "module": "os", "alias": null}, {"type": "from_import", "module": "celery", "name": "Celery", "alias": null}, {"type": "from_import", "module": "src.core.config_manager", "name": "config", "alias": null}, {"type": "import", "module": "logging", "alias": null}, {"type": "import", "module": "importlib", "alias": null}], "classes": [], "functions": []}, {"name": "tasks", "type": "directory", "path": "src/shared/background/tasks", "children": [{"name": "indicators.py", "type": "file", "path": "src/shared/background/tasks/indicators.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "market_intelligence.py", "type": "file", "path": "src/shared/background/tasks/market_intelligence.py", "error": "unexpected character after line continuation character (<unknown>, line 1)"}, {"name": "__init__.py", "type": "file", "path": "src/shared/background/tasks/__init__.py"}]}, {"name": "__init__.py", "type": "file", "path": "src/shared/background/__init__.py"}]}, {"name": "analytics", "type": "directory", "path": "src/shared/analytics", "children": [{"name": "performance_tracker.py", "type": "file", "path": "src/shared/analytics/performance_tracker.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/shared/analytics/__init__.py"}]}, {"name": "config", "type": "directory", "path": "src/shared/config", "children": [{"name": "config_manager.py", "type": "file", "path": "src/shared/config/config_manager.py", "error": "name 'base' is not defined"}]}, {"name": "configuration", "type": "directory", "path": "src/shared/configuration", "children": [{"name": "__init__.py", "type": "file", "path": "src/shared/configuration/__init__.py"}, {"name": "validators.py", "type": "file", "path": "src/shared/configuration/validators.py", "error": "name 'base' is not defined"}]}, {"name": "redis", "type": "directory", "path": "src/shared/redis", "children": [{"name": "redis_manager.py", "type": "file", "path": "src/shared/redis/redis_manager.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/shared/redis/__init__.py"}]}, {"name": "watchlist", "type": "directory", "path": "src/shared/watchlist", "children": [{"name": "models.py", "type": "file", "path": "src/shared/watchlist/models.py", "error": "name 'base' is not defined"}, {"name": "base_manager.py", "type": "file", "path": "src/shared/watchlist/base_manager.py", "error": "name 'base' is not defined"}, {"name": "bot_manager.py", "type": "file", "path": "src/shared/watchlist/bot_manager.py", "error": "name 'base' is not defined"}, {"name": "webhook_manager.py", "type": "file", "path": "src/shared/watchlist/webhook_manager.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/shared/watchlist/__init__.py"}, {"name": "supabase_manager.py", "type": "file", "path": "src/shared/watchlist/supabase_manager.py", "error": "name 'base' is not defined"}]}, {"name": "services", "type": "directory", "path": "src/shared/services", "children": [{"name": "optimization_service.py", "type": "file", "path": "src/shared/services/optimization_service.py", "error": "name 'base' is not defined"}, {"name": "enhanced_performance_optimizer.py", "type": "file", "path": "src/shared/services/enhanced_performance_optimizer.py", "error": "name 'base' is not defined"}, {"name": "performance_monitor.py", "type": "file", "path": "src/shared/services/performance_monitor.py", "error": "name 'base' is not defined"}]}, {"name": "sentiment", "type": "directory", "path": "src/shared/sentiment", "children": [{"name": "sentiment_analyzer.py", "type": "file", "path": "src/shared/sentiment/sentiment_analyzer.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/shared/sentiment/__init__.py"}]}, {"name": "market_analysis", "type": "directory", "path": "src/shared/market_analysis", "children": [{"name": "signals.py", "type": "file", "path": "src/shared/market_analysis/signals.py", "error": "name 'base' is not defined"}, {"name": "unified_signal_analyzer.py", "type": "file", "path": "src/shared/market_analysis/unified_signal_analyzer.py", "error": "name 'base' is not defined"}, {"name": "signal_analyzer.py", "type": "file", "path": "src/shared/market_analysis/signal_analyzer.py", "error": "name 'base' is not defined"}, {"name": "confidence_scorer.py", "type": "file", "path": "src/shared/market_analysis/confidence_scorer.py", "error": "name 'base' is not defined"}, {"name": "utils.py", "type": "file", "path": "src/shared/market_analysis/utils.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "__init__.py", "type": "file", "path": "src/shared/market_analysis/__init__.py"}]}, {"name": "data_providers", "type": "directory", "path": "src/shared/data_providers", "children": [{"name": "fallback_provider.py", "type": "file", "path": "src/shared/data_providers/fallback_provider.py", "error": "name 'base' is not defined"}, {"name": "aggregator.py", "type": "file", "path": "src/shared/data_providers/aggregator.py", "error": "name 'base' is not defined"}, {"name": "unified_base.py", "type": "file", "path": "src/shared/data_providers/unified_base.py", "error": "name 'base' is not defined"}, {"name": "finnhub_provider.py", "type": "file", "path": "src/shared/data_providers/finnhub_provider.py", "error": "name 'base' is not defined"}, {"name": "yfinance_provider.py", "type": "file", "path": "src/shared/data_providers/yfinance_provider.py", "error": "name 'base' is not defined"}, {"name": "alpha_vantage.py", "type": "file", "path": "src/shared/data_providers/alpha_vantage.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/shared/data_providers/__init__.py"}, {"name": "base.py", "type": "file", "path": "src/shared/data_providers/base.py", "error": "name 'base' is not defined"}, {"name": "polygon_provider.py", "type": "file", "path": "src/shared/data_providers/polygon_provider.py", "error": "name 'base' is not defined"}, {"name": "alpaca_provider.py", "type": "file", "path": "src/shared/data_providers/alpaca_provider.py", "error": "name 'base' is not defined"}]}, {"name": "ai", "type": "directory", "path": "src/shared/ai", "children": [{"name": "model_fine_tuner.py", "type": "file", "path": "src/shared/ai/model_fine_tuner.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/shared/ai/__init__.py"}, {"name": "depth_controller.py", "type": "file", "path": "src/shared/ai/depth_controller.py", "error": "name 'base' is not defined"}, {"name": "recommendation_engine.py", "type": "file", "path": "src/shared/ai/recommendation_engine.py", "error": "name 'base' is not defined"}]}, {"name": "ai_chat", "type": "directory", "path": "src/shared/ai_chat", "children": [{"name": "data_fetcher.py", "type": "file", "path": "src/shared/ai_chat/data_fetcher.py", "error": "name 'base' is not defined"}, {"name": "config.py", "type": "file", "path": "src/shared/ai_chat/config.py", "error": "name 'base' is not defined"}, {"name": "models.py", "type": "file", "path": "src/shared/ai_chat/models.py", "error": "name 'base' is not defined"}, {"name": "fallbacks.py", "type": "file", "path": "src/shared/ai_chat/fallbacks.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/shared/ai_chat/__init__.py"}, {"name": "response_formatter.py", "type": "file", "path": "src/shared/ai_chat/response_formatter.py", "error": "name 'base' is not defined"}, {"name": "ai_client.py", "type": "file", "path": "src/shared/ai_chat/ai_client.py", "error": "name 'base' is not defined"}, {"name": "processor.py", "type": "file", "path": "src/shared/ai_chat/processor.py", "error": "name 'base' is not defined"}]}, {"name": "metrics", "type": "directory", "path": "src/shared/metrics", "children": [{"name": "naming_conventions.py", "type": "file", "path": "src/shared/metrics/naming_conventions.py", "error": "name 'base' is not defined"}, {"name": "metrics_service.py", "type": "file", "path": "src/shared/metrics/metrics_service.py", "error": "name 'base' is not defined"}, {"name": "unified_metrics_service.py", "type": "file", "path": "src/shared/metrics/unified_metrics_service.py", "error": "name 'base' is not defined"}]}, {"name": "data_validation.py", "type": "file", "path": "src/shared/data_validation.py", "error": "name 'base' is not defined"}, {"name": "utils", "type": "directory", "path": "src/shared/utils", "children": [{"name": "discord_helpers.py", "type": "file", "path": "src/shared/utils/discord_helpers.py", "error": "name 'base' is not defined"}, {"name": "deprecation.py", "type": "file", "path": "src/shared/utils/deprecation.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "__init__.py", "type": "file", "path": "src/shared/utils/__init__.py"}, {"name": "symbol_extraction.py", "type": "file", "path": "src/shared/utils/symbol_extraction.py", "error": "name 'base' is not defined"}]}, {"name": "monitoring", "type": "directory", "path": "src/shared/monitoring", "children": [{"name": "step_logger.py", "type": "file", "path": "src/shared/monitoring/step_logger.py", "error": "name 'base' is not defined"}, {"name": "intelligent_grader.py", "type": "file", "path": "src/shared/monitoring/intelligent_grader.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/shared/monitoring/__init__.py"}, {"name": "pipeline_grader.py", "type": "file", "path": "src/shared/monitoring/pipeline_grader.py", "error": "name 'base' is not defined"}, {"name": "pipeline_monitor.py", "type": "file", "path": "src/shared/monitoring/pipeline_monitor.py", "error": "name 'base' is not defined"}, {"name": "performance_monitor.py", "type": "file", "path": "src/shared/monitoring/performance_monitor.py", "error": "name 'base' is not defined"}]}, {"name": "error_handling", "type": "directory", "path": "src/shared/error_handling", "children": [{"name": "logging.py", "type": "file", "path": "src/shared/error_handling/logging.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/shared/error_handling/__init__.py"}, {"name": "fallback.py", "type": "file", "path": "src/shared/error_handling/fallback.py", "error": "name 'base' is not defined"}, {"name": "retry.py", "type": "file", "path": "src/shared/error_handling/retry.py", "error": "name 'base' is not defined"}]}, {"name": "__init__.py", "type": "file", "path": "src/shared/__init__.py"}, {"name": "ai_services", "type": "directory", "path": "src/shared/ai_services", "children": [{"name": "query_cache.py", "type": "file", "path": "src/shared/ai_services/query_cache.py", "error": "name 'base' is not defined"}, {"name": "ai_processor_clean.py", "type": "file", "path": "src/shared/ai_services/ai_processor_clean.py", "error": "name 'base' is not defined"}, {"name": "response_synthesizer.py", "type": "file", "path": "src/shared/ai_services/response_synthesizer.py", "error": "name 'base' is not defined"}, {"name": "ai_processor_robust.py", "type": "file", "path": "src/shared/ai_services/ai_processor_robust.py", "error": "name 'base' is not defined"}, {"name": "tool_registry.py", "type": "file", "path": "src/shared/ai_services/tool_registry.py", "error": "name 'base' is not defined"}, {"name": "timeout_manager.py", "type": "file", "path": "src/shared/ai_services/timeout_manager.py", "error": "name 'base' is not defined"}, {"name": "ai_chat_processor.py", "type": "file", "path": "src/shared/ai_services/ai_chat_processor.py", "error": "name 'base' is not defined"}, {"name": "circuit_breaker.py", "type": "file", "path": "src/shared/ai_services/circuit_breaker.py", "error": "name 'base' is not defined"}, {"name": "fallback_handler.py", "type": "file", "path": "src/shared/ai_services/fallback_handler.py", "error": "name 'base' is not defined"}, {"name": "fast_price_lookup.py", "type": "file", "path": "src/shared/ai_services/fast_price_lookup.py", "error": "name 'base' is not defined"}, {"name": "simple_query_analyzer.py", "type": "file", "path": "src/shared/ai_services/simple_query_analyzer.py", "error": "name 'base' is not defined"}, {"name": "smart_model_router.py", "type": "file", "path": "src/shared/ai_services/smart_model_router.py", "error": "name 'base' is not defined"}, {"name": "query_router.py", "type": "file", "path": "src/shared/ai_services/query_router.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/shared/ai_services/__init__.py"}, {"name": "intelligent_chatbot.py", "type": "file", "path": "src/shared/ai_services/intelligent_chatbot.py", "error": "name 'base' is not defined"}, {"name": "ai_service_wrapper.py", "type": "file", "path": "src/shared/ai_services/ai_service_wrapper.py", "error": "name 'base' is not defined"}, {"name": "openrouter_key.py", "type": "file", "path": "src/shared/ai_services/openrouter_key.py", "commands": [], "pipelines": [], "imports": [{"type": "import", "module": "os", "alias": null}], "classes": [], "functions": []}, {"name": "performance_optimizer.py", "type": "file", "path": "src/shared/ai_services/performance_optimizer.py", "error": "name 'base' is not defined"}]}, {"name": "cache", "type": "directory", "path": "src/shared/cache", "children": [{"name": "cache_service.py", "type": "file", "path": "src/shared/cache/cache_service.py", "error": "name 'base' is not defined"}]}, {"name": "technical_analysis", "type": "directory", "path": "src/shared/technical_analysis", "children": [{"name": "multi_timeframe_analyzer.py", "type": "file", "path": "src/shared/technical_analysis/multi_timeframe_analyzer.py", "error": "name 'base' is not defined"}, {"name": "unified_calculator.py", "type": "file", "path": "src/shared/technical_analysis/unified_calculator.py", "error": "name 'base' is not defined"}, {"name": "indicators.py", "type": "file", "path": "src/shared/technical_analysis/indicators.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "zones.py", "type": "file", "path": "src/shared/technical_analysis/zones.py", "error": "name 'base' is not defined"}, {"name": "config.py", "type": "file", "path": "src/shared/technical_analysis/config.py", "error": "name 'base' is not defined"}, {"name": "enhanced_indicators.py", "type": "file", "path": "src/shared/technical_analysis/enhanced_indicators.py", "error": "name 'base' is not defined"}, {"name": "calculator.py", "type": "file", "path": "src/shared/technical_analysis/calculator.py", "error": "name 'base' is not defined"}, {"name": "volume_analyzer.py", "type": "file", "path": "src/shared/technical_analysis/volume_analyzer.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/shared/technical_analysis/__init__.py"}, {"name": "test_indicators.py", "type": "file", "path": "src/shared/technical_analysis/test_indicators.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "options_greeks_calculator.py", "type": "file", "path": "src/shared/technical_analysis/options_greeks_calculator.py", "error": "name 'base' is not defined"}, {"name": "strategy_calculator.py", "type": "file", "path": "src/shared/technical_analysis/strategy_calculator.py", "error": "name 'base' is not defined"}, {"name": "signal_generator.py", "type": "file", "path": "src/shared/technical_analysis/signal_generator.py", "error": "name 'base' is not defined"}]}, {"name": "database", "type": "directory", "path": "src/shared/database", "children": [{"name": "__init__.py", "type": "file", "path": "src/shared/database/__init__.py"}, {"name": "usage_example.py", "type": "file", "path": "src/shared/database/usage_example.py", "error": "'Expr' object has no attribute 'body'"}]}, {"name": "ai_debugger", "type": "directory", "path": "src/shared/ai_debugger", "children": [{"name": "local_pattern_debugger.py", "type": "file", "path": "src/shared/ai_debugger/local_pattern_debugger.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/shared/ai_debugger/__init__.py"}, {"name": "live_ai_debugger.py", "type": "file", "path": "src/shared/ai_debugger/live_ai_debugger.py", "error": "name 'base' is not defined"}]}]}, {"name": "__init__.py", "type": "file", "path": "src/__init__.py"}, {"name": "templates", "type": "directory", "path": "src/templates", "children": [{"name": "analysis_response.py", "type": "file", "path": "src/templates/analysis_response.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/templates/__init__.py"}]}, {"name": "database", "type": "directory", "path": "src/database", "children": [{"name": "config.py", "type": "file", "path": "src/database/config.py", "error": "'Expr' object has no attribute 'body'"}, {"name": "models", "type": "directory", "path": "src/database/models", "children": [{"name": "analysis.py", "type": "file", "path": "src/database/models/analysis.py", "error": "name 'base' is not defined"}, {"name": "alerts.py", "type": "file", "path": "src/database/models/alerts.py", "error": "name 'base' is not defined"}, {"name": "market_data.py", "type": "file", "path": "src/database/models/market_data.py", "error": "name 'base' is not defined"}, {"name": "interactions.py", "type": "file", "path": "src/database/models/interactions.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/database/models/__init__.py"}]}, {"name": "migrations", "type": "directory", "path": "src/database/migrations", "children": [{"name": "env.py", "type": "file", "path": "src/database/migrations/env.py", "error": "'Import' object has no attribute 'body'"}, {"name": "__init__.py", "type": "file", "path": "src/database/migrations/__init__.py"}]}, {"name": "query_optimizer.py", "type": "file", "path": "src/database/query_optimizer.py", "error": "name 'base' is not defined"}, {"name": "__init__.py", "type": "file", "path": "src/database/__init__.py"}, {"name": "repositories", "type": "directory", "path": "src/database/repositories", "children": [{"name": "__init__.py", "type": "file", "path": "src/database/repositories/__init__.py"}]}, {"name": "unified_client.py", "type": "file", "path": "src/database/unified_client.py", "error": "name 'base' is not defined"}, {"name": "unified_db.py", "type": "file", "path": "src/database/unified_db.py", "error": "name 'base' is not defined"}, {"name": "query_wrapper.py", "type": "file", "path": "src/database/query_wrapper.py", "error": "'Expr' object has no attribute 'body'"}]}]}]}