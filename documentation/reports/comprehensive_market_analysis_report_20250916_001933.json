{"timestamp": "20250916_001933", "summary": {"total_tests": 17, "successful_tests": 17, "avg_score": 5.***************, "avg_time": 1.****************}, "routing_analysis": {"1": {"basic_price_lookup": [{"routes_used": ["data_provider"], "efficiency": 1.0, "score": 10.0}], "basic_trend": [{"routes_used": ["data_provider"], "efficiency": 0.5, "score": 5.0}], "basic_fundamental": [{"routes_used": ["data_provider"], "efficiency": 0.5, "score": 5.0}]}, "2": {"comparative_analysis": [{"routes_used": ["analysis_engine", "comparison_tool", "data_provider"], "efficiency": 1.0, "score": 5.***************}], "performance_ranking": [{"routes_used": ["analysis_engine", "comparison_tool", "data_provider"], "efficiency": 1.0, "score": 5.***************}], "correlation_analysis": [{"routes_used": ["analysis_engine", "comparison_tool", "data_provider"], "efficiency": 1.0, "score": 5.***************}]}, "3": {"technical_indicators": [{"routes_used": ["analysis_engine", "comparison_tool", "data_provider"], "efficiency": 1.0, "score": 5.***************}], "volume_anomaly": [{"routes_used": ["quantitative_engine", "data_provider", "risk_analysis", "optimization"], "efficiency": 1.***************3, "score": 5.***************}], "breakout_screening": [{"routes_used": ["quantitative_engine", "data_provider", "risk_analysis", "optimization"], "efficiency": 1.***************3, "score": 5.***************}]}, "4": {"sector_performance": [{"routes_used": ["quantitative_engine", "data_provider", "risk_analysis", "optimization"], "efficiency": 1.***************3, "score": 5.***************}], "macro_impact": [{"routes_used": ["quantitative_engine", "data_provider", "risk_analysis", "optimization"], "efficiency": 1.***************3, "score": 5.***************}]}, "5": {"portfolio_optimization": [{"routes_used": ["risk_modeling", "ml_models", "simulation", "quantitative_engine", "data_provider"], "efficiency": 1.****************, "score": 5.***************}], "monte_carlo": [{"routes_used": ["risk_modeling", "ml_models", "simulation", "quantitative_engine", "data_provider"], "efficiency": 1.****************, "score": 5.***************}]}, "6": {"algorithmic_trading": [{"routes_used": ["risk_modeling", "ml_models", "simulation", "quantitative_engine", "data_provider"], "efficiency": 1.****************, "score": 5.***************}], "risk_management": [{"routes_used": ["risk_modeling", "ml_models", "simulation", "quantitative_engine", "data_provider"], "efficiency": 1.****************, "score": 5.***************}]}, "7": {"multi_factor_stress": [{"routes_used": ["risk_modeling", "ml_models", "simulation", "quantitative_engine", "data_provider"], "efficiency": 1.25, "score": 4.75}], "ml_credit_modeling": [{"routes_used": ["risk_modeling", "ml_models", "simulation", "quantitative_engine", "data_provider"], "efficiency": 1.25, "score": 4.75}]}}, "capability_log": {"1": {"basic_price_lookup": [{"capabilities": ["data_retrieval", "symbol_recognition"], "coverage": 0.5, "score": 5.0}], "basic_trend": [{"capabilities": ["data_retrieval", "symbol_recognition"], "coverage": 0.0, "score": 0.0}], "basic_fundamental": [{"capabilities": ["data_retrieval", "symbol_recognition"], "coverage": 0.0, "score": 0.0}]}, "2": {"comparative_analysis": [{"capabilities": ["comparison", "interpretation", "analysis", "data_retrieval"], "coverage": 0.***************3, "score": 3.***************}], "performance_ranking": [{"capabilities": ["comparison", "interpretation", "analysis", "data_retrieval"], "coverage": 0.0, "score": 0.0}], "correlation_analysis": [{"capabilities": ["comparison", "interpretation", "analysis", "data_retrieval"], "coverage": 0.0, "score": 0.0}]}, "3": {"technical_indicators": [{"capabilities": ["comparison", "interpretation", "analysis", "data_retrieval"], "coverage": 0.***************3, "score": 3.***************}], "volume_anomaly": [{"capabilities": ["risk_analysis", "statistical_inference", "quantitative_modeling", "optimization"], "coverage": 0.0, "score": 0.0}], "breakout_screening": [{"capabilities": ["risk_analysis", "statistical_inference", "quantitative_modeling", "optimization"], "coverage": 0.0, "score": 0.0}]}, "4": {"sector_performance": [{"capabilities": ["risk_analysis", "statistical_inference", "quantitative_modeling", "optimization"], "coverage": 0.0, "score": 0.0}], "macro_impact": [{"capabilities": ["risk_analysis", "statistical_inference", "quantitative_modeling", "optimization"], "coverage": 0.0, "score": 0.0}]}, "5": {"portfolio_optimization": [{"capabilities": ["machine_learning", "multi_factor_analysis", "simulation", "advanced_modeling"], "coverage": 0.0, "score": 0.0}], "monte_carlo": [{"capabilities": ["machine_learning", "multi_factor_analysis", "simulation", "advanced_modeling"], "coverage": 0.***************3, "score": 3.***************}]}, "6": {"algorithmic_trading": [{"capabilities": ["machine_learning", "multi_factor_analysis", "simulation", "advanced_modeling"], "coverage": 0.0, "score": 0.0}], "risk_management": [{"capabilities": ["machine_learning", "multi_factor_analysis", "simulation", "advanced_modeling"], "coverage": 0.0, "score": 0.0}]}, "7": {"multi_factor_stress": [{"capabilities": ["machine_learning", "multi_factor_analysis", "simulation", "advanced_modeling"], "coverage": 0.0, "score": 0.0}], "ml_credit_modeling": [{"capabilities": ["machine_learning", "multi_factor_analysis", "simulation", "advanced_modeling"], "coverage": 0.25, "score": 2.5}]}}, "detailed_results": [{"query_id": "BASIC_001", "level": 1, "complexity": 1, "success": true, "execution_time": 0.****************, "overall_score": 6.***************, "grade": "B", "routing_analysis": {"score": 10.0, "coverage": 1.0, "efficiency": 1.0, "expected_routes": ["data_provider"], "actual_routes": ["data_provider"], "missing_routes": [], "extra_routes": []}, "capability_analysis": {"score": 5.0, "coverage": 0.5, "expected_capabilities": ["price_data", "symbol_recognition"], "demonstrated_capabilities": ["data_retrieval", "symbol_recognition"], "missing_capabilities": ["price_data"], "extra_capabilities": ["data_retrieval"]}, "quality_analysis": {"score": 6.85, "confidence": 0.95, "data_quality": "high", "analysis_depth": "basic", "insights_generated": 1}, "performance_analysis": {"score": 4.***************, "execution_time": 0.****************, "expected_time": 0.5, "time_efficiency": 0.****************}, "response_data": {"response_content": "Comprehensive analysis for basic_price_lookup query", "routes_used": ["data_provider"], "capabilities_demonstrated": ["data_retrieval", "symbol_recognition"], "confidence": 0.95, "data_quality": "high", "analysis_depth": "basic", "processing_method": "AI-enhanced", "data_sources": 2, "computational_complexity": 1, "insights_generated": 1}}, {"query_id": "BASIC_002", "level": 1, "complexity": 2, "success": true, "execution_time": 0.****************, "overall_score": 4.***************, "grade": "C", "routing_analysis": {"score": 5.0, "coverage": 0.5, "efficiency": 0.5, "expected_routes": ["trend_analysis", "data_provider"], "actual_routes": ["data_provider"], "missing_routes": ["trend_analysis"], "extra_routes": []}, "capability_analysis": {"score": 0.0, "coverage": 0.0, "expected_capabilities": ["trend_calculation", "price_data"], "demonstrated_capabilities": ["data_retrieval", "symbol_recognition"], "missing_capabilities": ["trend_calculation", "price_data"], "extra_capabilities": ["data_retrieval", "symbol_recognition"]}, "quality_analysis": {"score": 6.85, "confidence": 0.95, "data_quality": "high", "analysis_depth": "basic", "insights_generated": 1}, "performance_analysis": {"score": 7.***************, "execution_time": 0.****************, "expected_time": 1.0, "time_efficiency": 1.****************}, "response_data": {"response_content": "Comprehensive analysis for basic_trend query", "routes_used": ["data_provider"], "capabilities_demonstrated": ["data_retrieval", "symbol_recognition"], "confidence": 0.95, "data_quality": "high", "analysis_depth": "basic", "processing_method": "AI-enhanced", "data_sources": 3, "computational_complexity": 2, "insights_generated": 1}}, {"query_id": "BASIC_003", "level": 1, "complexity": 2, "success": true, "execution_time": 0.****************, "overall_score": 4.***************, "grade": "C", "routing_analysis": {"score": 5.0, "coverage": 0.5, "efficiency": 0.5, "expected_routes": ["fundamental_data", "data_provider"], "actual_routes": ["data_provider"], "missing_routes": ["fundamental_data"], "extra_routes": []}, "capability_analysis": {"score": 0.0, "coverage": 0.0, "expected_capabilities": ["calculation", "fundamental_data"], "demonstrated_capabilities": ["data_retrieval", "symbol_recognition"], "missing_capabilities": ["calculation", "fundamental_data"], "extra_capabilities": ["data_retrieval", "symbol_recognition"]}, "quality_analysis": {"score": 6.85, "confidence": 0.95, "data_quality": "high", "analysis_depth": "basic", "insights_generated": 1}, "performance_analysis": {"score": 7.***************, "execution_time": 0.****************, "expected_time": 1.0, "time_efficiency": 1.***************}, "response_data": {"response_content": "Comprehensive analysis for basic_fundamental query", "routes_used": ["data_provider"], "capabilities_demonstrated": ["data_retrieval", "symbol_recognition"], "confidence": 0.95, "data_quality": "high", "analysis_depth": "basic", "processing_method": "AI-enhanced", "data_sources": 3, "computational_complexity": 2, "insights_generated": 1}}, {"query_id": "COMP_001", "level": 2, "complexity": 4, "success": true, "execution_time": 1.****************, "overall_score": 5.****************, "grade": "C+", "routing_analysis": {"score": 5.***************, "coverage": 0.***************3, "efficiency": 1.0, "expected_routes": ["fundamental_analysis", "comparison_engine", "data_provider"], "actual_routes": ["analysis_engine", "comparison_tool", "data_provider"], "missing_routes": ["fundamental_analysis", "comparison_engine"], "extra_routes": ["analysis_engine", "comparison_tool"]}, "capability_analysis": {"score": 3.***************, "coverage": 0.***************3, "expected_capabilities": ["comparison", "fundamental_data", "ratio_analysis"], "demonstrated_capabilities": ["comparison", "interpretation", "analysis", "data_retrieval"], "missing_capabilities": ["ratio_analysis", "fundamental_data"], "extra_capabilities": ["interpretation", "analysis", "data_retrieval"]}, "quality_analysis": {"score": 7.55, "confidence": 0.85, "data_quality": "high", "analysis_depth": "moderate", "insights_generated": 2}, "performance_analysis": {"score": 9.***************, "execution_time": 1.****************, "expected_time": 2.0, "time_efficiency": 1.8169296634072023}, "response_data": {"response_content": "Comprehensive analysis for comparative_analysis query", "routes_used": ["data_provider", "analysis_engine", "comparison_tool"], "capabilities_demonstrated": ["data_retrieval", "analysis", "comparison", "interpretation"], "confidence": 0.85, "data_quality": "high", "analysis_depth": "moderate", "processing_method": "AI-enhanced", "data_sources": 5, "computational_complexity": 4, "insights_generated": 2}}, {"query_id": "COMP_002", "level": 2, "complexity": 5, "success": true, "execution_time": 1.****************, "overall_score": 5.***************, "grade": "C+", "routing_analysis": {"score": 5.***************, "coverage": 0.***************3, "efficiency": 1.0, "expected_routes": ["ranking_engine", "performance_analysis", "data_provider"], "actual_routes": ["analysis_engine", "comparison_tool", "data_provider"], "missing_routes": ["ranking_engine", "performance_analysis"], "extra_routes": ["analysis_engine", "comparison_tool"]}, "capability_analysis": {"score": 0.0, "coverage": 0.0, "expected_capabilities": ["performance_calculation", "ranking", "historical_data"], "demonstrated_capabilities": ["comparison", "interpretation", "analysis", "data_retrieval"], "missing_capabilities": ["performance_calculation", "ranking", "historical_data"], "extra_capabilities": ["comparison", "interpretation", "analysis", "data_retrieval"]}, "quality_analysis": {"score": 7.55, "confidence": 0.85, "data_quality": "high", "analysis_depth": "moderate", "insights_generated": 2}, "performance_analysis": {"score": 9.***************, "execution_time": 1.****************, "expected_time": 2.5, "time_efficiency": 1.9218028631595925}, "response_data": {"response_content": "Comprehensive analysis for performance_ranking query", "routes_used": ["data_provider", "analysis_engine", "comparison_tool"], "capabilities_demonstrated": ["data_retrieval", "analysis", "comparison", "interpretation"], "confidence": 0.85, "data_quality": "high", "analysis_depth": "moderate", "processing_method": "AI-enhanced", "data_sources": 6, "computational_complexity": 5, "insights_generated": 2}}, {"query_id": "COMP_003", "level": 2, "complexity": 6, "success": true, "execution_time": 1.****************, "overall_score": 5.***************, "grade": "C+", "routing_analysis": {"score": 5.***************, "coverage": 0.***************3, "efficiency": 1.0, "expected_routes": ["crypto_data", "data_provider", "correlation_engine"], "actual_routes": ["analysis_engine", "comparison_tool", "data_provider"], "missing_routes": ["crypto_data", "correlation_engine"], "extra_routes": ["analysis_engine", "comparison_tool"]}, "capability_analysis": {"score": 0.0, "coverage": 0.0, "expected_capabilities": ["statistical_analysis", "crypto_data", "correlation_calculation"], "demonstrated_capabilities": ["comparison", "interpretation", "analysis", "data_retrieval"], "missing_capabilities": ["statistical_analysis", "crypto_data", "correlation_calculation"], "extra_capabilities": ["comparison", "interpretation", "analysis", "data_retrieval"]}, "quality_analysis": {"score": 7.55, "confidence": 0.85, "data_quality": "high", "analysis_depth": "moderate", "insights_generated": 3}, "performance_analysis": {"score": 9.**************, "execution_time": 1.****************, "expected_time": 3.0, "time_efficiency": 1.997606601943104}, "response_data": {"response_content": "Comprehensive analysis for correlation_analysis query", "routes_used": ["data_provider", "analysis_engine", "comparison_tool"], "capabilities_demonstrated": ["data_retrieval", "analysis", "comparison", "interpretation"], "confidence": 0.85, "data_quality": "high", "analysis_depth": "moderate", "processing_method": "AI-enhanced", "data_sources": 7, "computational_complexity": 6, "insights_generated": 3}}, {"query_id": "TECH_001", "level": 3, "complexity": 6, "success": true, "execution_time": 1.***************, "overall_score": 6.****************, "grade": "B", "routing_analysis": {"score": 5.***************, "coverage": 0.***************3, "efficiency": 1.0, "expected_routes": ["technical_analysis", "data_provider", "indicator_calculation"], "actual_routes": ["analysis_engine", "comparison_tool", "data_provider"], "missing_routes": ["technical_analysis", "indicator_calculation"], "extra_routes": ["analysis_engine", "comparison_tool"]}, "capability_analysis": {"score": 3.***************, "coverage": 0.***************3, "expected_capabilities": ["interpretation", "technical_analysis", "indicator_calculation"], "demonstrated_capabilities": ["comparison", "interpretation", "analysis", "data_retrieval"], "missing_capabilities": ["technical_analysis", "indicator_calculation"], "extra_capabilities": ["comparison", "analysis", "data_retrieval"]}, "quality_analysis": {"score": 7.55, "confidence": 0.85, "data_quality": "high", "analysis_depth": "moderate", "insights_generated": 3}, "performance_analysis": {"score": 9.***************, "execution_time": 1.***************, "expected_time": 3.0, "time_efficiency": 1.9986051122271555}, "response_data": {"response_content": "Comprehensive analysis for technical_indicators query", "routes_used": ["data_provider", "analysis_engine", "comparison_tool"], "capabilities_demonstrated": ["data_retrieval", "analysis", "comparison", "interpretation"], "confidence": 0.85, "data_quality": "high", "analysis_depth": "moderate", "processing_method": "AI-enhanced", "data_sources": 7, "computational_complexity": 6, "insights_generated": 3}}, {"query_id": "TECH_002", "level": 3, "complexity": 7, "success": true, "execution_time": 1.****************, "overall_score": 5.***************, "grade": "C+", "routing_analysis": {"score": 5.***************, "coverage": 0.***************3, "efficiency": 1.***************3, "expected_routes": ["anomaly_detection", "data_provider", "volume_analysis"], "actual_routes": ["quantitative_engine", "data_provider", "risk_analysis", "optimization"], "missing_routes": ["anomaly_detection", "volume_analysis"], "extra_routes": ["quantitative_engine", "risk_analysis", "optimization"]}, "capability_analysis": {"score": 0.0, "coverage": 0.0, "expected_capabilities": ["screening", "anomaly_detection", "volume_data"], "demonstrated_capabilities": ["risk_analysis", "statistical_inference", "quantitative_modeling", "optimization"], "missing_capabilities": ["screening", "anomaly_detection", "volume_data"], "extra_capabilities": ["quantitative_modeling", "statistical_inference", "risk_analysis", "optimization"]}, "quality_analysis": {"score": 7.25, "confidence": 0.75, "data_quality": "moderate", "analysis_depth": "advanced", "insights_generated": 3}, "performance_analysis": {"score": 10.0, "execution_time": 1.****************, "expected_time": 3.5, "time_efficiency": 2.0}, "response_data": {"response_content": "Comprehensive analysis for volume_anomaly query", "routes_used": ["data_provider", "quantitative_engine", "risk_analysis", "optimization"], "capabilities_demonstrated": ["quantitative_modeling", "risk_analysis", "optimization", "statistical_inference"], "confidence": 0.75, "data_quality": "moderate", "analysis_depth": "advanced", "processing_method": "AI-enhanced", "data_sources": 8, "computational_complexity": 7, "insights_generated": 3}}, {"query_id": "TECH_003", "level": 3, "complexity": 7, "success": true, "execution_time": 1.****************, "overall_score": 5.***************, "grade": "C+", "routing_analysis": {"score": 5.***************, "coverage": 0.***************3, "efficiency": 1.***************3, "expected_routes": ["technical_analysis", "screening_engine", "data_provider"], "actual_routes": ["quantitative_engine", "data_provider", "risk_analysis", "optimization"], "missing_routes": ["technical_analysis", "screening_engine"], "extra_routes": ["quantitative_engine", "risk_analysis", "optimization"]}, "capability_analysis": {"score": 0.0, "coverage": 0.0, "expected_capabilities": ["screening", "technical_analysis", "volume_confirmation"], "demonstrated_capabilities": ["risk_analysis", "statistical_inference", "quantitative_modeling", "optimization"], "missing_capabilities": ["screening", "technical_analysis", "volume_confirmation"], "extra_capabilities": ["quantitative_modeling", "statistical_inference", "risk_analysis", "optimization"]}, "quality_analysis": {"score": 7.25, "confidence": 0.75, "data_quality": "moderate", "analysis_depth": "advanced", "insights_generated": 3}, "performance_analysis": {"score": 10.0, "execution_time": 1.****************, "expected_time": 3.5, "time_efficiency": 2.0}, "response_data": {"response_content": "Comprehensive analysis for breakout_screening query", "routes_used": ["data_provider", "quantitative_engine", "risk_analysis", "optimization"], "capabilities_demonstrated": ["quantitative_modeling", "risk_analysis", "optimization", "statistical_inference"], "confidence": 0.75, "data_quality": "moderate", "analysis_depth": "advanced", "processing_method": "AI-enhanced", "data_sources": 8, "computational_complexity": 7, "insights_generated": 3}}, {"query_id": "SECT_001", "level": 4, "complexity": 7, "success": true, "execution_time": 1.****************, "overall_score": 5.***************, "grade": "C+", "routing_analysis": {"score": 5.***************, "coverage": 0.***************3, "efficiency": 1.***************3, "expected_routes": ["performance_comparison", "data_provider", "sector_analysis"], "actual_routes": ["quantitative_engine", "data_provider", "risk_analysis", "optimization"], "missing_routes": ["performance_comparison", "sector_analysis"], "extra_routes": ["quantitative_engine", "risk_analysis", "optimization"]}, "capability_analysis": {"score": 0.0, "coverage": 0.0, "expected_capabilities": ["market_comparison", "performance_analysis", "sector_data"], "demonstrated_capabilities": ["risk_analysis", "statistical_inference", "quantitative_modeling", "optimization"], "missing_capabilities": ["market_comparison", "sector_data", "performance_analysis"], "extra_capabilities": ["quantitative_modeling", "statistical_inference", "risk_analysis", "optimization"]}, "quality_analysis": {"score": 7.25, "confidence": 0.75, "data_quality": "moderate", "analysis_depth": "advanced", "insights_generated": 3}, "performance_analysis": {"score": 10.0, "execution_time": 1.****************, "expected_time": 3.5, "time_efficiency": 2.0}, "response_data": {"response_content": "Comprehensive analysis for sector_performance query", "routes_used": ["data_provider", "quantitative_engine", "risk_analysis", "optimization"], "capabilities_demonstrated": ["quantitative_modeling", "risk_analysis", "optimization", "statistical_inference"], "confidence": 0.75, "data_quality": "moderate", "analysis_depth": "advanced", "processing_method": "AI-enhanced", "data_sources": 8, "computational_complexity": 7, "insights_generated": 3}}, {"query_id": "SECT_002", "level": 4, "complexity": 8, "success": true, "execution_time": 1.***************, "overall_score": 5.***************, "grade": "C+", "routing_analysis": {"score": 5.***************, "coverage": 0.***************3, "efficiency": 1.***************3, "expected_routes": ["sector_impact", "macro_analysis", "data_provider"], "actual_routes": ["quantitative_engine", "data_provider", "risk_analysis", "optimization"], "missing_routes": ["sector_impact", "macro_analysis"], "extra_routes": ["quantitative_engine", "risk_analysis", "optimization"]}, "capability_analysis": {"score": 0.0, "coverage": 0.0, "expected_capabilities": ["sector_impact", "macro_data", "causal_analysis"], "demonstrated_capabilities": ["risk_analysis", "statistical_inference", "quantitative_modeling", "optimization"], "missing_capabilities": ["sector_impact", "causal_analysis", "macro_data"], "extra_capabilities": ["quantitative_modeling", "statistical_inference", "risk_analysis", "optimization"]}, "quality_analysis": {"score": 7.25, "confidence": 0.75, "data_quality": "moderate", "analysis_depth": "advanced", "insights_generated": 4}, "performance_analysis": {"score": 10.0, "execution_time": 1.***************, "expected_time": 4.0, "time_efficiency": 2.0}, "response_data": {"response_content": "Comprehensive analysis for macro_impact query", "routes_used": ["data_provider", "quantitative_engine", "risk_analysis", "optimization"], "capabilities_demonstrated": ["quantitative_modeling", "risk_analysis", "optimization", "statistical_inference"], "confidence": 0.75, "data_quality": "moderate", "analysis_depth": "advanced", "processing_method": "AI-enhanced", "data_sources": 9, "computational_complexity": 8, "insights_generated": 4}}, {"query_id": "QUANT_001", "level": 5, "complexity": 9, "success": true, "execution_time": 2.****************, "overall_score": 5.***************, "grade": "C+", "routing_analysis": {"score": 5.***************, "coverage": 0.***************3, "efficiency": 1.****************, "expected_routes": ["portfolio_optimizer", "data_provider", "risk_analysis"], "actual_routes": ["risk_modeling", "ml_models", "simulation", "quantitative_engine", "data_provider"], "missing_routes": ["portfolio_optimizer", "risk_analysis"], "extra_routes": ["ml_models", "risk_modeling", "simulation", "quantitative_engine"]}, "capability_analysis": {"score": 0.0, "coverage": 0.0, "expected_capabilities": ["portfolio_theory", "risk_metrics", "optimization"], "demonstrated_capabilities": ["machine_learning", "multi_factor_analysis", "simulation", "advanced_modeling"], "missing_capabilities": ["portfolio_theory", "risk_metrics", "optimization"], "extra_capabilities": ["machine_learning", "multi_factor_analysis", "simulation", "advanced_modeling"]}, "quality_analysis": {"score": 7.95, "confidence": 0.65, "data_quality": "moderate", "analysis_depth": "expert", "insights_generated": 4}, "performance_analysis": {"score": 10.0, "execution_time": 2.****************, "expected_time": 4.5, "time_efficiency": 2.0}, "response_data": {"response_content": "Comprehensive analysis for portfolio_optimization query", "routes_used": ["data_provider", "ml_models", "quantitative_engine", "risk_modeling", "simulation"], "capabilities_demonstrated": ["machine_learning", "advanced_modeling", "simulation", "multi_factor_analysis"], "confidence": 0.65, "data_quality": "moderate", "analysis_depth": "expert", "processing_method": "AI-enhanced", "data_sources": 10, "computational_complexity": 9, "insights_generated": 4}}, {"query_id": "QUANT_002", "level": 5, "complexity": 9, "success": true, "execution_time": 2.***************, "overall_score": 6.***************, "grade": "B", "routing_analysis": {"score": 5.***************, "coverage": 0.***************3, "efficiency": 1.****************, "expected_routes": ["simulation_engine", "data_provider", "quantitative_modeling"], "actual_routes": ["risk_modeling", "ml_models", "simulation", "quantitative_engine", "data_provider"], "missing_routes": ["simulation_engine", "quantitative_modeling"], "extra_routes": ["ml_models", "risk_modeling", "simulation", "quantitative_engine"]}, "capability_analysis": {"score": 3.***************, "coverage": 0.***************3, "expected_capabilities": ["probability_analysis", "simulation", "quantitative_modeling"], "demonstrated_capabilities": ["machine_learning", "multi_factor_analysis", "simulation", "advanced_modeling"], "missing_capabilities": ["probability_analysis", "quantitative_modeling"], "extra_capabilities": ["machine_learning", "multi_factor_analysis", "advanced_modeling"]}, "quality_analysis": {"score": 7.95, "confidence": 0.65, "data_quality": "moderate", "analysis_depth": "expert", "insights_generated": 4}, "performance_analysis": {"score": 10.0, "execution_time": 2.***************, "expected_time": 4.5, "time_efficiency": 2.0}, "response_data": {"response_content": "Comprehensive analysis for monte_carlo query", "routes_used": ["data_provider", "ml_models", "quantitative_engine", "risk_modeling", "simulation"], "capabilities_demonstrated": ["machine_learning", "advanced_modeling", "simulation", "multi_factor_analysis"], "confidence": 0.65, "data_quality": "moderate", "analysis_depth": "expert", "processing_method": "AI-enhanced", "data_sources": 10, "computational_complexity": 9, "insights_generated": 4}}, {"query_id": "EXPERT_001", "level": 6, "complexity": 10, "success": true, "execution_time": 2.***************, "overall_score": 5.***************, "grade": "C+", "routing_analysis": {"score": 5.***************, "coverage": 0.***************3, "efficiency": 1.****************, "expected_routes": ["cointegration_analysis", "data_provider", "pairs_trading"], "actual_routes": ["risk_modeling", "ml_models", "simulation", "quantitative_engine", "data_provider"], "missing_routes": ["cointegration_analysis", "pairs_trading"], "extra_routes": ["ml_models", "risk_modeling", "simulation", "quantitative_engine"]}, "capability_analysis": {"score": 0.0, "coverage": 0.0, "expected_capabilities": ["cointegration", "strategy_development", "pairs_trading"], "demonstrated_capabilities": ["machine_learning", "multi_factor_analysis", "simulation", "advanced_modeling"], "missing_capabilities": ["cointegration", "strategy_development", "pairs_trading"], "extra_capabilities": ["machine_learning", "multi_factor_analysis", "simulation", "advanced_modeling"]}, "quality_analysis": {"score": 7.95, "confidence": 0.65, "data_quality": "moderate", "analysis_depth": "expert", "insights_generated": 5}, "performance_analysis": {"score": 10.0, "execution_time": 2.***************, "expected_time": 5.0, "time_efficiency": 2.0}, "response_data": {"response_content": "Comprehensive analysis for algorithmic_trading query", "routes_used": ["data_provider", "ml_models", "quantitative_engine", "risk_modeling", "simulation"], "capabilities_demonstrated": ["machine_learning", "advanced_modeling", "simulation", "multi_factor_analysis"], "confidence": 0.65, "data_quality": "moderate", "analysis_depth": "expert", "processing_method": "AI-enhanced", "data_sources": 11, "computational_complexity": 10, "insights_generated": 5}}, {"query_id": "EXPERT_002", "level": 6, "complexity": 10, "success": true, "execution_time": 2.****************, "overall_score": 5.***************, "grade": "C+", "routing_analysis": {"score": 5.***************, "coverage": 0.***************3, "efficiency": 1.****************, "expected_routes": ["garch_modeling", "var_calculation", "data_provider"], "actual_routes": ["risk_modeling", "ml_models", "simulation", "quantitative_engine", "data_provider"], "missing_routes": ["garch_modeling", "var_calculation"], "extra_routes": ["ml_models", "risk_modeling", "simulation", "quantitative_engine"]}, "capability_analysis": {"score": 0.0, "coverage": 0.0, "expected_capabilities": ["garch_modeling", "var_calculation", "risk_management"], "demonstrated_capabilities": ["machine_learning", "multi_factor_analysis", "simulation", "advanced_modeling"], "missing_capabilities": ["garch_modeling", "var_calculation", "risk_management"], "extra_capabilities": ["machine_learning", "multi_factor_analysis", "simulation", "advanced_modeling"]}, "quality_analysis": {"score": 7.95, "confidence": 0.65, "data_quality": "moderate", "analysis_depth": "expert", "insights_generated": 5}, "performance_analysis": {"score": 10.0, "execution_time": 2.****************, "expected_time": 5.0, "time_efficiency": 2.0}, "response_data": {"response_content": "Comprehensive analysis for risk_management query", "routes_used": ["data_provider", "ml_models", "quantitative_engine", "risk_modeling", "simulation"], "capabilities_demonstrated": ["machine_learning", "advanced_modeling", "simulation", "multi_factor_analysis"], "confidence": 0.65, "data_quality": "moderate", "analysis_depth": "expert", "processing_method": "AI-enhanced", "data_sources": 11, "computational_complexity": 10, "insights_generated": 5}}, {"query_id": "STRESS_001", "level": 7, "complexity": 10, "success": true, "execution_time": 2.***************, "overall_score": 5.0725, "grade": "C+", "routing_analysis": {"score": 4.75, "coverage": 0.25, "efficiency": 1.25, "expected_routes": ["multi_factor_modeling", "stress_testing", "data_provider", "geopolitical_analysis"], "actual_routes": ["risk_modeling", "ml_models", "simulation", "quantitative_engine", "data_provider"], "missing_routes": ["geopolitical_analysis", "multi_factor_modeling", "stress_testing"], "extra_routes": ["ml_models", "risk_modeling", "simulation", "quantitative_engine"]}, "capability_analysis": {"score": 0.0, "coverage": 0.0, "expected_capabilities": ["liquidity_modeling", "multi_factor_modeling", "tail_risk", "stress_testing"], "demonstrated_capabilities": ["machine_learning", "multi_factor_analysis", "simulation", "advanced_modeling"], "missing_capabilities": ["multi_factor_modeling", "tail_risk", "liquidity_modeling", "stress_testing"], "extra_capabilities": ["machine_learning", "multi_factor_analysis", "simulation", "advanced_modeling"]}, "quality_analysis": {"score": 7.95, "confidence": 0.65, "data_quality": "moderate", "analysis_depth": "expert", "insights_generated": 5}, "performance_analysis": {"score": 10.0, "execution_time": 2.***************, "expected_time": 5.0, "time_efficiency": 2.0}, "response_data": {"response_content": "Comprehensive analysis for multi_factor_stress query", "routes_used": ["data_provider", "ml_models", "quantitative_engine", "risk_modeling", "simulation"], "capabilities_demonstrated": ["machine_learning", "advanced_modeling", "simulation", "multi_factor_analysis"], "confidence": 0.65, "data_quality": "moderate", "analysis_depth": "expert", "processing_method": "AI-enhanced", "data_sources": 11, "computational_complexity": 10, "insights_generated": 5}}, {"query_id": "STRESS_002", "level": 7, "complexity": 10, "success": true, "execution_time": 2.****************, "overall_score": 5.8225, "grade": "C+", "routing_analysis": {"score": 4.75, "coverage": 0.25, "efficiency": 1.25, "expected_routes": ["credit_analysis", "data_provider", "regime_detection", "ml_modeling"], "actual_routes": ["risk_modeling", "ml_models", "simulation", "quantitative_engine", "data_provider"], "missing_routes": ["credit_analysis", "regime_detection", "ml_modeling"], "extra_routes": ["ml_models", "risk_modeling", "simulation", "quantitative_engine"]}, "capability_analysis": {"score": 2.5, "coverage": 0.25, "expected_capabilities": ["machine_learning", "regime_analysis", "multi_data_integration", "credit_modeling"], "demonstrated_capabilities": ["machine_learning", "multi_factor_analysis", "simulation", "advanced_modeling"], "missing_capabilities": ["multi_data_integration", "credit_modeling", "regime_analysis"], "extra_capabilities": ["multi_factor_analysis", "simulation", "advanced_modeling"]}, "quality_analysis": {"score": 7.95, "confidence": 0.65, "data_quality": "moderate", "analysis_depth": "expert", "insights_generated": 5}, "performance_analysis": {"score": 10.0, "execution_time": 2.****************, "expected_time": 5.0, "time_efficiency": 2.0}, "response_data": {"response_content": "Comprehensive analysis for ml_credit_modeling query", "routes_used": ["data_provider", "ml_models", "quantitative_engine", "risk_modeling", "simulation"], "capabilities_demonstrated": ["machine_learning", "advanced_modeling", "simulation", "multi_factor_analysis"], "confidence": 0.65, "data_quality": "moderate", "analysis_depth": "expert", "processing_method": "AI-enhanced", "data_sources": 11, "computational_complexity": 10, "insights_generated": 5}}]}