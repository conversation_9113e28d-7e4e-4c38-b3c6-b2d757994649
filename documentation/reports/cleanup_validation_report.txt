
🎯 CLEANUP VALIDATION REPORT
============================================================

📊 OVERALL SCORE: 96/100

✅ PASSED VALIDATIONS: 29
❌ FAILED VALIDATIONS: 1
⚠️ WARNINGS: 1

📋 DETAILED RESULTS:
------------------------------

DOCKER VALIDATION:
  ✅ docker/compose/development.yml: exists
  ✅ docker/compose/production.yml: exists
  ✅ docker/compose/development.optimized.yml: exists
  ✅ docker/dockerfiles/services/api.Dockerfile: exists
  ✅ docker/dockerfiles/services/discord-bot.Dockerfile: exists
  ✅ docker/dockerfiles/services/webhook-ingest.Dockerfile: exists
  ✅ docker/dockerfiles/services/tradingview-ingest.Dockerfile: exists
  ✅ docker/README.md: exists
  ✅ docker/compose/development.yml_yaml: valid
  ✅ docker/compose/production.yml_yaml: valid
  ✅ docker/compose/development.optimized.yml_yaml: valid
  ✅ deprecation_notice: present

REQUIREMENTS VALIDATION:
  ✅ requirements/environments/base.txt: exists
  ✅ requirements/environments/development.txt: exists
  ✅ requirements/environments/production.txt: exists
  ✅ requirements/services/api.txt: exists
  ✅ requirements/services/discord-bot.txt: exists
  ✅ requirements/services/webhook-ingest.txt: exists
  ✅ requirements/README.md: exists
  ✅ deprecation_notice: present

CONFIG VALIDATION:
  ✅ config/environments/development.yaml: exists
  ✅ config/environments/production.yaml: exists
  ✅ config/services/ai_models.yaml: exists
  ✅ config/services/api.yaml: exists
  ✅ config/services/discord_bot.yaml: exists
  ✅ config/services/performance_monitoring.conf: exists
  ✅ config/schemas/environment_schema.yaml: exists
  ✅ config/README.md: exists

IMPORT VALIDATION:
  ❌ old_references: 1

SERVICE VALIDATION:
  ✅ docker/compose/development.yml: valid

❌ ERRORS:
---------------
  • Docker Compose syntax error in docker/compose/production.yml

⚠️ WARNINGS:
----------------
  • Old reference in /home/<USER>/Desktop/tradingview-automatio/venv/lib/python3.12/site-packages/pip/_internal/resolution/resolvelib/factory.py: requirements.txt

🎯 RECOMMENDATIONS:
-------------------------
  🎉 Excellent! Cleanup is successful and production-ready.
