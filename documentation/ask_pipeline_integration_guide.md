# ASK Pipeline Integration Guide

## Quick Integration

To integrate the new ASK pipeline with your existing Discord bot, you only need to update the import and call:

### Before (Old System)
```python
from src.bot.pipeline.commands.ask_old_backup.executor import execute_ask_pipeline

# In your Discord command handler
result = await execute_ask_pipeline(
    query=user_query,
    user_id=str(interaction.user.id),
    username=interaction.user.display_name,
    guild_id=str(interaction.guild.id),
    interaction=interaction
)
```

### After (New System)
```python
from src.bot.pipeline.commands.ask import execute_ask_pipeline, format_response_for_discord

# In your Discord command handler
result = await execute_ask_pipeline(
    query=user_query,
    user_id=str(interaction.user.id),
    username=interaction.user.display_name,
    guild_id=str(interaction.guild.id)
)

# Format for Discord
discord_response = format_response_for_discord(result)

# Send response
if isinstance(discord_response, discord.Embed):
    await interaction.followup.send(embed=discord_response)
else:
    await interaction.followup.send(discord_response)
```

## Configuration

### Environment Variables
Set these environment variables for optimal performance:

```bash
# ASK Pipeline Configuration
ASK_INTENT_TIMEOUT=1.0
ASK_TOOLS_TIMEOUT=5.0
ASK_MAX_CONCURRENT_TOOLS=3
ASK_MAX_TOKENS=1000
ASK_TEMPERATURE=0.3
ASK_RESPONSE_TIMEOUT=10.0
ASK_CACHE_ENABLED=true
ASK_CACHE_TTL=600
ASK_LOG_LEVEL=INFO
ASK_METRICS_ENABLED=true
```

### YAML Configuration (Optional)
You can also use the YAML configuration file at `src/bot/pipeline/commands/ask/config.yaml`.

## Monitoring

The new pipeline provides comprehensive logging with correlation IDs:

```python
# Example log output
2025-09-20 22:24:01,299 - src.bot.pipeline.commands.ask.pipeline - INFO - Starting ASK pipeline
2025-09-20 22:24:01,299 - src.bot.pipeline.commands.ask.stages.intent_detector - INFO - Intent detection completed
2025-09-20 22:24:01,299 - src.bot.pipeline.commands.ask.stages.tool_orchestrator - INFO - Tool orchestration completed
2025-09-20 22:24:01,299 - src.bot.pipeline.commands.ask.stages.response_generator - INFO - Response generation completed
2025-09-20 22:24:01,299 - src.bot.pipeline.commands.ask.pipeline - INFO - ASK pipeline completed successfully
```

## Error Handling

The new system provides graceful error handling:

```python
result = await execute_ask_pipeline(query="test")

if result.success:
    # Handle successful response
    print(f"Response: {result.response}")
    print(f"Execution time: {result.execution_time}s")
else:
    # Handle error
    print(f"Error: {result.error}")
    # Fallback response is still provided in result.response
```

## Performance Monitoring

Track pipeline performance:

```python
# Access performance metrics
print(f"Execution time: {result.execution_time}s")
print(f"Intent detected: {result.intent}")
print(f"Tools used: {result.tools_used}")
print(f"Cache hit: {result.cache_hit}")
print(f"Correlation ID: {result.correlation_id}")
```

## Migration Checklist

- [ ] Update import statements to use new pipeline
- [ ] Test with sample queries
- [ ] Configure environment variables
- [ ] Monitor logs for any issues
- [ ] Verify Discord formatting works correctly
- [ ] Check performance metrics
- [ ] Remove old pipeline references (optional)

## Rollback Plan

If you need to rollback to the old system:

1. Restore the old directory:
   ```bash
   mv src/bot/pipeline/commands/ask_old_backup src/bot/pipeline/commands/ask_old
   ```

2. Update imports back to the old system:
   ```python
   from src.bot.pipeline.commands.ask_old.executor import execute_ask_pipeline
   ```

## Support

The new pipeline is fully tested and production-ready. If you encounter any issues:

1. Check the logs for correlation IDs
2. Verify configuration settings
3. Test with the provided test scripts
4. Review the comprehensive documentation

The new system is designed to be a drop-in replacement with improved performance and reliability!
