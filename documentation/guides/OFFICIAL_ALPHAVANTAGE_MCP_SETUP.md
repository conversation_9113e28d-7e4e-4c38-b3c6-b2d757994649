# 🚀 Official Alpha Vantage MCP Server Setup

## 🎯 **What We've Built**

Your trading automation app now has **complete MCP integration** with the **official Alpha Vantage MCP server**! This provides:

- ✅ **Official Alpha Vantage MCP Server** - Hosted at `https://mcp.alphavantage.co/mcp`
- ✅ **50+ Financial Tools** - Stock quotes, technical indicators, forex, crypto, news sentiment
- ✅ **Docker-based MCP Servers** - DuckDuckGo, ArXiv, Fetch, Time, and custom Trading tools
- ✅ **Production Ready** - Proper networking, health checks, and error handling

---

## 🔧 **Quick Setup**

### 1. Get Alpha Vantage API Key

1. Visit [Alpha Vantage API Key Registration](https://www.alphavantage.co/support/#api-key)
2. Sign up for a **free account** (500 calls/day)
3. Copy your API key

### 2. Configure Environment

Add to your `.env` file:

```bash
# Alpha Vantage API Key (Required)
ALPHA_VANTAGE_API_KEY=your_api_key_here

# MCP Server URLs (Auto-configured)
MCP_ALPHAVANTAGE_URL=https://mcp.alphavantage.co/mcp?apikey=${ALPHA_VANTAGE_API_KEY}
MCP_DUCKDUCKGO_URL=http://tradingview-mcp-servers-dev:3001
MCP_ARXIV_URL=http://tradingview-mcp-servers-dev:3002
MCP_FETCH_URL=http://tradingview-mcp-servers-dev:3004
MCP_TIME_URL=http://tradingview-mcp-servers-dev:3005
MCP_TRADING_URL=http://tradingview-mcp-servers-dev:3006
```

### 3. Start the Environment

```bash
# Start all services including MCP servers
docker-compose up -d

# Test the setup
python test_official_alphavantage.py
```

---

## 📊 **Available Alpha Vantage Tools**

Based on the [official documentation](https://mcp.alphavantage.co/?utm_source=chatgpt.com), you have access to:

### **Core Stock APIs**
- `GLOBAL_QUOTE` - Real-time stock quotes
- `TIME_SERIES_DAILY` - Daily historical data
- `TIME_SERIES_INTRADAY` - Intraday data
- `COMPANY_OVERVIEW` - Company fundamentals

### **Technical Indicators**
- `RSI` - Relative Strength Index
- `MACD` - Moving Average Convergence Divergence
- `SMA` - Simple Moving Average
- `EMA` - Exponential Moving Average
- `BBANDS` - Bollinger Bands
- `STOCH` - Stochastic Oscillator

### **Market Intelligence**
- `NEWS_SENTIMENT` - News sentiment analysis
- `EARNINGS_CALENDAR` - Upcoming earnings
- `TOP_GAINERS_LOSERS` - Market movers

### **Forex & Crypto**
- `FX_DAILY` - Foreign exchange rates
- `DIGITAL_CURRENCY_DAILY` - Cryptocurrency data

---

## 🛠️ **Usage Examples**

### **Python MCP Client**

```python
from src.shared.mcp.docker_mcp_client import get_docker_mcp_client, MCPToolCall, MCPServerType

# Get MCP client
client = await get_docker_mcp_client()

# Get stock quote
tool_call = MCPToolCall(
    tool_name="GLOBAL_QUOTE",
    parameters={"symbol": "AAPL"},
    server_type=MCPServerType.ALPHAVANTAGE
)

result = await client.call_tool(tool_call)
if result.success:
    print(f"AAPL Price: {result.data}")
```

### **Technical Analysis**

```python
# Get RSI indicator
tool_call = MCPToolCall(
    tool_name="RSI",
    parameters={
        "symbol": "AAPL",
        "interval": "daily",
        "time_period": 14,
        "series_type": "close"
    },
    server_type=MCPServerType.ALPHAVANTAGE
)

result = await client.call_tool(tool_call)
```

### **News Sentiment**

```python
# Get news sentiment
tool_call = MCPToolCall(
    tool_name="NEWS_SENTIMENT",
    parameters={
        "tickers": "AAPL",
        "topics": "technology",
        "time_from": "20240101T0000",
        "time_to": "20241231T2359",
        "sort": "LATEST",
        "limit": 50
    },
    server_type=MCPServerType.ALPHAVANTAGE
)
```

---

## 🐳 **Docker Architecture**

```
tradingview-automatio/
├── tradingview-mcp-servers-dev (Ports 3001-3006)
│   ├── DuckDuckGo MCP (3001) - Web search
│   ├── ArXiv MCP (3002) - Academic research  
│   ├── Fetch MCP (3004) - Web content
│   ├── Time MCP (3005) - Time utilities
│   └── Trading MCP (3006) - Custom tools
├── tradingview-discord-bot-dev - Discord bot
├── tradingview-api-dev (8000) - Main API
└── tradingview-redis-dev (6379) - Cache
```

**External:**
- `https://mcp.alphavantage.co/mcp` - Official Alpha Vantage MCP Server

---

## 🔍 **Testing & Monitoring**

### **Health Checks**

```bash
# Check all MCP servers
python test_official_alphavantage.py

# Check Docker containers
docker-compose ps

# View logs
docker-compose logs mcp-servers
```

### **Available Endpoints**

- **Alpha Vantage**: `https://mcp.alphavantage.co/mcp?apikey=YOUR_KEY`
- **DuckDuckGo**: `http://localhost:3001/health`
- **ArXiv**: `http://localhost:3002/health`
- **Fetch**: `http://localhost:3004/health`
- **Time**: `http://localhost:3005/health`

---

## 📈 **Rate Limits & Best Practices**

### **Alpha Vantage Limits**
- **Free Tier**: 500 calls/day, 5 calls/minute
- **Premium**: 1200 calls/day, 5 calls/minute
- **Enterprise**: Custom limits

### **Optimization Tips**
1. **Cache Results** - Store frequently accessed data in Redis
2. **Batch Requests** - Combine multiple symbols when possible
3. **Error Handling** - Implement exponential backoff for rate limits
4. **Monitoring** - Track API usage and costs

---

## 🎉 **Success Metrics**

Your MCP setup now provides:

- ✅ **Official Alpha Vantage Integration** - 50+ financial tools
- ✅ **5 Custom MCP Servers** - Web search, research, time utilities
- ✅ **Docker-based Architecture** - Scalable and maintainable
- ✅ **Production Ready** - Health checks, error handling, monitoring
- ✅ **Easy Integration** - Simple Python client for your Discord bot

---

## 🆘 **Troubleshooting**

### **Common Issues**

1. **API Key Not Working**
   ```bash
   # Check if key is set
   echo $ALPHA_VANTAGE_API_KEY
   
   # Test with curl
   curl "https://mcp.alphavantage.co/mcp?apikey=YOUR_KEY"
   ```

2. **MCP Servers Not Starting**
   ```bash
   # Check container logs
   docker-compose logs mcp-servers
   
   # Restart services
   docker-compose restart mcp-servers
   ```

3. **Network Issues**
   ```bash
   # Test connectivity
   docker-compose exec discord-bot curl https://mcp.alphavantage.co/mcp
   ```

---

## 🚀 **Next Steps**

1. **Add Your API Key** to `.env` file
2. **Start the Environment** with `docker-compose up -d`
3. **Test Integration** with `python test_official_alphavantage.py`
4. **Integrate with Discord Bot** using the MCP client
5. **Monitor Usage** and optimize for your needs

Your trading automation app is now **MCP-powered** with official Alpha Vantage integration! 🎉
