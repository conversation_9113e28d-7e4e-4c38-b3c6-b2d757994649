# 🐳 MCP Docker Setup Guide

## 🎯 Overview

Your trading automation app now has **complete MCP (Model Context Protocol) server integration** running in Docker containers! This setup provides:

- **6 MCP Servers** running in isolated containers
- **18+ Tools** available for AI agents and your Discord bot
- **External API Integration** (AlphaVantage, Reddit, ArXiv, etc.)
- **Custom Trading Tools** built specifically for your app
- **Easy Scaling** and management

---

## 🚀 Quick Start

### 1. Start the MCP Environment

```bash
# Start all services including MCP servers
./scripts/start-mcp-environment.sh
```

This will:
- Build the MCP server container
- Start all Docker services
- Check server health
- Show connection information

### 2. Test MCP Integration

```bash
# Test the MCP integration
python scripts/test-mcp-integration.py
```

### 3. View Logs

```bash
# View MCP server logs
docker-compose logs mcp-servers

# View Discord bot logs
docker-compose logs discord-bot

# View all logs
docker-compose logs
```

---

## 🏗️ Architecture

### MCP Server Container

```
tradingview-mcp-servers-dev
├── AlphaVantage MCP (Port 3000)    # Stock market data
├── DuckDuckGo MCP (Port 3001)      # Web search
├── ArXiv MCP (Port 3002)           # Academic research
├── Reddit MCP (Port 3003)          # Social sentiment
├── Fetch MCP (Port 3004)           # Web content
├── Time MCP (Port 3005)            # Time utilities
└── Trading MCP (Port 3006)         # Custom trading tools
```

### Network Configuration

```
tradingview-network (Main app network)
├── api (Port 8000)
├── discord-bot
├── redis
└── webhook-ingest (Port 8001)

mcp-network (MCP servers)
└── mcp-servers (Ports 3000-3006)
```

---

## 🛠️ Available MCP Servers

### 1. AlphaVantage MCP Server
- **Port**: 3000
- **Tools**: Stock prices, company overview, technical indicators
- **API Key**: `ALPHA_VANTAGE_API_KEY` (required)
- **Status**: ✅ Enabled if API key provided

### 2. DuckDuckGo MCP Server
- **Port**: 3001
- **Tools**: Web search, instant answers
- **API Key**: None required
- **Status**: ✅ Always enabled

### 3. ArXiv MCP Server
- **Port**: 3002
- **Tools**: Academic paper search, research
- **API Key**: None required
- **Status**: ✅ Always enabled

### 4. Reddit MCP Server
- **Port**: 3003
- **Tools**: Reddit search, sentiment analysis
- **API Key**: `REDDIT_CLIENT_ID` + `REDDIT_CLIENT_SECRET` (required)
- **Status**: ⚠️ Enabled if credentials provided

### 5. Fetch MCP Server
- **Port**: 3004
- **Tools**: Web content extraction
- **API Key**: None required
- **Status**: ✅ Always enabled

### 6. Time MCP Server
- **Port**: 3005
- **Tools**: Time utilities, market hours
- **API Key**: None required
- **Status**: ✅ Always enabled

### 7. Trading MCP Server (Custom)
- **Port**: 3006
- **Tools**: Custom trading analysis tools
- **API Key**: None required
- **Status**: ✅ Always enabled

---

## 🔧 Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# AlphaVantage API (Required for stock data)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key

# Reddit API (Optional for social sentiment)
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret

# MCP Server URLs (Auto-configured)
MCP_ALPHAVANTAGE_URL=http://tradingview-mcp-servers-dev:3000
MCP_DUCKDUCKGO_URL=http://tradingview-mcp-servers-dev:3001
MCP_ARXIV_URL=http://tradingview-mcp-servers-dev:3002
MCP_REDDIT_URL=http://tradingview-mcp-servers-dev:3003
MCP_FETCH_URL=http://tradingview-mcp-servers-dev:3004
MCP_TIME_URL=http://tradingview-mcp-servers-dev:3005
MCP_TRADING_URL=http://tradingview-mcp-servers-dev:3006
```

### MCP Client Usage

```python
from src.shared.mcp.docker_mcp_client import get_docker_mcp_client, MCPToolCall, MCPServerType

# Get MCP client
client = await get_docker_mcp_client()

# Call a tool
tool_call = MCPToolCall(
    tool_name="get_stock_price",
    parameters={"symbol": "AAPL"},
    server_type=MCPServerType.ALPHAVANTAGE
)

result = await client.call_tool(tool_call)
if result.success:
    print(f"Stock price: {result.data}")
else:
    print(f"Error: {result.error}")
```

---

## 📊 Monitoring & Health Checks

### Health Check Endpoints

```bash
# Check individual servers
curl http://localhost:3000/health  # AlphaVantage
curl http://localhost:3001/health  # DuckDuckGo
curl http://localhost:3002/health  # ArXiv
curl http://localhost:3003/health  # Reddit
curl http://localhost:3004/health  # Fetch
curl http://localhost:3005/health  # Time
curl http://localhost:3006/health  # Trading
```

### Docker Health Checks

```bash
# Check container health
docker-compose ps

# View health check logs
docker inspect tradingview-mcp-servers-dev | grep -A 10 Health
```

### MCP Client Health Check

```python
# Programmatic health check
health = await client.health_check()
print(f"Overall Status: {health['overall_status']}")
print(f"Total Tools: {health['total_tools']}")
```

---

## 🚨 Troubleshooting

### Common Issues

#### 1. MCP Servers Not Starting
```bash
# Check logs
docker-compose logs mcp-servers

# Restart MCP servers
docker-compose restart mcp-servers
```

#### 2. API Key Issues
```bash
# Check environment variables
docker-compose exec mcp-servers env | grep API_KEY

# Verify .env file
cat .env | grep API_KEY
```

#### 3. Network Connectivity
```bash
# Test internal connectivity
docker-compose exec discord-bot curl http://tradingview-mcp-servers-dev:3000/health

# Check network configuration
docker network ls
docker network inspect tradingview-automatio_mcp-network
```

#### 4. Tool Discovery Issues
```bash
# Test tool discovery
python scripts/test-mcp-integration.py

# Check available tools
docker-compose exec mcp-servers curl http://localhost:3000/tools
```

### Debug Commands

```bash
# Enter MCP server container
docker-compose exec mcp-servers bash

# Check running processes
docker-compose exec mcp-servers ps aux

# View server configurations
docker-compose exec mcp-servers cat /app/mcp-configs/*.json
```

---

## 🔄 Development Workflow

### Adding New MCP Servers

1. **Add to Dockerfile**:
   ```dockerfile
   RUN npm install -g @modelcontextprotocol/server-newserver
   ```

2. **Update startup script**:
   ```bash
   # Add to start-mcp-servers.sh
   start_server "newserver" "mcp-server-newserver" 3007
   ```

3. **Update docker-compose.yml**:
   ```yaml
   ports:
     - "3007:3007"  # New server
   ```

4. **Update MCP client config**:
   ```python
   # Add to mcp_client_config.py
   ```

### Modifying Existing Servers

1. **Update configuration** in `docker/mcp-server/mcp-configs/`
2. **Rebuild container**: `docker-compose build mcp-servers`
3. **Restart services**: `docker-compose restart mcp-servers`

---

## 📈 Performance & Scaling

### Resource Usage

```bash
# Monitor resource usage
docker stats tradingview-mcp-servers-dev

# Check memory usage
docker-compose exec mcp-servers free -h
```

### Scaling MCP Servers

```yaml
# Scale specific servers
docker-compose up -d --scale mcp-servers=2
```

### Caching & Optimization

- MCP servers use internal caching
- HTTP client has connection pooling
- Tool results are cached in Redis

---

## 🎉 Success Metrics

Your MCP Docker setup provides:

- ✅ **6 MCP Servers** running in containers
- ✅ **18+ Tools** available for AI agents
- ✅ **External API Integration** (AlphaVantage, Reddit, ArXiv)
- ✅ **Custom Trading Tools** for your Discord bot
- ✅ **Health Monitoring** and error handling
- ✅ **Easy Scaling** and management
- ✅ **Development Workflow** for adding new servers

---

## 🆘 Support

If you encounter issues:

1. Check the logs: `docker-compose logs mcp-servers`
2. Run health checks: `python scripts/test-mcp-integration.py`
3. Verify API keys in `.env` file
4. Check network connectivity between containers

The MCP Docker setup is now **production-ready** and integrated with your trading automation app! 🚀
