TradingView Automation Bot Documentation
========================================

.. toctree::
   :maxdepth: 2
   :caption: Contents:

   installation
   quickstart
   user_guide
   api_reference
   development
   contributing
   changelog

Welcome to the TradingView Automation Bot documentation!

This Discord bot provides AI-powered trading analysis and automation capabilities through an intuitive `/ask` command interface.

Features
--------

* **AI-Powered Analysis**: Get intelligent trading insights using advanced AI models
* **Real-time Data**: Access live market data and technical indicators
* **Smart Caching**: Optimized performance with intelligent caching
* **Security**: Comprehensive input validation and rate limiting
* **Observability**: Full monitoring, logging, and metrics
* **Performance**: Async operations and connection pooling

Quick Start
-----------

1. Install the bot::

   pip install tradingview-automation

2. Configure your environment::

   cp .env.example .env
   # Edit .env with your configuration

3. Run the bot::

   python -m src.bot.main

Architecture
------------

The bot is built with a modular architecture:

* **Core Pipeline**: Main processing pipeline for ask commands
* **Security Layer**: Input validation, rate limiting, and authentication
* **Performance Layer**: Caching, connection pooling, and optimization
* **Observability**: Logging, metrics, and health monitoring
* **Quality Assurance**: Type safety, testing, and code standards

API Reference
-------------

.. toctree::
   :maxdepth: 2

   api_reference

Development
-----------

.. toctree::
   :maxdepth: 2

   development

Contributing
------------

We welcome contributions! Please see our :doc:`contributing` guide for details.

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
