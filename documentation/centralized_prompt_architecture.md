# Centralized Prompt Architecture Design

## Overview

This document outlines the design for a centralized prompt management system that will eliminate duplication, ensure consistency, and provide a single source of truth for all AI prompts in the trading bot system.

## Architecture Principles

### 1. **Single Source of Truth**
- All prompts managed through one centralized system
- No duplicate prompt definitions across the codebase
- Version control and change tracking for all prompts

### 2. **Hierarchical Inheritance**
- Base personas that can be extended for specific use cases
- System prompts that inherit from personas
- Command-specific prompts that extend system prompts

### 3. **Dynamic Context Injection**
- Date/time injection for all prompts
- Market context and real-time data integration
- User context and session state

### 4. **Modular Design**
- Separate modules for different prompt categories
- Easy to extend and maintain
- Clear separation of concerns

## Directory Structure

```
src/core/prompts/
├── __init__.py                 # Main exports and global prompt manager
├── models.py                   # Existing data models (enhanced)
├── prompt_manager.py           # Enhanced central manager
├── base/                       # Base prompt components
│   ├── __init__.py
│   ├── personas.py             # Core AI personalities
│   ├── system_prompts.py       # Base system prompts
│   └── compliance.py           # Risk disclaimers and compliance
├── commands/                   # Command-specific prompts
│   ├── __init__.py
│   ├── ask_prompts.py          # Ask command prompts
│   ├── analyze_prompts.py      # Analyze command prompts
│   └── general_prompts.py      # General command prompts
├── services/                   # AI service prompts
│   ├── __init__.py
│   ├── intent_detection.py     # Intent classification prompts
│   ├── security_analysis.py    # Security threat detection
│   ├── text_parsing.py         # Text parsing and extraction
│   └── anti_hallucination.py   # Anti-fabrication prompts
├── templates/                  # Template files and static content
│   ├── __init__.py
│   ├── system_prompt.txt       # Enhanced base system prompt
│   ├── personas/               # Persona template files
│   │   ├── trading_expert.txt
│   │   ├── risk_analyst.txt
│   │   └── educational_assistant.txt
│   └── compliance/             # Compliance template files
│       ├── standard_disclaimer.txt
│       ├── options_warning.txt
│       └── high_risk_warning.txt
└── utils/                      # Prompt utilities and helpers
    ├── __init__.py
    ├── context_injection.py    # Date/time and context injection
    ├── validation.py           # Prompt validation and testing
    └── formatters.py           # Prompt formatting utilities
```

## Core Components

### 1. Enhanced Prompt Manager

```python
class EnhancedPromptManager:
    """Central manager for all AI prompts with inheritance and context injection"""
    
    def __init__(self):
        self.personas = PersonaManager()
        self.system_prompts = SystemPromptManager()
        self.command_prompts = CommandPromptManager()
        self.service_prompts = ServicePromptManager()
        self.compliance = ComplianceManager()
        self.context_injector = ContextInjector()
    
    def get_prompt(self, prompt_type: str, context: dict = None) -> str:
        """Get a fully contextualized prompt"""
        
    def get_system_prompt(self, persona: str = "trading_expert", context: dict = None) -> str:
        """Get system prompt with persona and context"""
        
    def get_command_prompt(self, command: str, intent: str = None, context: dict = None) -> str:
        """Get command-specific prompt"""
        
    def get_service_prompt(self, service: str, task: str, context: dict = None) -> str:
        """Get AI service prompt"""
```

### 2. Persona System

```python
class PersonaManager:
    """Manages AI personas and personalities"""
    
    PERSONAS = {
        "trading_expert": {
            "name": "Professional Trading Expert",
            "personality": "Analytical, risk-aware, educational",
            "expertise": ["technical analysis", "risk management", "market psychology"],
            "tone": "professional",
            "risk_tolerance": "conservative"
        },
        "risk_analyst": {
            "name": "Risk Management Specialist", 
            "personality": "Cautious, detail-oriented, compliance-focused",
            "expertise": ["risk assessment", "portfolio management", "compliance"],
            "tone": "cautious",
            "risk_tolerance": "very_conservative"
        },
        "educational_assistant": {
            "name": "Trading Education Assistant",
            "personality": "Patient, explanatory, beginner-friendly",
            "expertise": ["trading basics", "market education", "strategy explanation"],
            "tone": "educational",
            "risk_tolerance": "educational"
        }
    }
```

### 3. Context Injection System

```python
class ContextInjector:
    """Handles dynamic context injection into prompts"""
    
    def inject_datetime(self, prompt: str) -> str:
        """Inject current date and time"""
        
    def inject_market_context(self, prompt: str, market_data: dict = None) -> str:
        """Inject current market conditions"""
        
    def inject_user_context(self, prompt: str, user_context: dict = None) -> str:
        """Inject user-specific context"""
        
    def inject_compliance_context(self, prompt: str, risk_level: str = "medium") -> str:
        """Inject appropriate compliance disclaimers"""
```

## Prompt Inheritance Hierarchy

```
Base Persona
    ↓
System Prompt (persona + base instructions)
    ↓
Command Prompt (system + command-specific instructions)
    ↓
Service Prompt (command + service-specific instructions)
    ↓
Final Prompt (service + dynamic context)
```

## Migration Strategy

### Phase 1: Core Infrastructure
1. Create enhanced prompt manager
2. Implement persona system
3. Set up context injection
4. Create base system prompts

### Phase 2: System Prompt Migration
1. Consolidate duplicate system prompts
2. Migrate to persona-based system
3. Update AI client to use centralized prompts
4. Test system prompt functionality

### Phase 3: Command Prompt Migration
1. Migrate ask command prompts
2. Migrate analyze command prompts
3. Update command handlers
4. Test command functionality

### Phase 4: Service Prompt Migration
1. Migrate AI service prompts
2. Update service classes
3. Test service functionality
4. Clean up old prompt files

## Benefits

### 1. **Consistency**
- Single source of truth for AI behavior
- Consistent persona across all interactions
- Standardized compliance and risk disclaimers

### 2. **Maintainability**
- Easy to update prompts globally
- Version control for prompt changes
- Clear organization and structure

### 3. **Flexibility**
- Easy to create new personas
- Dynamic context injection
- Modular prompt composition

### 4. **Quality**
- Centralized testing and validation
- Consistent anti-fabrication rules
- Standardized quality standards

## Implementation Notes

### 1. **Backward Compatibility**
- Maintain existing function signatures during migration
- Gradual migration with fallbacks
- Comprehensive testing at each phase

### 2. **Performance**
- Cache compiled prompts
- Lazy loading of templates
- Efficient context injection

### 3. **Configuration**
- Environment-specific prompt variations
- A/B testing capabilities
- Runtime prompt customization
