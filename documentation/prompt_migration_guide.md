# Prompt Migration Guide

## Overview

This guide explains how to migrate from scattered prompt definitions to the new centralized prompt management system.

## Migration Summary

### Before (Scattered Prompts)
```python
# In src/shared/ai_chat/ai_client.py
def get_system_prompt():
    # Basic system prompt with date injection
    
# In src/bot/pipeline/commands/ask/stages/prompts.py  
def get_system_prompt():
    # Comprehensive ask command prompt
    
# In various AI service files
self.ai_prompts = {
    "SYMBOL_EXTRACTION": "hardcoded prompt...",
    # More hardcoded prompts
}
```

### After (Centralized Prompts)
```python
# Single import for all prompt needs
from src.core.prompts import (
    get_system_prompt,
    get_fallback_responses,
    get_compliance_disclaimer,
    prompt_manager
)

# Unified interface
system_prompt = get_system_prompt(persona="trading_expert", command="ask")
fallbacks = get_fallback_responses(command="ask")
```

## Step-by-Step Migration

### 1. Update Import Statements

**Old:**
```python
from src.bot.pipeline.commands.ask.stages.prompts import get_system_prompt
```

**New:**
```python
from src.core.prompts import get_system_prompt
```

### 2. Update Function Calls

**Old:**
```python
# Different functions with different signatures
system_prompt = get_system_prompt()  # From ai_client.py
ask_prompt = get_system_prompt()     # From ask/prompts.py
```

**New:**
```python
# Unified function with clear parameters
general_prompt = get_system_prompt(persona="trading_expert")
ask_prompt = get_system_prompt(persona="trading_expert", command="ask")
json_prompt = get_system_prompt(persona="trading_expert", include_json_format=True)
```

### 3. Migrate AI Service Prompts

**Old:**
```python
class IntelligentTextParser:
    def __init__(self):
        self.ai_prompts = {
            "SYMBOL_EXTRACTION": "Extract symbols...",
            "PRICE_EXTRACTION": "Extract prices..."
        }
```

**New:**
```python
from src.core.prompts.services.text_parsing import TextParsingPrompts

class IntelligentTextParser:
    def __init__(self):
        self.text_prompts = TextParsingPrompts()
    
    def extract_symbols(self, text):
        prompt = self.text_prompts.get_symbol_extraction_prompt(text)
```

### 4. Update Context Injection

**Old:**
```python
def get_system_prompt():
    from datetime import datetime
    current_date = datetime.now().strftime("%B %d, %Y")
    return f"System prompt with date: {current_date}"
```

**New:**
```python
from src.core.prompts import get_system_prompt, inject_context

# Automatic date injection
prompt = get_system_prompt(persona="trading_expert")

# Or with additional context
context = {
    'market_data': {'sentiment': 'bullish'},
    'user_context': {'experience_level': 'intermediate'}
}
enhanced_prompt = get_system_prompt(persona="trading_expert", context=context)
```

## File-by-File Migration Plan

### High Priority Files

#### 1. `src/shared/ai_chat/ai_client.py`
- **Current**: Has its own `get_system_prompt()` function
- **Action**: Replace with centralized function
- **Impact**: Used by general AI client calls

```python
# OLD
def get_system_prompt():
    # Local implementation
    
# NEW  
from src.core.prompts import get_system_prompt

# In AIClientWrapper methods:
system_content = get_system_prompt(persona="trading_expert", include_json_format=use_json_system_prompt)
```

#### 2. `src/bot/pipeline/commands/ask/stages/prompts.py`
- **Current**: Comprehensive ask command prompts
- **Action**: Migrate to centralized ask command prompts
- **Impact**: Used by ask command pipeline

```python
# OLD
def get_system_prompt():
    # Ask-specific implementation
    
def get_fallback_responses():
    # Ask-specific fallbacks

# NEW
from src.core.prompts import get_system_prompt, get_fallback_responses

# Usage remains the same, but now uses centralized system
system_prompt = get_system_prompt(command="ask")
fallbacks = get_fallback_responses(command="ask")
```

### Medium Priority Files

#### 3. AI Service Files
- `src/shared/ai_services/enhanced_intent_detector.py`
- `src/shared/ai_services/intelligent_text_parser.py`
- `src/shared/ai_services/ai_security_detector.py`

**Migration Pattern:**
```python
# OLD
class SomeAIService:
    def __init__(self):
        self.prompt_template = "hardcoded prompt..."

# NEW
from src.core.prompts.services.intent_detection import IntentDetectionPrompts

class SomeAIService:
    def __init__(self):
        self.prompts = IntentDetectionPrompts()
    
    def some_method(self, query):
        prompt = self.prompts.get_intent_classification_prompt(query)
```

## Testing Migration

### 1. Create Migration Test
```python
# test_prompt_migration.py
import pytest
from src.core.prompts import get_system_prompt, get_fallback_responses

def test_unified_system_prompt():
    """Test that unified system prompt works"""
    prompt = get_system_prompt(persona="trading_expert")
    assert "trading" in prompt.lower()
    assert "educational" in prompt.lower()
    assert len(prompt) > 100

def test_ask_command_prompts():
    """Test ask command specific prompts"""
    prompt = get_system_prompt(command="ask")
    assert "JSON" in prompt  # Should include JSON formatting
    
def test_fallback_responses():
    """Test fallback responses"""
    fallbacks = get_fallback_responses(command="ask")
    assert "ai_error" in fallbacks
    assert "2025" in fallbacks["ai_error"]  # Should have current date

def test_backward_compatibility():
    """Test that old imports still work during migration"""
    # This ensures gradual migration is possible
    pass
```

### 2. Run Migration Tests
```bash
python -m pytest test_prompt_migration.py -v
```

## Rollback Plan

If issues arise during migration:

1. **Immediate Rollback**: The centralized system includes fallback compatibility
2. **Gradual Rollback**: Revert individual file imports one by one
3. **Full Rollback**: Disable centralized components in `__init__.py`

## Benefits After Migration

### 1. **Consistency**
- Single source of truth for AI behavior
- Consistent persona across all interactions
- Standardized compliance and disclaimers

### 2. **Maintainability**
- Update prompts in one place
- Version control for prompt changes
- Easy A/B testing of prompt variations

### 3. **Quality**
- Centralized validation and testing
- Consistent anti-fabrication rules
- Standardized quality standards

### 4. **Flexibility**
- Easy persona switching
- Dynamic context injection
- Modular prompt composition

## Migration Checklist

- [ ] Update `src/shared/ai_chat/ai_client.py`
- [ ] Update `src/bot/pipeline/commands/ask/stages/prompts.py`
- [ ] Update AI service files
- [ ] Test all command functionality
- [ ] Verify fallback responses work
- [ ] Check compliance disclaimers
- [ ] Test persona switching
- [ ] Validate context injection
- [ ] Run full integration tests
- [ ] Update documentation
- [ ] Clean up old prompt files

## Support

If you encounter issues during migration:

1. Check the fallback compatibility functions
2. Verify import statements are correct
3. Test with simple cases first
4. Use the migration test suite
5. Refer to the centralized prompt documentation
