# ASK Pipeline - New Simplified Architecture

## Overview

This document outlines the new simplified architecture for the ASK pipeline, replacing the current 824-line over-engineered system with a clean, maintainable, and performant solution.

## Design Principles

1. **Simplicity First**: Every component should be easy to understand and maintain
2. **Performance Focused**: Optimize for speed and efficiency (< 2 seconds response time)
3. **Error Resilient**: Graceful handling of all failure modes
4. **Testable**: Every component should be easily testable
5. **Monitorable**: Comprehensive logging and metrics
6. **Configurable**: Easy to modify behavior without code changes

## New Architecture Flow

```
User Query → Intent Detection → Tool Orchestration → Response Generation → Discord Response
     ↓              ↓                    ↓                    ↓                ↓
  Sanitize    Casual/Data?        MCP Tools         AI Synthesis      Format & Send
   Input      (AI Call)          (Parallel)        (Single Call)      (Embed/Text)
     ↓              ↓                    ↓                    ↓                ↓
  Security    Cache Check        Rate Limit        Add Disclaimer     Log & Monitor
   Check      (Fast Path)        Circuit Break     Validate Result    Update Cache
```

## Directory Structure

```
src/bot/pipeline/commands/ask/
├── __init__.py
├── pipeline.py              # Main pipeline controller (< 100 lines)
├── config.py                # Configuration management
├── executor.py              # Discord integration
├── stages/
│   ├── __init__.py
│   ├── intent_detector.py   # Quick intent classification
│   ├── tool_orchestrator.py # MCP tool management
│   ├── response_generator.py # AI response synthesis
│   └── formatter.py         # Discord formatting
├── tools/
│   ├── __init__.py
│   ├── mcp_client.py        # MCP tool client
│   ├── cache_manager.py     # Intelligent caching
│   └── fallback_handler.py  # Error fallbacks
└── tests/
    ├── __init__.py
    ├── test_pipeline.py
    ├── test_tools.py
    └── test_integration.py
```

## Core Components

### 1. Pipeline Controller (`pipeline.py`)
- **Single entry point** for all ask requests
- **Linear flow** with clear stage progression
- **Error handling** with fallback chain
- **Performance monitoring** with timing metrics
- **< 100 lines** total

### 2. Intent Detector (`stages/intent_detector.py`)
- **Single AI call** for intent classification
- **Binary decision**: Casual vs Data Needed
- **< 1 second** response time
- **Caching** for common intents
- **Confidence scoring**

### 3. Tool Orchestrator (`stages/tool_orchestrator.py`)
- **Parallel MCP tool execution**
- **Dynamic tool selection** based on intent
- **Rate limiting** and circuit breakers
- **Result aggregation** and validation
- **Tool failure handling**

### 4. Response Generator (`stages/response_generator.py`)
- **Single AI call** for response synthesis
- **Context-aware prompting** using centralized system
- **Tool result integration**
- **Response validation**
- **Disclaimer injection**

### 5. Discord Formatter (`stages/formatter.py`)
- **Embed creation** with proper styling
- **Character limit handling**
- **Markdown formatting**
- **Error message formatting**
- **Success indicators**

## Key Improvements Over Current System

### Performance Optimizations
- **Parallel tool calls** vs sequential data collection
- **Cache-first approach** vs complex caching layers
- **Single AI call per stage** vs multiple AI analysis stages
- **Simplified decision tree** vs complex routing logic

### Reduced Complexity
- **~500 lines total** vs 824 lines in pipeline.py alone
- **5 core files** vs 50+ files in current system
- **Clear separation** vs nested dependencies
- **Single responsibility** per component

### Better Error Handling
- **Clear fallback chain** vs complex error handling
- **Tool failure isolation** vs service-level failures
- **User-friendly messages** vs technical errors
- **Graceful degradation** vs hard failures

## Configuration Schema

```yaml
ask_pipeline:
  intent_detection:
    model: "fast_model"
    timeout: 1.0
    cache_ttl: 300
  
  tools:
    mcp_enabled: true
    parallel_execution: true
    timeout: 5.0
    max_concurrent: 3
  
  response:
    model: "quality_model"
    max_tokens: 1000
    temperature: 0.3
  
  cache:
    enabled: true
    ttl: 600
    max_size: 1000
  
  monitoring:
    log_level: "INFO"
    metrics_enabled: true
    correlation_tracking: true
```

## Success Metrics

- **Performance**: < 2 seconds average response time
- **Reliability**: > 99% success rate
- **Maintainability**: < 100 lines per file
- **Testability**: > 90% test coverage
- **User Experience**: Clear, helpful responses

## Migration Strategy

1. **Phase 1**: Create new architecture alongside current system
2. **Phase 2**: Implement core components with tests
3. **Phase 3**: Gradual migration with A/B testing
4. **Phase 4**: Remove old system after validation
5. **Phase 5**: Performance optimization and monitoring

## Fallback Chain

1. **Primary**: MCP Tools with real data
2. **Secondary**: Local AI with cached data
3. **Tertiary**: Static educational responses
4. **Final**: User-friendly error messages

## Monitoring & Observability

- **Correlation ID tracking** for request tracing
- **Performance metrics** for each stage
- **Error rate monitoring** with alerting
- **Cache hit rate tracking**
- **Tool usage statistics**
- **User satisfaction metrics**

This new architecture will provide a clean, maintainable, and performant foundation for the ASK pipeline while preserving all essential functionality.
