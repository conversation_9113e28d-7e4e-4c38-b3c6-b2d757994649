# Analysis Self-Evaluation System

## Overview
This system provides a modular, extensible framework for generating trading analysis with built-in self-evaluation and validation. It addresses the requirement to analyze its own answers and provide grades for reliability.

## Key Components

### 1. Template System (`src/analysis/templates/analysis_response_template.py`)
- **Modular Design**: Abstract base classes for evaluators and response templates
- **Extensible**: Easy to add new analysis types
- **Standardized Format**: Consistent response structure

### 2. Analysis Result Structure
- Standardized data structure for all analysis types
- Contains all necessary information for evaluation and response generation

### 3. Evaluation Framework
- **Confidence Scoring**: Numerical confidence rating (0-100)
- **Reliability Grading**: Letter grades (A-D) with descriptions
- **Risk Assessment**: Risk level categorization
- **Validation Points**: Positive aspects of the analysis
- **Potential Issues**: Areas of concern
- **Recommendations**: Actionable advice based on the evaluation

### 4. Response Formatting
- Professional, user-friendly output
- Clear organization of information
- Risk disclosure statements
- Technical context information

## Integration with /ask Command

The system is designed to be easily integrated into your `/ask` command:

1. Parse user query to determine analysis type
2. Perform the requested analysis
3. Package results into `AnalysisResult` object
4. Use `AnalysisTemplateFactory` to get appropriate evaluator and template
5. Evaluate the analysis
6. Format response using template
7. Return formatted response to user

## Example Output

```
🤖 ANALYSIS SUMMARY & SELF-EVALUATION
==================================================

🎯 NVDA PRICE TARGETS:
  Current Price: $177.82
  Conservative: $182.05 (****%)
  Moderate: $188.39 (****%)
  Aggressive: $198.96 (+11.9%)
  Stop_loss: $173.59 (-2.4%)

📊 ANALYSIS EVALUATION:
  Confidence Score: 50/100
  Reliability Grade: C (Moderately Reliable)
  Risk Assessment: Higher Risk

✅ VALIDATION POINTS:
  • Targets align with technical indicators
  • Reasonable risk/reward ratio

⚠️  POTENTIAL ISSUES:
  • Weak trend detected

💡 RECOMMENDATIONS:
  • Monitor key support levels
  • Use stop loss to manage risk
  • Wait for trend confirmation

🔍 TECHNICAL CONTEXT:
  Trend Direction: sideways
  Trend Strength: 0.38
  Volatility: 4.72%

📊 SUPPORT LEVELS:
  • $83.13
  • $122.86
  • $126.46

📊 RESISTANCE LEVELS:
  • $209.23
  • $209.25
  • $211.24

==================================================
⚠️  DISCLOSURE: This analysis is algorithmically generated and should not be
    considered financial advice. Always validate with additional research
    and consider your risk tolerance before making investment decisions.
```

## Extensibility

The system is designed to be easily extended for new analysis types:

1. Create a new evaluator class that inherits from `AnalysisEvaluator`
2. Create a new response template class that inherits from `ResponseTemplate`
3. Register the new types in the `AnalysisTemplateFactory`
4. Add the new analysis type to the query parser in your `/ask` command

## Benefits

1. **Self-Evaluation**: Automatically analyzes and grades its own results
2. **Transparency**: Clearly shows confidence levels and potential issues
3. **Risk Awareness**: Provides risk assessments and recommendations
4. **Modularity**: Easy to modify and extend
5. **Professional Format**: Clean, organized output that's easy to understand
6. **Consistency**: Standardized response format across all analysis types