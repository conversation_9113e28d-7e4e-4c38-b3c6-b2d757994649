# Production Deployment Guide

This guide provides step-by-step instructions for deploying the TradingView Automation system in a production environment.

## Prerequisites

- Docker Engine 20.10.0 or later
- Docker Compose 2.0.0 or later (for Docker Compose deployment)
- Docker Swarm initialized (for Swarm deployment)
- SSL certificates for HTTPS (recommended for production)
- Access to required API keys:
  - Polygon.io
  - Finnhub
  - Alpha Vantage
  - Alpaca (optional)
  - Discord (optional)
  - OpenRouter (optional)

## Deployment Options

There are two recommended deployment options:

1. **Docker Compose**: Simpler setup, suitable for single-host deployments
2. **Docker Swarm**: More robust, supports scaling and high availability

## Step 1: Generate Secrets

We provide a script to securely generate all required secrets:

```bash
# Make the script executable
chmod +x scripts/generate_docker_secrets.sh

# Run the script
./scripts/generate_docker_secrets.sh
```

This script will:
- Generate secure random passwords for database and Redis
- Generate a secure JWT secret
- Prompt for API keys
- Create Docker secret files in the `./secrets` directory
- Generate a `.env.production` file with non-sensitive configuration

## Step 2: Configure SSL (Recommended)

For production deployments, HTTPS is strongly recommended:

1. Place your SSL certificate and private key in the `./nginx/ssl` directory:
   ```bash
   mkdir -p ./nginx/ssl
   cp your-cert.crt ./nginx/ssl/cert.crt
   cp your-key.key ./nginx/ssl/private.key
   ```

2. Update the Nginx configuration in `./nginx/nginx.conf` to use your domain name.

## Step 3: Configure Environment

Review and adjust the generated `.env.production` file as needed:

```bash
# Edit the environment file
nano .env.production
```

Key settings to review:
- `CORS_ORIGINS`: Update with your frontend domain(s)
- Provider settings: Disable any providers you're not using

## Step 4: Deploy the Application

### Option A: Docker Compose Deployment

```bash
# Deploy with Docker Compose
docker-compose --env-file .env.production -f docker-compose.prod.yml up -d
```

### Option B: Docker Swarm Deployment

```bash
# Initialize Docker Swarm if not already done
docker swarm init

# Create Docker secrets
for file in ./secrets/*; do
  docker secret create $(basename $file) $file
done

# Deploy the stack
docker stack deploy -c docker-compose.prod.yml tradingview
```

## Step 5: Verify Deployment

1. Check that all services are running:

   ```bash
   # For Docker Compose
   docker-compose -f docker-compose.prod.yml ps
   
   # For Docker Swarm
   docker stack ps tradingview
   ```

2. Check the logs for any errors:

   ```bash
   # For Docker Compose
   docker-compose -f docker-compose.prod.yml logs -f
   
   # For Docker Swarm
   docker service logs tradingview_app
   ```

3. Verify the API is accessible:

   ```bash
   curl http://localhost:8000/health
   ```

   You should see a response like:
   ```json
   {"status": "healthy", "timestamp": "2025-01-01T00:00:00Z"}
   ```

## Step 6: Security Hardening

For production deployments, consider these additional security measures:

1. **Firewall Configuration**: Restrict access to only necessary ports (80, 443)
2. **Rate Limiting**: Configure rate limiting at the Nginx level
3. **Regular Updates**: Keep all components updated with security patches
4. **Monitoring**: Set up monitoring and alerting for the system
5. **Backups**: Configure regular database backups

## Step 7: Maintenance

### Updating the Application

```bash
# Pull the latest code
git pull

# Rebuild and redeploy
docker-compose --env-file .env.production -f docker-compose.prod.yml up -d --build
```

### Backing Up the Database

```bash
# Create a backup directory
mkdir -p ./backups

# Backup the database
docker exec tradingview-postgres-prod pg_dump -U tradingview_user tradingview_automation > ./backups/db_backup_$(date +%Y%m%d).sql
```

### Rotating Secrets

It's a good practice to periodically rotate secrets:

1. Generate new secrets using the script
2. Update the Docker secrets (for Swarm) or environment file (for Compose)
3. Redeploy the application

## Troubleshooting

### Common Issues

1. **Database Connection Errors**:
   - Check that the database credentials are correct
   - Verify that the database container is running
   - Check the database logs for errors

2. **API Key Errors**:
   - Verify that all required API keys are set
   - Check that the API keys have the necessary permissions
   - Look for rate limiting issues in the logs

3. **Network Issues**:
   - Check that all containers are on the same network
   - Verify that port mappings are correct
   - Check for firewall or network policy restrictions

### Getting Help

If you encounter issues:

1. Check the logs for error messages
2. Review the documentation
3. Open an issue on the GitHub repository with detailed information about the problem

## Security Considerations

1. **Secrets Management**: Never commit secrets to version control
2. **Regular Updates**: Keep all components updated with security patches
3. **Access Control**: Limit access to the production environment
4. **Monitoring**: Monitor for unusual activity or performance issues
5. **Backups**: Regularly backup data and verify the backups

## Conclusion

Following these steps will help you deploy the TradingView Automation system securely in a production environment. Remember to regularly review and update your deployment to maintain security and performance. 