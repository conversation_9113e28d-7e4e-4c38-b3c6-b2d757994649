# Test Files Audit

## Project Tests
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_different_questions.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_bot_real_data.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_real_ai_system.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tradingview-ingest/test_crypto_simple.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tradingview-ingest/test_webhook.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_docker_processor.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_robust_processor.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_hybrid_ai.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_simple_ai.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_core_pipeline.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_discord_ask.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_real_system.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_hallucinated_response.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_enhanced_hybrid_consolidated.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_professional_standards.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_fixed_validation.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_answer_quality.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_ai_answer_quality_final.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_real_ai_responses.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_enhanced_hybrid_approach.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_hybrid_approach.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_ai_controlled_analysis.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_zero_hallucination.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_enhanced_validation.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_architectural_improvements.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_ai_data_constraint.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_data_binding_fix.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/scripts/test_ai_memory.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/scripts/test_fallback_remediation.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/scripts/test_full_analysis.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/src/bot/pipeline/commands/ask/stages/test_ai_models_config.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/src/bot/pipeline/commands/ask/stages/test_infrastructure.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/src/bot/pipeline/commands/ask/test_modular_system.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/src/bot/pipeline/test_pipeline.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/src/shared/technical_analysis/test_indicators.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_ai_vs_regex.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_ast_parsing.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_consolidated_architecture.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_dashboard.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/test_monte_carlo_demo.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/e2e/test_bot_commands.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/integration/test_alpha_vantage_provider.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/integration/test_cross_platform_context.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/integration/test_enhanced_ai_context.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/integration/test_market_data_service.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/integration/test_polygon_provider.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/integration/test_qqq_options_estimation.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/integration/test_supabase_integration.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/integration/test_supertrend_analysis.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/load/test_bot_load.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_advanced_security.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_ai_automation.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_ai_chat_processor.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_ai_debugger_demo.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_ai_pipeline.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_ai_response.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_ai_routing_service_fix.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_alpaca_data.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_alpha_vantage_config.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_analysis.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_analyze_pipeline.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_api_endpoints.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_ask_command.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_async_database.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_audit_visualization.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_automation.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_backward_compatibility.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_batch_processing.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_bot_pipeline_system.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_cache_metrics.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_cache_warming.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_comprehensive_pipeline.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_comprehensive_symbol_extraction.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_comprehensive.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_config_integration.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_config.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_consolidated_providers.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_contextual_logger.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_core_system_components.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_correlation_integration.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_correlation_standalone.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_correlation_wrappers.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_critical_fixes.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_critical_pipeline_fixes.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_current_credentials.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_data_gap_detection.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_data_provider_integration_fix.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_data_provider_system.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_data_provider.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_data_providers.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_data_quality_scoring.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_database_connection.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_db_manager.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_debug_logging.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_discord_integration.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_discord_interaction.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_discord_optimizations.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_enhanced_analysis_mock.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_enhanced_analysis.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_enhanced_engines_only.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_enhanced_indicators.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_enhanced_stage_only.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_fallback_remediation.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_feedback_mechanism.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_final_enhancements.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_finnhub_provider.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_fixed_pipeline.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_fixes.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_full_analysis.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_imports.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_integrated_analysis.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_live_data.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_local_debugger_demo.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_market_api.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_market_calendar.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_market_hours_fix.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_market_hours.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_message_length_enforcement.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_metrics_api.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_mock_fix.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_multi_symbol_integration.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_multi_timeframe.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_new_credentials.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_optimizations.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_options_greeks.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_outlier_detection.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_performance_optimization.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_pipeline_completion_focused.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_pipeline_completion_issues.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_pipeline_monitoring.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_pipeline_optimization.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_pipeline_visualization.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_probability_engine_components.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_probability_response_service.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_production_deployment.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_prompt_system.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_provider_attribution.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_provider_status.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_quick_enhancements.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_real_data_providers.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_real_data_quality.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_recommendation_engine.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_redis_docker.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_redis_manager.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_refactored_bot.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_report_engine_mock.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_response_audit.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_response_depth_fix.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_response_generator.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_response_template_fix.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_show_full_report.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_simple_audit.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_simple_correlation.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_specific_symbol.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_stale_data_detection.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_supabase_connection.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_supply_demand_zones.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_suspicious_data_detection.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_symbol_extraction_fix.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_technical_analysis_integration.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_technical_indicators.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_template_format_fix_comprehensive.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_unified_symbol_extraction.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_uniform_alerts.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_volume_analyzer.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_webhook_integration.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_webhook_unique.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_webhook.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/test_zone_integration_real_data.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/unit/test_ml_sentiment_analyzer.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/unit/test_options_greeks_calculator.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tests/unit/test_strategy_calculator.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tradingview-ingest/test_high_volume.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tradingview-ingest/test_multi_ticker.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tradingview-ingest/test_uniform_alerts.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tradingview-ingest/tests/test_config.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tradingview-ingest/tests/test_external_integrations.py
- [ ] /home/<USER>/Desktop/tradingview-automatio/tradingview-ingest/tests/test_no_legacy_data_processing.py

## Third-party Tests (from venv and pylint_env)
- These tests are part of the installed packages and can likely be ignored for the purpose of auditing the project's test suite.
