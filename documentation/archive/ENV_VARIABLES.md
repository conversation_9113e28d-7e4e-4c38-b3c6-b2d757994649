# Environment Variables Management in Docker

## Issue: Docker Container Environment Variables Overridden by Shell Variables

The environment variables in Docker containers were being overridden by shell environment variables, causing API keys and database connections to use placeholder values instead of the actual values from the `.env` file.

### Affected Variables

1. **OPENROUTER_API_KEY**
   - Docker Container: `placeholder_key`
   - .env File: `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`
   - Impact: AI features not working

2. **ALPACA_API_KEY**
   - Docker Container: `placeholder_key`
   - .env File: `DEMO_ALPACA_KEY`
   - Impact: Alpaca data provider functionality affected

3. **POLYGON_API_KEY**
   - Docker Container: `placeholder_key`
   - .env File: `DEMO_POLYGON_KEY`
   - Impact: Polygon data provider functionality affected

4. **FINNHUB_API_KEY**
   - Docker Container: `your_finnhub_key_here`
   - .env File: `DEMO_FINNHUB_KEY`
   - Impact: Finnhub data provider functionality affected

5. **DATABASE_URL**
   - Docker Container: Uses `db.sgxjackuhalscowqrulv.supabase.co` subdomain
   - .env File: Uses `db.xftqkryqonjsfdyxdrri.supabase.co` subdomain
   - Impact: Database connection problems

## Solutions Implemented

### 1. Immediate Fix Script

A script (`fix_env_variables.sh`) has been created to:
- Unset problematic environment variables in the shell
- Restart Docker containers with explicit reference to the `.env` file
- Verify that the environment variables are correctly set

Run the script with:

```bash
./fix_env_variables.sh
```

### 2. Long-term Fix

The Docker Compose files have been updated to explicitly use the `.env` file:

- Added `env_file: .env` to each service in the main `docker/compose/development.yml`
- Added `env_file: ../../../.env` to each service in `docker/compose/services/tradingview-ingest.yml`

### Best Practices for Environment Variables in Docker

1. **Always Use `env_file` Directive**
   - Explicitly specify the `.env` file in Docker Compose files

2. **Check for Shell Environment Variables**
   - Use `printenv | grep VARIABLE_NAME` to check for variables that might override Docker settings

3. **Use Docker Secrets for Production**
   - For production deployments, consider using Docker Secrets instead of environment variables for sensitive data

4. **Verify Environment Variables in Containers**
   - After starting containers, verify that variables are set correctly with:
     ```bash
     docker exec CONTAINER_NAME printenv VARIABLE_NAME
     ```

5. **Keep `.env` File Secure**
   - Ensure the `.env` file is in `.gitignore`
   - Limit access to the `.env` file to authorized personnel only 