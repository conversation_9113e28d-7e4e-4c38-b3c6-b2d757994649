# Discord Bot Container Requirements

## Overview
The Discord Bot container processes commands and sends notifications to Discord channels. It serves as the primary user interface for interacting with the TradingView Automation system.

## Container Configuration
- **Service Name**: discord-bot
- **Container Name**: tradingview-discord-bot-dev
- **Base Image**: Custom Python 3.12-slim image
- **Command**: `python -m src.bot.main`

## Environment Variables
- ENVIRONMENT=development
- PYTHONPATH=/app
- LOG_LEVEL=INFO
- REDIS_URL=redis://${REDIS_PASSWORD}@redis:6379/0
- DISCORD_BOT_TOKEN=${DISCORD_BOT_TOKEN}
- DISCORD_GUILD_ID=${DISCORD_GUILD_ID:-}
- USE_SUPABASE=true
- SUPABASE_URL=${SUPABASE_URL}
- SUPABASE_KEY=${SUPABASE_KEY}
- DATABASE_URL=${DATABASE_URL}
- SUPABASE_FALLBACK_IP=${SUPABASE_FALLBACK_IP}
- OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
- POLYGON_API_KEY=${POLYGON_API_KEY}
- FINNHUB_API_KEY=${FINNHUB_API_KEY}
- ALPACA_API_KEY=${ALPACA_API_KEY}
- JWT_SECRET=${JWT_SECRET}
- REDIS_PASSWORD=${REDIS_PASSWORD}

## Dependencies
All dependencies are defined in the main requirements/environments/production.txt file:
- discord.py >= 2.3.0
- discord-py-interactions >= 5.0.0
- OpenAI >= 1.0.0
- Anthropic >= 0.3.0
- Google-generativeai >= 0.3.0
- Transformers >= 4.30.0
- Torch >= 2.0.0
- Sentence-transformers >= 2.2.0
- Pandas >= 2.0.0
- NumPy >= 1.24.0
- TA >= 0.10.0
- YFinance >= 0.2.0
- Alpha-vantage >= 2.3.0
- SQLAlchemy >= 2.0.0
- Supabase >= 2.0.0
- Redis >= 4.5.0
- Structlog >= 23.0.0
- Python-dotenv >= 1.0.0
- PyYAML >= 6.0
- Tenacity >= 8.2.0

## Volumes
- ./src:/app/src (Source code)
- ./config:/app/config:ro (Configuration files - read-only)
- ./logs:/app/logs (Log files)
- ./data:/app/data (Data files)
- ./tests:/app/tests (Test files)

## Networks
- tradingview-network (Main network)
- external-network (External communication)

## Health Check
- Test: `python -c "import asyncio; from src.bot.client import TradingBot; print('Health check passed')"`
- Interval: 30s
- Timeout: 10s
- Retries: 3
- Start period: 60s

## Key Responsibilities
1. Process Discord commands from users
2. Send notifications and trading signals to Discord channels
3. Execute pipeline architecture for processing requests
4. Interface with Redis for caching and messaging
5. Communicate with database for data persistence
6. Integrate with AI services (OpenAI, Anthropic, Google)
7. Fetch market data from external providers (Polygon, Finnhub, Alpaca, YFinance)
8. Perform technical analysis using TA libraries
9. Handle user authentication and authorization

## Required Services
- Redis (caching and messaging)
- Database (Supabase/PostgreSQL)
- External APIs (Polygon, Finnhub, Alpaca, YFinance, Alpha Vantage)

## Security Considerations
- Discord bot token management
- JWT-based authentication
- Secure communication with Redis using password authentication
- API key management for external services
- Environment variables for sensitive configuration

## DNS Configuration
- Uses Google DNS (*******) and Cloudflare DNS (*******)
- Extra hosts mapping for host.docker.internal