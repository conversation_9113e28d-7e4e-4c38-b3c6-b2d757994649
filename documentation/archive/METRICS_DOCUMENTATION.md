# Metrics Documentation

This document provides comprehensive documentation of all metrics used across the trading automation system.

## Overview

The system currently uses multiple metrics implementations that are being consolidated into a unified approach:

1. **Custom Metrics Service** (`src/shared/metrics/metrics_service.py`) - Internal metrics storage
2. **Prometheus-based Metrics** (`src/api/data/metrics.py`) - External monitoring
3. **Pipeline Metrics** (`src/bot/pipeline/utils/metrics.py`) - Pipeline-specific tracking
4. **Monitoring Packages** (`src/core/monitoring_pkg/`, `src/shared/monitoring/`) - System monitoring

## Current Metrics Inventory

### 1. System Metrics

#### Uptime and Performance
- **system_uptime_seconds** (Gauge)
  - Description: System uptime in seconds
  - Labels: None
  - Usage: Track system availability

- **system_requests_total** (Counter)
  - Description: Total number of requests processed
  - Labels: `endpoint`, `method`, `status`
  - Usage: Track API request volume and success rates

- **system_response_time_seconds** (Histogram)
  - Description: Request response time in seconds
  - Labels: `endpoint`, `method`
  - Usage: Monitor API performance

### 2. Bot Metrics

#### Command Processing
- **bot_commands_processed_total** (Counter)
  - Description: Total bot commands processed
  - Labels: `command_type`, `user_id`, `success`
  - Usage: Track bot usage and success rates

#### Pipeline Execution
- **bot_pipeline_executions_total** (Counter)
  - Description: Total pipeline executions
  - Labels: `pipeline`, `section`, `status`
  - Usage: Monitor pipeline health

- **bot_pipeline_duration_seconds** (Histogram)
  - Description: Pipeline execution duration
  - Labels: `pipeline`, `section`
  - Usage: Track pipeline performance

- **ask_section_success_total** (Counter)
  - Description: Total successful pipeline section executions
  - Labels: `section`, `pipeline`
  - Usage: Track pipeline section success

- **ask_section_failure_total** (Counter)
  - Description: Total failed pipeline section executions
  - Labels: `section`, `pipeline`, `error_type`
  - Usage: Track pipeline failures

- **ask_section_retry_total** (Counter)
  - Description: Total retry attempts for pipeline sections
  - Labels: `section`, `pipeline`
  - Usage: Track retry patterns

- **ask_pipeline_latency_seconds** (Histogram)
  - Description: Pipeline section execution latency
  - Labels: `section`, `pipeline`
  - Usage: Monitor pipeline performance

- **ask_pipeline_active_sections** (Gauge)
  - Description: Number of currently active pipeline sections
  - Labels: `pipeline`
  - Usage: Monitor concurrency

- **ask_pipeline_circuit_breaker_state** (Gauge)
  - Description: Circuit breaker state (0=closed, 1=half_open, 2=open)
  - Labels: `section`, `pipeline`
  - Usage: Monitor circuit breaker status

### 3. Cache Metrics

#### Cache Operations
- **cache_operations_total** (Counter)
  - Description: Total cache operations
  - Labels: `operation`, `result`, `cache_type`
  - Usage: Track cache usage patterns

- **cache_hit_rate** (Gauge)
  - Description: Cache hit rate percentage
  - Labels: `cache_type`
  - Usage: Monitor cache effectiveness

- **cache_memory_usage_bytes** (Gauge)
  - Description: Cache memory usage in bytes
  - Labels: `cache_type`
  - Usage: Monitor memory consumption

#### Cache Warming
- **cache_warming_duration_seconds** (Histogram)
  - Description: Time spent warming cache
  - Labels: `job_type`
  - Usage: Track cache warming performance

- **cache_warming_symbols_total** (Gauge)
  - Description: Total number of symbols processed in cache warming
  - Labels: `job_type`
  - Usage: Track cache warming scope

- **cache_warming_success_rate** (Gauge)
  - Description: Success rate of cache warming job (0-1)
  - Labels: `job_type`
  - Usage: Monitor cache warming reliability

- **cache_warming_last_run_timestamp** (Gauge)
  - Description: Timestamp of last cache warming job execution
  - Labels: `job_type`
  - Usage: Track cache warming schedule

#### API Cache
- **api_cache_hits_total** (Counter)
  - Description: Number of API requests served from cache
  - Labels: `endpoint`, `symbol`
  - Usage: Track API cache effectiveness

- **api_cache_misses_total** (Counter)
  - Description: Number of API requests that missed cache
  - Labels: `endpoint`, `symbol`
  - Usage: Track API cache misses

### 4. Data Provider Metrics

#### Provider Performance
- **data_provider_requests_total** (Counter)
  - Description: Total data provider requests
  - Labels: `provider`, `operation`, `status`
  - Usage: Track provider usage

- **data_provider_response_time_seconds** (Histogram)
  - Description: Data provider response time
  - Labels: `provider`, `operation`
  - Usage: Monitor provider performance

- **provider_response_time_ms** (Histogram)
  - Description: Provider response time in milliseconds
  - Labels: `provider_name`, `provider_type`, `operation`
  - Usage: Track provider latency

- **provider_success_rate** (Gauge)
  - Description: Provider success rate percentage
  - Labels: `provider_name`, `provider_type`
  - Usage: Monitor provider reliability

- **provider_fallback_usage_total** (Counter)
  - Description: Number of times fallback providers were used
  - Labels: `primary_provider`, `fallback_provider`, `reason`
  - Usage: Track fallback usage

- **provider_cache_hit_rate** (Gauge)
  - Description: Cache hit rate for each provider
  - Labels: `provider_name`, `provider_type`
  - Usage: Monitor provider cache effectiveness

#### Data Quality
- **data_quality_score** (Gauge)
  - Description: Data quality score (0-100)
  - Labels: `symbol`, `data_type`
  - Usage: Monitor data quality

- **data_completeness_percent** (Gauge)
  - Description: Data completeness percentage
  - Labels: `symbol`, `interval_type`
  - Usage: Track data completeness

- **data_gaps_total** (Counter)
  - Description: Total number of data gaps detected
  - Labels: `symbol`, `interval_type`, `severity`
  - Usage: Track data gaps

- **data_gap_duration_seconds** (Histogram)
  - Description: Duration of detected data gaps
  - Labels: `symbol`, `interval_type`, `severity`
  - Usage: Monitor gap severity

- **data_gap_detected_last_timestamp** (Gauge)
  - Description: Timestamp of last data gap detection
  - Labels: `symbol`, `interval_type`
  - Usage: Track gap detection timing

- **data_freshness_minutes** (Gauge)
  - Description: Data freshness in minutes since last update
  - Labels: `provider_name`, `symbol`, `data_type`
  - Usage: Monitor data freshness

### 5. AI Service Metrics

#### AI Performance
- **ai_requests_total** (Counter)
  - Description: Total AI service requests
  - Labels: `service`, `model`, `status`
  - Usage: Track AI usage

- **ai_processing_time_seconds** (Histogram)
  - Description: AI processing time
  - Labels: `service`, `model`
  - Usage: Monitor AI performance

- **ai_tokens_used_total** (Counter)
  - Description: Total AI tokens used
  - Labels: `service`, `model`, `token_type`
  - Usage: Track AI costs

### 6. Error Metrics

#### Error Tracking
- **errors_total** (Counter)
  - Description: Total errors by type
  - Labels: `error_type`, `component`, `severity`
  - Usage: Track system errors

### 7. TradingView Ingest Metrics

#### Webhook Processing
- **webhook_requests_total** (Counter)
  - Description: Total webhook requests received
  - Labels: `status`, `source`
  - Usage: Track webhook volume

- **webhook_processing_duration_seconds** (Histogram)
  - Description: Webhook processing time
  - Labels: `type`
  - Usage: Monitor webhook performance

#### Data Processing
- **data_parsing_duration_seconds** (Histogram)
  - Description: Data parsing time
  - Labels: `data_type`
  - Usage: Track parsing performance

- **analysis_duration_seconds** (Histogram)
  - Description: Analysis execution time
  - Labels: `analysis_type`
  - Usage: Monitor analysis performance

## Metric Naming Conventions

### Current Patterns
1. **Snake case**: `system_uptime_seconds`
2. **Descriptive suffixes**: `_total`, `_seconds`, `_bytes`, `_rate`
3. **Component prefixes**: `system_`, `bot_`, `cache_`, `ai_`

### Standardized Conventions (Proposed)
1. **Component prefix**: `{component}_{metric_name}_{unit}`
2. **Units**: `_total`, `_seconds`, `_bytes`, `_rate`, `_count`
3. **Status values**: `_success`, `_failure`, `_error`

## Label Conventions

### Common Labels
- `component`: System component (api, bot, cache, etc.)
- `operation`: Specific operation being performed
- `status`: Success/failure status
- `error_type`: Type of error
- `severity`: Error severity level

### Component-Specific Labels
- **API**: `endpoint`, `method`, `status_code`
- **Bot**: `command_type`, `user_id`, `pipeline`, `section`
- **Cache**: `operation`, `result`, `cache_type`
- **Data**: `provider`, `symbol`, `data_type`, `interval_type`

## Usage Patterns Analysis

### High-Frequency Metrics
1. **Request counters** - Tracked on every API call
2. **Cache operations** - Tracked on every cache access
3. **Pipeline executions** - Tracked on every bot command

### Low-Frequency Metrics
1. **System uptime** - Updated periodically
2. **Memory usage** - Updated on health checks
3. **Data quality scores** - Updated on data refresh

### Critical Metrics for Alerting
1. **Error rates** - Alert on high error counts
2. **Response times** - Alert on slow responses
3. **Cache hit rates** - Alert on low hit rates
4. **Data gaps** - Alert on data quality issues

## Integration Points

### Prometheus Integration
- All metrics are exposed via Prometheus format
- Metrics server runs on port 8001 by default
- Grafana dashboards available for visualization

### Custom Storage
- Internal metrics stored in memory with configurable retention
- Thread-safe operations for concurrent access
- Automatic cleanup of old metrics

## Migration Plan

### Phase 1: Consolidation
1. ✅ Create unified metrics service
2. 🔄 Migrate existing metrics to unified service
3. 🔄 Standardize naming conventions
4. 🔄 Remove duplicate metrics

### Phase 2: Enhancement
1. Add persistent storage for metrics
2. Implement advanced alerting
3. Create operational dashboards
4. Add distributed tracing

### Phase 3: Optimization
1. Implement metric sampling
2. Add machine learning for anomaly detection
3. Create self-service metrics platform
4. Optimize for high-throughput scenarios

## Best Practices

### Metric Design
1. **Use appropriate metric types**:
   - Counters for cumulative values
   - Gauges for current state
   - Histograms for distributions

2. **Choose meaningful labels**:
   - Avoid high-cardinality labels
   - Use consistent label names
   - Include relevant context

3. **Set appropriate retention**:
   - Keep only necessary historical data
   - Use sampling for high-frequency metrics
   - Archive old data appropriately

### Performance Considerations
1. **Minimize metric collection overhead**
2. **Use async operations where possible**
3. **Batch metric updates when feasible**
4. **Monitor metrics collection performance**

### Monitoring and Alerting
1. **Set up meaningful alerts**:
   - Error rate thresholds
   - Performance degradation
   - Resource utilization

2. **Create operational dashboards**:
   - System health overview
   - Performance trends
   - Error analysis

3. **Regular metric review**:
   - Remove unused metrics
   - Update alert thresholds
   - Optimize collection frequency
