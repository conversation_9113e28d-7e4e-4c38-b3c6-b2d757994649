# Automatic Debug Console Updates

This system allows the `claude.html` debug console to automatically fetch and display real-time information from your trading system services.

## How It Works

The `claude.html` file uses Server-Sent Events (SSE) to connect to your API's debug stream endpoint at `/api/debug/stream`. The debug console can automatically receive:

1. **Pipeline Execution Events** - Real-time updates as commands flow through the pipeline stages
2. **Tool Usage Events** - Information about which data providers and tools are being used
3. **Log Events** - System logs and health check results
4. **Command Events** - User commands being processed

## Prerequisites

1. Your main API service must be running on port 8000
2. The webhook-ingest service should be running on port 8001
3. All services should be properly connected in your docker/compose/development.yml

## Automatic Fetching Setup

### 1. Start the Debug Console Updater

Run the automatic updater script to simulate real-time events:

```bash
cd /home/<USER>/Desktop/tradingview-automatio
python scripts/update_debug_console.py
```

This script will:
- Connect to your API at `http://localhost:8000`
- Post debug events to `/api/debug/*` endpoints
- Simulate pipeline execution, tool usage, and health checks

### 2. View Real-time Updates

Open `claude.html` in your web browser to see the real-time updates in the Debug Console tab.

## How to Integrate with Real Services

To make this work with your actual services instead of simulation:

1. **In your webhook-ingest service**, add code to post events to the API:
   ```python
   import aiohttp
   
   async def post_debug_event(event_type, data):
       async with aiohttp.ClientSession() as session:
           async with session.post(
               'http://api:8000/api/debug/command',  # Use internal service name
               json={'type': event_type, **data}
           ) as response:
               return response.status == 200
   ```

2. **In your Discord bot**, add similar code to report commands:
   ```python
   # When processing a command
   await post_debug_event('command', {
       'command': '/analyze AAPL',
       'user': 'discord_user_id'
   })
   ```

3. **In your pipeline stages**, report tool usage:
   ```python
   # When using a data provider
   await post_debug_event('tool', {
       'tool': 'PolygonProvider',
       'details': {
           'symbol': 'AAPL',
           'response_time': 0.42
       }
   })
   ```

## Manual Simulation

You can also manually trigger events using the buttons in the Debug Console tab:
- "Simulate /analyze" - Simulates an analysis command
- "Simulate /zones" - Simulates a zones command  
- "Simulate /ask" - Simulates an ask command
- "Advance" - Manually advances the pipeline visualization
- "Reset" - Resets the pipeline visualization

## Troubleshooting

1. **If you don't see updates**:
   - Check that your API is running on port 8000
   - Verify the `/api/debug/stream` endpoint is accessible
   - Check browser console for SSE connection errors

2. **If events aren't posting**:
   - Ensure the API debug routes are enabled
   - Check that the services can reach the API (network connectivity)
   - Verify the API is not blocking requests (CORS, authentication)

3. **Docker network issues**:
   - Make sure all services are on the same network in docker/compose/development.yml
   - Use service names (like 'api') instead of 'localhost' for inter-service communication