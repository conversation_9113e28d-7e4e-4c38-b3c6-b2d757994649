# Pipeline Monitoring and Grading System

## Overview

The Pipeline Monitoring and Grading System provides comprehensive monitoring, evaluation, and quality assessment for pipeline execution. It allows tracking performance metrics, data quality, and reliability for each step in a pipeline and generates detailed reports with letter grades.

## Key Features

- **Step-by-Step Monitoring**: Track execution time and success/failure for each pipeline stage
- **Component-Based Grading**: Evaluate data quality, performance, and reliability separately
- **Weighted Scoring**: Calculate overall grades using configurable weights for different metrics
- **Automatic Report Generation**: Save detailed JSON reports of pipeline execution
- **Customizable Grading Functions**: Extend with custom grading logic for specific use cases
- **Real-Time Feedback**: Get immediate feedback on pipeline quality during execution

## Usage

### Basic Usage

```python
from src.shared.monitoring import PipelineGrader

# Create a grader for your pipeline
grader = PipelineGrader(pipeline_name="stock_analysis_pipeline")

# Start timing a step
grader.start_step("data_fetching")

# Your pipeline code here
data = fetch_stock_data("AAPL")

# End the step with grading information
grader.end_step(
    success=True,
    data_quality_score=95.0,
    performance_score=87.5,
    reliability_score=100.0
)

# Start another step
grader.start_step("technical_analysis")

# More pipeline code
analysis_results = perform_technical_analysis(data)

# End the step with grading information
grader.end_step(
    success=True,
    data_quality_score=92.0,
    performance_score=78.5,
    reliability_score=95.0
)

# Finalize the pipeline grade and get the result
pipeline_grade = grader.finalize_grade()

print(f"Pipeline grade: {pipeline_grade.grade.value}")
print(f"Overall score: {pipeline_grade.overall_score:.1f}")
print(f"Success rate: {pipeline_grade.success_rate:.1f}%")
```

### Using Grading Functions

```python
from src.shared.monitoring import PipelineGrader, grade_data_quality, grade_performance, grade_reliability

# Create a grader
grader = PipelineGrader(pipeline_name="watchlist_pipeline")

# Start timing a step
grader.start_step("fetch_watchlist")

# Your pipeline code here
start_time = time.time()
watchlist_data = fetch_user_watchlist(user_id)
execution_time = time.time() - start_time

# Calculate scores using the provided grading functions
data_quality = grade_data_quality(
    watchlist_data, 
    required_fields=["symbols", "last_updated"],
    expected_length=10
)

performance = grade_performance(
    execution_time=execution_time,
    target_time=0.5,  # Target: 500ms
    critical_threshold=2.0  # Fail if over 2 seconds
)

reliability = grade_reliability(
    success=True,
    error_count=0,
    retry_count=0
)

# End the step with calculated scores
grader.end_step(
    success=True,
    data_quality_score=data_quality,
    performance_score=performance,
    reliability_score=reliability
)

# Finalize the pipeline grade
pipeline_grade = grader.finalize_grade()
```

### Error Handling

```python
grader = PipelineGrader(pipeline_name="error_handling_example")

# Start timing a step
grader.start_step("risky_operation")

try:
    # Attempt risky operation
    result = perform_risky_operation()
    
    # If successful, end step with positive grade
    grader.end_step(
        success=True,
        data_quality_score=90.0,
        performance_score=85.0,
        reliability_score=100.0
    )
except Exception as e:
    # If failed, end step with error information
    grader.end_step(
        success=False,
        error_message=str(e),
        data_quality_score=0.0,
        performance_score=0.0,
        reliability_score=0.0
    )

# Finalize grade (will include error information)
pipeline_grade = grader.finalize_grade()
```

## Grade Levels

The system uses the following grade levels:

| Grade | Score Range |
|-------|-------------|
| A+    | 97-100      |
| A     | 93-96       |
| A-    | 90-92       |
| B+    | 87-89       |
| B     | 83-86       |
| B-    | 80-82       |
| C+    | 77-79       |
| C     | 73-76       |
| C-    | 70-72       |
| D+    | 67-69       |
| D     | 63-66       |
| D-    | 60-62       |
| F     | 0-59        |

## Component Scores

### Data Quality Score

Evaluates the quality of data produced by a pipeline step:
- Completeness (required fields present)
- Expected data size/length
- Custom validation logic

### Performance Score

Evaluates the execution speed of a pipeline step:
- Comparison against target execution time
- Critical threshold checks
- Scaling penalties for slower execution

### Reliability Score

Evaluates the reliability of a pipeline step:
- Success/failure status
- Error count
- Retry attempts needed

## Integration with Existing Systems

### Discord Bot Integration

```python
from src.shared.monitoring import PipelineGrader
from src.bot.pipeline.commands.ask.executor import execute_ask_pipeline

async def enhanced_ask_command(ctx, query):
    # Create a grader for this execution
    grader = PipelineGrader(pipeline_name="ask_command")
    
    # Add metadata about the request
    grader.add_metadata("user_id", ctx.author.id)
    grader.add_metadata("query", query)
    
    # Execute pipeline with grading
    try:
        # Start timing the pipeline execution
        grader.start_step("execute_ask_pipeline")
        
        # Execute the pipeline
        result = await execute_ask_pipeline(ctx, query)
        
        # End step with success
        grader.end_step(
            success=True,
            data_quality_score=95.0,
            performance_score=90.0,
            reliability_score=100.0
        )
    except Exception as e:
        # End step with failure
        grader.end_step(
            success=False,
            error_message=str(e)
        )
        raise
    finally:
        # Always finalize the grade
        pipeline_grade = grader.finalize_grade()
        
        # Optionally log or report the grade
        logger.info(f"Ask pipeline grade: {pipeline_grade.grade.value} ({pipeline_grade.overall_score:.1f})")
    
    return result
```

### API Integration

```python
from fastapi import FastAPI, Depends
from src.shared.monitoring import PipelineGrader

app = FastAPI()

@app.get("/api/market-data/{symbol}")
async def get_market_data(symbol: str):
    # Create a grader for this API endpoint
    grader = PipelineGrader(pipeline_name="market_data_api")
    
    # Add request metadata
    grader.add_metadata("symbol", symbol)
    
    # Start timing data retrieval
    grader.start_step("fetch_market_data")
    
    try:
        # Fetch data
        data = await fetch_market_data(symbol)
        
        # End step with success and metrics
        grader.end_step(
            success=True,
            data_quality_score=95.0,
            performance_score=85.0,
            reliability_score=100.0,
            metrics={"data_points": len(data)}
        )
    except Exception as e:
        # End step with failure
        grader.end_step(
            success=False,
            error_message=str(e)
        )
        raise
    
    # Finalize grade
    pipeline_grade = grader.finalize_grade()
    
    # Return data along with grade information
    return {
        "data": data,
        "metadata": {
            "grade": pipeline_grade.grade.value,
            "score": pipeline_grade.overall_score,
            "execution_time": pipeline_grade.execution_time
        }
    }
```

## Reporting and Visualization

The system automatically saves pipeline grades as JSON files in the `logs/pipeline_grades` directory. These files can be used for:

1. **Historical Analysis**: Track performance trends over time
2. **Quality Dashboards**: Build dashboards to visualize pipeline quality
3. **Alerting**: Set up alerts for failing pipelines or degraded performance
4. **Audit Trails**: Maintain records of pipeline execution for compliance

## Best Practices

1. **Grade Every Pipeline**: Apply grading to all critical pipelines
2. **Set Realistic Targets**: Calibrate performance expectations based on real-world usage
3. **Adjust Weights**: Customize component weights based on what's most important for your use case
4. **Automate Reporting**: Set up automated reporting to track trends over time
5. **Use for Continuous Improvement**: Regularly review grades to identify areas for optimization

## Extending the System

### Custom Grading Functions

```python
def grade_ml_model_accuracy(predictions, actual_values, threshold=0.9):
    """
    Grade machine learning model accuracy.
    
    Args:
        predictions: Model predictions
        actual_values: Actual values
        threshold: Minimum acceptable accuracy
        
    Returns:
        A score from 0-100
    """
    from sklearn.metrics import accuracy_score
    
    try:
        accuracy = accuracy_score(actual_values, predictions)
        
        # Convert accuracy to a 0-100 scale
        if accuracy >= threshold:
            # Scale from threshold to 1.0 to a range of 80-100
            score = 80 + (accuracy - threshold) * (20 / (1 - threshold))
        else:
            # Scale from 0 to threshold to a range of 0-80
            score = accuracy * (80 / threshold)
            
        return score
    except Exception:
        return 0.0
```

### Custom Grade Reporting

```python
def generate_weekly_grade_report(start_date, end_date, output_file="weekly_report.md"):
    """Generate a weekly report of pipeline grades."""
    import glob
    import json
    from datetime import datetime
    
    # Find all grade files in the date range
    files = glob.glob("logs/pipeline_grades/*.json")
    grades = []
    
    for file in files:
        with open(file, 'r') as f:
            try:
                data = json.load(f)
                start_time = datetime.fromisoformat(data["start_time"])
                
                if start_date <= start_time <= end_date:
                    grades.append(data)
            except (json.JSONDecodeError, KeyError):
                continue
    
    # Generate markdown report
    with open(output_file, 'w') as f:
        f.write(f"# Weekly Pipeline Report ({start_date.date()} - {end_date.date()})\n\n")
        
        # Overall statistics
        avg_score = sum(g["overall_score"] for g in grades) / len(grades) if grades else 0
        success_rate = sum(1 for g in grades if g["grade"] in ["A+", "A", "A-", "B+", "B"]) / len(grades) * 100 if grades else 0
        
        f.write(f"**Average Score**: {avg_score:.1f}\n")
        f.write(f"**Success Rate**: {success_rate:.1f}%\n")
        f.write(f"**Total Pipelines**: {len(grades)}\n\n")
        
        # Pipeline breakdown
        f.write("## Pipeline Breakdown\n\n")
        f.write("| Pipeline | Executions | Avg Grade | Avg Time (s) |\n")
        f.write("|----------|------------|-----------|-------------|\n")
        
        # Group by pipeline name
        pipeline_stats = {}
        for g in grades:
            name = g["pipeline_name"]
            if name not in pipeline_stats:
                pipeline_stats[name] = {"count": 0, "total_score": 0, "total_time": 0}
            
            pipeline_stats[name]["count"] += 1
            pipeline_stats[name]["total_score"] += g["overall_score"]
            pipeline_stats[name]["total_time"] += g["execution_time"] or 0
        
        # Write pipeline stats
        for name, stats in pipeline_stats.items():
            avg_score = stats["total_score"] / stats["count"]
            avg_time = stats["total_time"] / stats["count"]
            f.write(f"| {name} | {stats['count']} | {avg_score:.1f} | {avg_time:.2f} |\n")
```

## Conclusion

The Pipeline Monitoring and Grading System provides a robust framework for evaluating pipeline quality, performance, and reliability. By integrating this system into your pipelines, you can:

1. Identify performance bottlenecks
2. Track data quality issues
3. Monitor reliability over time
4. Generate comprehensive quality reports
5. Continuously improve pipeline execution

This system is a key component in achieving high-quality, reliable data processing pipelines that meet the demands of a production environment. 