# /ask Pipeline Enhancement Tasks

## Phase 0: Analysis & Planning (COMPLETED)
- [x] Analyze current AI integration in `ai_chat_processor.py` and legacy stages
- [x] Evaluate data collection patterns and identify unification opportunities
- [x] Review template system and define standardized bot response payload
- [x] Assess symbol detection and AI context understanding strategies
- [x] Examine error handling and fallback mechanisms
- [x] Create phased AI enhancement implementation plan
- [x] Design specialized AI modules per query type
- [x] Develop testing strategy for AI-powered responses
- [x] Document integration points for external AI services
- [x] Plan monitoring and quality assurance for AI components

## Phase 1: Immediate Fixes
- [ ] Fix stage interface compatibility: Make `ai_chat_processor.processor` and `AskPipeline._run_stage` compatible (pass context, return dict)
- [ ] Adjust `execute_ask_pipeline()` result merge to handle dict vs `SectionResult` (prefer dict; if `SectionResult`, merge `.output_data`)
- [ ] Fix broken imports and types in `ai_chat_processor.py` (remove `..data_providers.get_data_provider` and `..models.SectionResult`; switch to `EnhancedMarketDataService`; import correct enums)

## Phase 2: Core AI Enhancements
- [ ] Update OpenAI client to current API: Read `OPENAI_API_KEY` from env; add timeout and robust error handling
- [ ] Enforce JSON schema for AI result (`intent`, `symbols`, `needs_data`, `response`) with validation and fallbacks (use Pydantic)
- [ ] Add config toggles in `ask/config.py` (model name, temperature, provider list, enable_data_fetch, timeouts)
- [ ] Create Pydantic model `AIAskResult` for strict parsing/validation
- [ ] Externalize prompts and JSON schema to `src/bot/pipeline/commands/ask/prompts.py`

## Phase 3: Data Layer Refactoring
- [ ] Refactor data fetching to be async and resilient using `EnhancedMarketDataService.get_comprehensive_stock_data()`
- [ ] Implement async, concurrent data fetching with `asyncio.gather`, per-symbol timeouts, and graceful aggregation

## Phase 4: Reliability & Resilience
- [ ] Add retries/rate-limiting/backoff for OpenAI and data providers using `tenacity`
- [ ] Implement offline fallback path when OpenAI is unavailable: use `symbol_validator.py` + canned responses
- [ ] Add `tenacity` to requirements/environments/production.txt and document retry behavior

## Phase 5: Monitoring & Optimization
- [ ] Improve structured logging: include `pipeline_id`, stage timings, OpenAI tokens/cost
- [ ] Add Redis-backed cache for AI analysis with TTL and config toggles
- [ ] Implement logging hygiene: use `src/core/logger.get_logger`, sanitize sensitive data

## Phase 6: Testing & Validation
- [ ] Write unit tests for `AIChatProcessor` (mock OpenAI, validate parsing, data fetch decisions)
- [ ] Write tests for `execute_ask_pipeline()` happy/error paths
- [ ] Ensure consistent Discord response formatting with standardized payload
- [ ] Add integration/e2e tests for multi-symbol queries with mocked services

## Phase 7: Architecture Decision
- [ ] Decide on multi-stage vs single-stage: remove/archive legacy stages or rewire them
- [ ] Update documentation to reflect architecture decisions

## Phase 8: Documentation
- [ ] Update README and `docs/api/market_data_api.md` with /ask flow and config requirements
- [ ] Document all environment variables and configuration options

## 🚀 Recommended Additions (Based on Audit)

1. **Add Phase 9: Advanced Analytics**
   - Implement specialized analysis modules for short squeeze detection
   - Add technical indicator library (RSI, MACD, VWAP)
   - Integrate social sentiment and news analysis

2. **Add Phase 10: User Personalization**
   - Implement user preference tracking
   - Add portfolio context integration
   - Create personalized recommendation engine

3. **Enhance Phase 4** with:
   - Circuit breaker pattern for data providers
   - Health checks for external services
   - Fallback to multiple data sources

 




 /ask Pipeline Enhancement Tasks
Phase 0: Analysis & Planning (COMPLETED)
 Analyze current AI integration in ai_chat_processor.py and legacy stages
 Evaluate data collection patterns and identify unification opportunities
 Review template system and define standardized bot response payload
 Assess symbol detection and AI context understanding strategies
 Examine error handling and fallback mechanisms
 Create phased AI enhancement implementation plan
 Design specialized AI modules per query type
 Develop testing strategy for AI-powered responses
 Document integration points for external AI services
 Plan monitoring and quality assurance for AI components
Phase 1: Immediate Fixes
 Fix stage interface compatibility: Make ai_chat_processor.processor and AskPipeline._run_stage compatible (pass context, return dict)
 Adjust execute_ask_pipeline() result merge to handle dict vs SectionResult (prefer dict; if SectionResult, merge .output_data)
 Fix broken imports and types in ai_chat_processor.py (remove ..data_providers.get_data_provider and ..models.SectionResult; switch to EnhancedMarketDataService; import correct enums)
Phase 2: Core AI Enhancements
 Update OpenAI client to current API: Read OPENAI_API_KEY from env; add timeout and robust error handling
 Enforce JSON schema for AI result (intent, symbols, needs_data, response) with validation and fallbacks (use Pydantic)
 Add config toggles in ask/config.py (model name, temperature, provider list, enable_data_fetch, timeouts)
 Create Pydantic model AIAskResult for strict parsing/validation
 Externalize prompts and JSON schema to src/bot/pipeline/commands/ask/prompts.py
Phase 3: Data Layer Refactoring
 Refactor data fetching to be async and resilient using EnhancedMarketDataService.get_comprehensive_stock_data()
 Implement async, concurrent data fetching with asyncio.gather, per-symbol timeouts, and graceful aggregation
Phase 4: Reliability & Resilience
 Add retries/rate-limiting/backoff for OpenAI and data providers using tenacity
 Implement offline fallback path when OpenAI is unavailable: use symbol_validator.py + canned responses
 Add tenacity to requirements/environments/production.txt and document retry behavior
Phase 5: Monitoring & Optimization
 Improve structured logging: include pipeline_id, stage timings, OpenAI tokens/cost
 Add Redis-backed cache for AI analysis with TTL and config toggles
 Implement logging hygiene: use src/core/logger.get_logger, sanitize sensitive data
Phase 6: Testing & Validation
 Write unit tests for AIChatProcessor (mock OpenAI, validate parsing, data fetch decisions)
 Write tests for execute_ask_pipeline() happy/error paths
 Ensure consistent Discord response formatting with standardized payload
 Add integration/e2e tests for multi-symbol queries with mocked services
Phase 7: Architecture Decision
 Decide on multi-stage vs single-stage: remove/archive legacy stages or rewire them
 Update documentation to reflect architecture decisions
Phase 8: Documentation
 Update README and docs/api/market_data_api.md with /ask flow and config requirements
 Document all environment variables and configuration options
Phase 9: Advanced Analytics (Recommended)
 Implement specialized analysis modules for short squeeze detection
 Add technical indicator library (RSI, MACD, VWAP)
 Integrate social sentiment and news analysis
Phase 10: User Personalization (Recommended)
 Implement user preference tracking
 Add portfolio context integration
 Create personalized recommendation engine