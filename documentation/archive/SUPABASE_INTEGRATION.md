# Supabase Integration for TradingView Webhook Ingest

This document explains how to set up and use the Supabase integration for the TradingView webhook ingest service.

## Overview

The webhook ingest service now supports storing data in Supabase, a PostgreSQL-based backend-as-a-service platform. This integration allows for:

- Storing webhook data in a structured database
- Real-time updates using Supabase's realtime capabilities
- Secure access control using Row Level Security (RLS)
- Easy querying and analysis of trading data

## Setup

### 1. Create a Supabase Project

1. Go to [Supabase](https://supabase.com/) and create an account if you don't have one
2. Create a new project
3. Note your project URL and API key (found in Project Settings > API)

### 2. Run Migrations

Apply the database migrations to set up the required tables:

```bash
# From your Supabase project dashboard:
# 1. Go to SQL Editor
# 2. Copy the contents of migrations/01_create_webhook_tables.sql
# 3. Run the SQL script
```

### 3. Configure Environment Variables

Add the following environment variables to your `.env.secure` file:

```
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=your-supabase-api-key
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
USE_SUPABASE=true
```

## Database Schema

The integration creates the following tables:

### webhooks

Stores raw webhook data from TradingView:

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| webhook_id | TEXT | Unique identifier for the webhook |
| timestamp | TIMESTAMPTZ | When the webhook was received |
| client_ip | TEXT | IP address of the client |
| raw_data | JSONB | Raw webhook payload |
| status | TEXT | Processing status (received, processed, failed) |
| processed_at | TIMESTAMPTZ | When the webhook was processed |
| processed_data | JSONB | Processed webhook data |
| created_at | TIMESTAMPTZ | When the record was created |

### tickers

Stores information about trading symbols:

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| symbol | TEXT | Trading symbol (e.g., AAPL) |
| last_price | NUMERIC | Last known price |
| last_updated | TIMESTAMPTZ | When the ticker was last updated |
| first_seen | TIMESTAMPTZ | When the ticker was first seen |
| is_active | BOOLEAN | Whether the ticker is active |
| alert_count | INTEGER | Number of alerts for this ticker |
| metadata | JSONB | Additional metadata |

### signals

Stores trading signals generated from webhooks:

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| symbol | TEXT | Trading symbol |
| signal_type | TEXT | Type of signal (e.g., BUY, SELL) |
| timeframe | TEXT | Timeframe of the signal |
| entry_price | NUMERIC | Entry price |
| tp1_price | NUMERIC | Take profit 1 price |
| tp2_price | NUMERIC | Take profit 2 price |
| tp3_price | NUMERIC | Take profit 3 price |
| sl_price | NUMERIC | Stop loss price |
| confidence | NUMERIC | Confidence level |
| timestamp | TIMESTAMPTZ | When the signal was generated |
| created_at | TIMESTAMPTZ | When the record was created |
| metadata | JSONB | Additional metadata |

## Security

The integration uses Supabase's Row Level Security (RLS) to secure access to the data:

- Authenticated users can read data
- Only the service role can write data
- Anonymous users have no access

## Usage

The webhook ingest service will automatically store incoming webhooks in Supabase if the integration is enabled. You can then query the data using the Supabase API or SQL.

### Example Queries

#### Get recent webhooks

```sql
SELECT * FROM webhooks
ORDER BY timestamp DESC
LIMIT 10;
```

#### Get active tickers with alerts

```sql
SELECT symbol, last_price, alert_count
FROM tickers
WHERE is_active = true
ORDER BY alert_count DESC;
```

#### Get signals for a specific symbol

```sql
SELECT * FROM signals
WHERE symbol = 'AAPL'
ORDER BY timestamp DESC;
```

## Realtime Updates

The integration enables realtime updates for all tables. You can subscribe to changes using the Supabase client:

```javascript
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

supabase
  .from('signals')
  .on('INSERT', (payload) => {
    console.log('New signal:', payload.new);
  })
  .subscribe();
```

## Best Practices

Based on our Supabase audit, here are some best practices to follow:

### Security Best Practices

1. **Enable Row Level Security (RLS) on all tables**: Every table in the public schema should have RLS enabled to prevent unauthorized access.

2. **Create appropriate RLS policies**: Each table should have policies that restrict access based on user roles.

3. **Use service roles for backend operations**: Backend services should use the service role to bypass RLS when necessary.

4. **Avoid storing sensitive data in public tables**: Use secure storage for API keys and credentials.

### Performance Best Practices

1. **Optimize RLS policies**: Use `(SELECT auth.uid())` instead of `auth.uid()` in RLS policies to avoid re-evaluation for each row.

2. **Consolidate permissive policies**: Avoid having multiple permissive policies for the same role and action.

3. **Create appropriate indexes**: Add indexes for columns frequently used in WHERE clauses, especially user_id columns.

4. **Use prepared statements**: When executing SQL queries, use prepared statements to improve performance.

### Database Structure Best Practices

1. **Use appropriate data types**: Choose the right data types for your columns to optimize storage and performance.

2. **Add constraints**: Use constraints like NOT NULL, UNIQUE, and CHECK to ensure data integrity.

3. **Document your schema**: Add comments to tables and columns to make your schema more understandable.

4. **Use migrations for schema changes**: Always use migration scripts to make changes to your schema.

## Troubleshooting

If you encounter issues with the Supabase integration:

1. Check that your environment variables are correctly set
2. Verify that the tables exist in your Supabase project
3. Check the logs for any error messages
4. Ensure your Supabase API key has the necessary permissions
5. Run the security and performance advisors in the Supabase dashboard to identify issues
