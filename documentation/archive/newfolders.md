src/analysis/
├── __init__.py
├── ai/
│   ├── __init__.py
│   ├── recommendation_engine.py
│   ├── enhancement_strategy.py
│   └── calculators/
│       ├── __init__.py
│       └── sentiment_calculator.py
├── fundamental/
│   ├── __init__.py
│   ├── metrics.py
│   └── calculators/
│       ├── __init__.py
│       ├── pe_calculator.py
│       └── growth_calculator.py
├── risk/
│   ├── __init__.py
│   ├── assessment.py
│   └── calculators/
│       ├── __init__.py
│       ├── volatility_calculator.py
│       └── beta_calculator.py
├── technical/
│   ├── __init__.py
│   ├── indicators.py
│   └── calculators/
│       ├── __init__.py
│       ├── rsi_calculator.py
│       └── macd_calculator.py
├── orchestration/
│   ├── __init__.py
│   ├── analysis_orchestrator.py
│   └── enhancement_strategy.py
└── utils/
    ├── __init__.py
    └── data_validators.py