# /ask Command Enhancement Summary

## Features Implemented

### 1. Batch Query Support
- Added automatic symbol extraction from queries
- Implemented parallel processing of queries for multiple symbols
- Added result aggregation and formatting
- Added concurrency control to prevent overloading
- Created batch processor module at `src/bot/pipeline/commands/ask/batch_processor.py`

### 2. Voice Input Parsing
- Added voice attachment processing capabilities
- Implemented audio format conversion (OGG, MP3, etc. to WAV)
- Integrated speech-to-text conversion using SpeechRecognition library
- Added support for voice message attachments in the `/ask` command
- Created voice processor module at `src/bot/pipeline/commands/ask/stages/voice_processor.py`

### 3. Multi-Language Detection
- Added language detection capabilities using langdetect library
- Implemented support for 10 major languages (English, Spanish, French, German, Italian, Portuguese, Russian, Chinese, Japanese, Korean)
- Added language-aware response handling
- Created language detector module at `src/bot/pipeline/commands/ask/stages/language_detector.py`
- Updated system prompts to include language awareness

## Technical Implementation Details

### Dependencies Added
- `langdetect==1.0.9` - Language detection library
- `SpeechRecognition==3.10.0` - Speech to text conversion
- `pydub==0.25.1` - Audio processing library

### Key Files Modified
1. `src/bot/client.py` - Updated `/ask` command handler to support attachments and language detection
2. `src/bot/pipeline/commands/ask/stages/prompts.py` - Updated system prompts with language awareness
3. `src/bot/pipeline/commands/ask/batch_processor.py` - Enhanced with better documentation
4. `requirements/environments/production.txt` - Added new dependencies

### Features
- Users can now send voice messages to the bot which will be automatically transcribed
- The bot can detect the language of queries and respond appropriately
- Batch queries with multiple symbols are automatically processed in parallel
- All features maintain compliance with financial disclaimer requirements
- Voice processing includes error handling and fallback mechanisms

## Usage Examples

### Voice Input
Users can now attach voice messages to their `/ask` commands, which will be automatically transcribed and processed.

### Batch Queries
Queries containing multiple stock symbols (e.g., "Compare $AAPL and $GOOGL technical indicators") are automatically detected and processed as batch queries.

### Language Detection
The bot automatically detects the language of queries and can provide appropriate responses, defaulting to English for unsupported languages while informing users of the limitation.