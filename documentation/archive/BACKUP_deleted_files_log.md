This file tracks files deleted by the audit for easy restoration via VCS if needed.

Deleted on 2025-09-15:
- src/templates/core.py (unused duplicate of core formatting templates)

Phase 1 - Unused Database Files (zero imports found):
- src/database/db.py (110 lines, DatabaseManager class - no references)
- src/database/smart_db.py (166 lines, SmartDatabaseManager class - no references)

Phase 2 - Legacy Technical Analysis (migrated to shared):
- src/analysis/technical/indicators.py (325 lines, TechnicalIndicatorsCalculator - migrated to shared)
- src/analysis/technical/price_targets.py (494 lines, PriceTargetEngine/TrendDirection - migrated to shared)
- src/analysis/technical/timeframe_confirmation.py (684 lines, unused timeframe logic)
- src/analysis/technical/config.py (108 lines, legacy config - replaced by shared config)

Phase 3 - Deprecated Symbol Validator (migrated to shared):
- src/bot/pipeline/commands/ask/stages/symbol_validator.py (270 lines, SymbolValidator - migrated to shared)

Phase 4 - Unused Technical Analysis Calculators (no imports found):
- src/analysis/technical/calculators/macd_calculator.py (8872 lines, unused MACD calculator)
- src/analysis/technical/calculators/rsi_calculator.py (6407 lines, unused RSI calculator)
- src/analysis/technical/calculators/ (entire directory - no references)

Phase 5 - Unused Cache Managers (no imports found):
- src/bot/pipeline/commands/ask/modules/utils/cache_manager.py (285 lines, unused thread-safe cache)
- src/bot/pipeline/commands/ask/modules/utils/cache.py (283 lines, unused comprehensive cache)
- src/bot/pipeline/commands/ask/stages/utils/cache_manager.py (32 lines, unused stub)
- src/bot/pipeline/commands/ask/stages/utils/enhanced_cache_manager.py (466 lines, unused enhanced cache)
- src/shared/redis/cache_manager.py (360 lines, unused Redis cache)

Phase 6 - Legacy Data Providers (migrated to shared):
- src/data/providers/alpha_vantage_provider.py (11178 lines, migrated to shared)
- src/data/providers/finnhub_provider.py (9620 lines, migrated to shared)
- src/data/providers/polygon_provider.py (10437 lines, migrated to shared)
- src/data/providers/yfinance_provider.py (13940 lines, migrated to shared)
- src/data/providers/base.py (19103 lines, migrated to shared unified_base)
- src/data/providers/manager.py (12554 lines, migrated to shared aggregator)
- src/data/providers/config.py (5072 lines, legacy config)

Phase 7 - Legacy Template Engine (replaced with lightweight wrapper):
- src/bot/pipeline/commands/ask/stages/response_templates.py (1901 lines, replaced with 132-line wrapper)
- src/templates/ask.py (24 lines, deprecated wrapper)

Phase 8 - Mega-File Breakup (modularized for maintainability):
- src/api/data/providers/data_source_manager.py (1424 lines, broken into modules)
  - Extracted: modules/config.py (configuration and data structures)
  - Extracted: modules/auditing.py (pipeline auditing and monitoring)
  - Extracted: modules/rate_limiting.py (rate limiting and throttling)
  - Extracted: modules/validation.py (data validation and quality assessment)
  - Backup: data_source_manager_backup.py (original preserved)

Phase 9 - Backup File Cleanup (unused backup files):
- src/bot/client_original.py (1639 lines, unused backup)
- src/api/data/providers/data_source_manager_backup.py (1423 lines, duplicate backup)
- src/api/data/providers/data_source_manager_original.py (1423 lines, duplicate backup)

