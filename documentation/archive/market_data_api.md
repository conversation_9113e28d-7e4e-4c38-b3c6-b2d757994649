# Market Data API

This service provides market data using Yahoo Finance as the data source. It's a reliable alternative to Finnhub and doesn't require an API key.

## Base URL
```
/api/market
```

## Endpoints

### Get Current Price
```
GET /price/{symbol}
```

**Parameters:**
- `symbol` (path): Stock symbol (e.g., AAPL, MSFT)

**Example Response:**
```json
{
  "symbol": "AAPL",
  "price": 175.25,
  "timestamp": "2025-08-21T13:30:00.000000",
  "source": "yfinance"
}
```

### Get Historical Data
```
GET /historical/{symbol}
```

**Query Parameters:**
- `start_date` (required): Start date in YYYY-MM-DD format
- `end_date` (optional): End date in YYYY-MM-DD format (defaults to today)
- `interval` (optional): Data interval (1d, 1wk, 1mo) - defaults to "1d"

**Example Request:**
```
GET /api/market/historical/AAPL?start_date=2025-01-01&end_date=2025-08-21&interval=1d
```

**Example Response:**
```json
{
  "symbol": "AAPL",
  "data": [
    {
      "Date": "2025-01-02 00:00:00",
      "Open": 130.28,
      "High": 132.63,
      "Low": 130.05,
      "Close": 131.86,
      "Volume": 104487900,
      "Dividends": 0.0,
      "Stock Splits": 0.0
    },
    ...
  ],
  "start_date": "2025-01-01",
  "end_date": "2025-08-21",
  "interval": "1d",
  "source": "yfinance"
}
```

### Get Intraday Data
```
GET /intraday/{symbol}
```

**Query Parameters:**
- `interval` (optional): Data interval (1m, 5m, 15m, 30m, 60m, 90m, 1h) - defaults to "5m"
- `days` (optional): Number of days of data to fetch (1-7) - defaults to 1

**Example Request:**
```
GET /api/market/intraday/AAPL?interval=5m&days=1
```

**Example Response:**
```json
{
  "symbol": "AAPL",
  "data": [
    {
      "Datetime": "2025-08-21 09:30:00",
      "Open": 174.25,
      "High": 174.50,
      "Low": 174.10,
      "Close": 174.35,
      "Volume": 1234567
    },
    ...
  ],
  "interval": "5m",
  "days": 1,
  "source": "yfinance"
}
```

## Rate Limiting
- No rate limits are currently enforced, but please be reasonable with your requests.
- For high-frequency data, consider implementing caching.

## Error Responses

### 400 Bad Request
```json
{
  "detail": "Invalid date format: Start date cannot be after end date"
}
```

### 404 Not Found
```json
{
  "detail": "No data found for INVALID_SYMBOL"
}
```

### 500 Internal Server Error
```json
{
  "detail": "Error fetching data: ..."
}
```

## Implementation Notes
- Data is fetched in real-time from Yahoo Finance
- No API key is required
- All prices are in USD
- Market hours are in UTC

## Example Usage

### Python
```python
import requests

# Get current price
response = requests.get("http://your-server/api/market/price/AAPL")
print(response.json())

# Get historical data
params = {
    'start_date': '2025-01-01',
    'end_date': '2025-08-21',
    'interval': '1d'
}
response = requests.get("http://your-server/api/market/historical/AAPL", params=params)
data = response.json()
```

## Testing
You can test the endpoints directly in your browser or using tools like curl:

```bash
# Get current price
curl "http://your-server/api/market/price/AAPL"

# Get historical data
curl "http://your-server/api/market/historical/AAPL?start_date=2025-01-01&interval=1d"

# Get intraday data
curl "http://your-server/api/market/intraday/AAPL?interval=5m&days=1"
```

## /ask Command Pipeline Integration

The Market Data API is integrated with the enhanced `/ask` command pipeline, which provides AI-powered trading analysis and insights.

### /ask Command Flow

1. **User Query**: User sends a question via Discord `/ask` command
2. **AI Analysis**: OpenRouter AI analyzes the query to determine:
   - Intent (stock analysis, general question, comparison, etc.)
   - Symbols mentioned in the query
   - Whether market data is needed
3. **Data Fetching**: If needed, fetches real-time market data concurrently for all symbols
4. **Response Generation**: AI generates comprehensive response using collected data
5. **Formatting**: Response is formatted for Discord with professional styling

### Configuration Requirements

#### Environment Variables for /ask Command
```bash
# OpenRouter AI Service
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# AI Model Configuration
AI_MODEL=deepseek/deepseek-chat-v3.1
AI_TEMPERATURE=0.7
AI_MAX_TOKENS=1000
AI_TIMEOUT=30

# Data Provider Configuration
FINNHUB_ENABLED=false
POLYGON_ENABLED=false
ALPHA_VANTAGE_ENABLED=false

# Performance Optimization
REDIS_CACHE_ENABLED=true
REDIS_CACHE_TTL=300
AI_CACHE_ENABLED=true

# Retry Configuration
AI_MAX_RETRIES=3
DATA_FETCH_TIMEOUT=10
```

#### Response Format
The `/ask` command returns responses in a standardized format:
```json
{
  "response": "Formatted analysis text with markdown",
  "data": {
    "symbol": {
      "current_price": 175.25,
      "change_percent": 1.5,
      "volume": 1000000,
      "technical_indicators": {...},
      "sentiment": {...}
    }
  },
  "intent": "stock_analysis",
  "symbols": ["AAPL", "MSFT"],
  "needs_data": true
}
```

### Error Handling
- **AI Service Unavailable**: Falls back to symbol validation + canned responses
- **Data Fetching Failures**: Continues with available data, provides partial analysis
- **Rate Limiting**: Automatic retries with exponential backoff
- **Validation Errors**: Graceful degradation with user-friendly error messages

### Performance Features
- **Parallel Data Fetching**: Multiple symbols fetched concurrently
- **Intelligent Caching**: Redis-backed cache for frequent queries
- **Timeout Management**: Configurable timeouts for AI and data services
- **Quality Assurance**: Comprehensive testing and validation suite

### Testing
The `/ask` pipeline includes extensive testing:
- Unit tests for AI processing logic
- Integration tests with mocked services
- End-to-end tests for complete pipeline validation
- Performance testing under load

For more details on the `/ask` command implementation, see the pipeline documentation in `src/bot/pipeline/commands/ask/`.
