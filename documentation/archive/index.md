# System Architecture and Workflow Audit

This document provides a comprehensive breakdown of the `tradingview-automation` project, serving as a single source of truth for its architecture, workflow, and security.

## Table of Contents

1.  [Webhook Ingestion](./1_webhook_ingestion.md)
2.  [Discord Bot](./2_discord_bot.md)
3.  [AI Pipeline](./3_ai_pipeline.md)
4.  [Data and Database](./4_data_and_database.md)
5.  [Core Components](./5_core_components.md)
6.  [Security Audit](./6_security_audit.md)
