# Redis Container Requirements

## Overview
The Redis container provides caching and message queuing services for the TradingView Automation system. It is used for temporary data storage, session management, and inter-service communication.

## Container Configuration
- **Service Name**: redis
- **Container Name**: tradingview-redis-dev
- **Base Image**: redis:7-alpine
- **Command**: `redis-server --requirepass ${REDIS_PASSWORD} --appendonly yes`

## Environment Variables
- REDIS_PASSWORD=${REDIS_PASSWORD} (from .env file)

## Volumes
- redis_data:/data (Persistent data storage)

## Ports
- 6379:6379 (Redis default port)

## Networks
- internal-network (Internal services)
- tradingview-network (Main network)

## Health Check
- Test: `redis-cli -a ${REDIS_PASSWORD} ping`
- Interval: 10s
- Timeout: 5s
- Retries: 3

## Key Responsibilities
1. Provide in-memory caching for frequently accessed data
2. Serve as a message queue for inter-service communication
3. Store session data for API and bot services
4. Maintain temporary state information for pipelines
5. Support pub/sub messaging patterns between services

## Security Considerations
- Password authentication required for all connections
- Persistent storage with AOF (Append Only File) enabled
- Isolated on internal network for core services
- No direct external access exposed

## Performance Considerations
- Uses lightweight Alpine Linux image
- AOF persistence for data durability
- Configured as a requirement for other services to ensure availability

## Dependencies
- None (standalone Redis service)

## Required By
- API container
- Discord Bot container
- Webhook Ingest container