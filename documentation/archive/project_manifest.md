# Project File Manifest for `tradingview-automatio`

This file lists all files and folders for auditing purposes. Use the 'Audit Notes' section below to keep notes.

**Summary:** 232 directories, 978 files, ~168,726 non-empty lines.

### File Tree
```text
tradingview-automatio/
├── .coverage  (176 lines)
├── .cursor/
│   └── config.json  (13 lines)
├── .cursorignore  (2 lines)
├── .dockerignore  (24 lines)
├── .gitignore  (63 lines)
├── .kiro/
│   └── specs/
│       └── commands-audit/
│           ├── design.md  (225 lines)
│           ├── requirements.md  (39 lines)
│           ├── task2.md  (185 lines)
│           └── tasks.md  (166 lines)
├── AI_DEBUGGER_SUMMARY.md  (124 lines)
├── AI_POWERED_QUERY_INTERPRETER_SUMMARY.md  (104 lines)
├── AI_QUERY_INTERPRETER_FIXES_SUMMARY.md  (101 lines)
├── CHATBOT_FIX_SUMMARY.md  (86 lines)
├── CODEBASE_EXPLORER_SUMMARY.md  (101 lines)
├── COMMAND_AUDIT_REPORT.md  (139 lines)
├── CRITICAL_FIXES_SUMMARY.md  (48 lines)
├── CURRENT_STATUS.md  (42 lines)
├── DISCLAIMER_FIX_SUMMARY.md  (44 lines)
├── DOCKER.md  (27 lines)
├── DOCKER_HEALTH_CHECK_AUDIT.md  (75 lines)
├── Dockerfile  (45 lines)
├── ENVIRONMENT_FIX_REPORT.md  (59 lines)
├── ENV_VARIABLES.md  (53 lines)
├── FAILURE_ANALYSIS_AND_FIX.md  (86 lines)
├── FINAL_AUDIT_REPORT.md  (70 lines)
├── FINAL_FIX_SUMMARY.md  (68 lines)
├── FINAL_LOG_ANALYSIS.md  (61 lines)
├── FINAL_STATUS_AND_TEST.md  (61 lines)
├── FINAL_TEST_RESULTS.md  (69 lines)
├── FINAL_WORKING_STATUS.md  (65 lines)
├── INTELLIGENT_GRADING_SUMMARY.md  (58 lines)
├── PERFORMANCE_FIXES_SUMMARY.md  (70 lines)
├── PERFORMANCE_IMPROVEMENTS.md  (133 lines)
├── PERFORMANCE_OPTIMIZATION_SUMMARY.md  (110 lines)
├── PROJECT_OVERVIEW.md  (774 lines)
├── README.md  (81 lines)
├── README_CODEBASE_EXPLORER.md  (137 lines)
├── REFACTORING_GUIDE.md  (102 lines)
├── SECURITY_AUDIT_REPORT.md  (55 lines)
├── add_timeout_handling.py  (80 lines)
├── alembic/
│   ├── env.py  (52 lines)
│   └── versions/
│       └── .gitkeep  (1 lines)
├── alembic.ini  (38 lines)
├── architecture_detailed.json  (4068 lines)
├── backups/
│   └── stub-removed/
│       ├── analyze.py  (31 lines)
│       ├── ask_backup.py  (31 lines)
│       ├── ask_fixed.py  (296 lines)
│       ├── ask_original.py  (252 lines)
│       ├── help.py  (60 lines)
│       ├── portfolio.py  (37 lines)
│       ├── recommendations.py  (31 lines)
│       ├── watchlist.py  (39 lines)
│       └── zones.py  (31 lines)
├── check_table_structure.py  (87 lines)
├── check_tables.py  (102 lines)
├── claude.html  (769 lines)
├── complete_fix.py  (192 lines)
├── comprehensive_test.py  (162 lines)
├── config/
├── config_api.py  (262 lines)
├── create_ai_query_interpreter.py  (197 lines)
├── create_fixed_pipeline.py  (124 lines)
├── create_watchlist_tables.py  (69 lines)
├── create_working_pipeline.py  (107 lines)
├── dashboard/
│   ├── DASHBOARD_SUMMARY.md  (69 lines)
│   ├── README.md  (55 lines)
│   ├── auto_update.sh  (53 lines)
│   ├── dashboard_generator.py  (367 lines)
│   ├── fix_docker_network.py  (201 lines)
│   ├── monitor_services.py  (175 lines)
│   ├── pipeline_analyzer.py  (215 lines)
│   ├── pipeline_data.json  (421 lines)
│   ├── system_dashboard.html  (990 lines)
│   ├── test.txt  (1 lines)
│   └── update_dashboard.sh  (28 lines)
├── dashboard_requirements/environments/production.txt  (15 lines)
├── data/
│   ├── context/
│   │   ├── conv_discord:conv_-2997810244074675534_1757641467.153141.json  (13 lines)
│   │   ├── conv_discord:conv_-302142993699364046_1757641523.138229.json  (13 lines)
│   │   ├── conv_discord:conv_-4078157315345074310_1757644518.60983.json  (13 lines)
│   │   ├── conv_discord:conv_-5387016997699176859_1757644598.085874.json  (13 lines)
│   │   ├── conv_discord:conv_-8003830481273157522_1757641563.529192.json  (13 lines)
│   │   ├── conv_discord:conv_6241505781410727370_1757644456.861526.json  (13 lines)
│   │   ├── conv_discord:conv_701543426776952118_1757641897.618009.json  (13 lines)
│   │   ├── conv_discord:conv_7999110414914176335_1757641950.212137.json  (13 lines)
│   │   ├── platform_mappings.json  (17 lines)
│   │   ├── user_01712a8341d64ca9ae62cd587e6ba462.json  (11 lines)
│   │   ├── user_05d765e5af214a7dae9b532efa179d30.json  (11 lines)
│   │   ├── user_1ddc99a5a9054ca6a352a7593dcdf332.json  (24 lines)
│   │   ├── user_4218863a215744e8855c184fcb18670f.json  (11 lines)
│   │   ├── user_5295572a71014768b632a1a036a9dc89.json  (11 lines)
│   │   ├── user_5f842e6b604b4d3999e59434ea478771.json  (11 lines)
│   │   ├── user_66be084406a642e4b9c05d6ef1a604c7.json  (11 lines)
│   │   ├── user_7906548e940748448e7a10b0419ce9c6.json  (24 lines)
│   │   ├── user_d49780f64205402e96b8c0eb82a11c46.json  (11 lines)
│   │   ├── user_efa09e4e1d034153b19d978f38277f11.json  (11 lines)
│   │   └── user_f2f17b905d4c41ed871cdae63e3f9ea5.json  (24 lines)
│   ├── models/
│   │   └── sentiment/
│   │       ├── financial_terms.json  (16 lines)
│   │       ├── sentiment_weights.json  (8 lines)
│   │       └── word_embeddings.json  (134 lines)
│   ├── response_metrics.json  (8 lines)
│   ├── tickers/
│   │   ├── all_tickers.txt  (6659 lines)
│   │   ├── nasdaq_tickers.txt  (4001 lines)
│   │   └── nyse_tickers.txt  (3115 lines)
│   └── watchlists/
│       └── test_user_123_watchlist.json  (54 lines)
├── debug_ast.py  (37 lines)
├── debug_pattern_matching.py  (26 lines)
├── debug_sentiment.py  (18 lines)
├── debug_sentiment_detailed.py  (25 lines)
├── debug_sentiment_model.py  (17 lines)
├── demo_enhanced_analysis.py  (238 lines)
├── dev-copy  (19 lines)
├── dev-watch  (26 lines)
├── docker/
│   ├── README.md  (53 lines)
│   ├── compose/
│   │   └── services/
│   │       └── tradingview-ingest.yml  (142 lines)
│   ├── config/
│   │   └── .gitkeep  (1 lines)
│   └── dockerfiles/
│       ├── app.Dockerfile  (51 lines)
│       └── services/
│           ├── monitor.Dockerfile  (28 lines)
│           ├── processor.Dockerfile  (28 lines)
│           └── webhook.Dockerfile  (41 lines)
├── docker-compose.prod.yml  (125 lines)
├── docker/compose/development.yml  (137 lines)
├── docker-entrypoint.sh  (52 lines)
├── docker-nuke  (21 lines)
├── docs/
│   ├── PIPELINE_MONITORING.md  (316 lines)
│   ├── api/
│   │   └── .gitkeep  (1 lines)
│   ├── audit/
│   │   └── .gitkeep  (1 lines)
│   ├── cleanup/
│   ├── deployment/
│   │   └── PRODUCTION_DEPLOYMENT.md  (144 lines)
│   ├── security/
│   │   └── SECURITY_CHECKLIST.md  (94 lines)
│   ├── system_audit/
│   │   └── .gitkeep  (1 lines)
│   ├── testing/
│   │   └── .gitkeep  (1 lines)
│   └── user/
│       └── .gitkeep  (1 lines)
├── documentation_index.md  (28 lines)
├── example_pipeline_integration.py  (100 lines)
├── examples/
│   └── database_usage.py  (73 lines)
├── final_working_solution.py  (225 lines)
├── fix_ai_parsing_errors.py  (129 lines)
├── fix_ai_processor.py  (184 lines)
├── fix_ai_processor_constructor.py  (110 lines)
├── fix_ai_query_interpretation.py  (187 lines)
├── fix_api_keys.py  (81 lines)
├── fix_ask_command_simple.py  (63 lines)
├── fix_chatbot_complete.py  (220 lines)
├── fix_circuit_breaker_error.py  (23 lines)
├── fix_clean_responses.py  (184 lines)
├── fix_context_parameter.py  (25 lines)
├── fix_data_provider.py  (61 lines)
├── fix_discord_final.py  (59 lines)
├── fix_discord_helpers.py  (70 lines)
├── fix_discord_interaction.py  (93 lines)
├── fix_discord_interaction_final.py  (74 lines)
├── fix_discord_send.py  (50 lines)
├── fix_f_string.py  (20 lines)
├── fix_logger_import.py  (20 lines)
├── fix_mock_issue.py  (73 lines)
├── fix_pattern_matching_order.py  (112 lines)
├── fix_pipeline_ai.py  (86 lines)
├── fix_pipeline_data_structure.py  (28 lines)
├── fix_pipeline_export.py  (17 lines)
├── fix_pipeline_export_final.py  (20 lines)
├── fix_pipeline_grader.py  (44 lines)
├── fix_processor_exception.py  (184 lines)
├── fix_real_processing.py  (217 lines)
├── fix_symbol_extraction.py  (45 lines)
├── fix_symbol_extraction_ai.py  (109 lines)
├── fix_timing_only.py  (30 lines)
├── fix_trading_keywords.py  (25 lines)
├── fix_user_id_parameter.py  (25 lines)
├── fix_username_parameter.py  (25 lines)
├── fixed_ai_service_wrapper.py  (470 lines)
├── generate_and_send_report.py  (75 lines)
├── generate_architecture.py  (252 lines)
├── generate_detailed_architecture.py  (256 lines)
├── import_test.py  (25 lines)
├── improve_pattern_matching.py  (23 lines)
├── init-db.sql  (62 lines)
├── markdown_backup/
│   ├── 1_webhook_ingestion.md  (88 lines)
│   ├── 2_discord_bot.md  (47 lines)
│   ├── 3_ai_pipeline.md  (44 lines)
│   ├── 4_data_and_database.md  (31 lines)
│   ├── 5_core_components.md  (30 lines)
│   ├── ALERT_FORMAT_UPGRADE_SUMMARY.md  (91 lines)
│   ├── ARCHITECTURE_DECISION.md  (81 lines)
│   ├── ASK_COMMAND_ENHANCEMENTS.md  (43 lines)
│   ├── AUTOMATION_ARCHITECTURE.md  (418 lines)
│   ├── AUTOMATION_IMPLEMENTATION_CHECKLIST.md  (243 lines)
│   ├── CANONICAL_MODULES_GUIDE.md  (72 lines)
│   ├── CODE_DEDUPLICATION_PLAN.md  (293 lines)
│   ├── COMMANDS.md  (238 lines)
│   ├── COMMANDS_ANALYSIS.md  (112 lines)
│   ├── DEPLOYMENT_GUIDE.md  (330 lines)
│   ├── ENHANCED_AI_CONTEXT_AND_CLASSIFICATION.md  (101 lines)
│   ├── ENHANCED_ANALYSIS_IMPLEMENTATION.md  (244 lines)
│   ├── ENHANCEMENT_CHECKLIST.md  (283 lines)
│   ├── FALLBACK_REMEDIATION_SUMMARY.md  (161 lines)
│   ├── FALLBACK_VALUE_ANALYSIS.md  (154 lines)
│   ├── Gemini.md  (158 lines)
│   ├── HEALTH_MONITORING.md  (131 lines)
│   ├── IMPLEMENTATION_STRATEGY.md  (158 lines)
│   ├── IMPLEMENTATION_SUMMARY.md  (129 lines)
│   ├── LEGACY_CODE_EXTERNAL_IMPACT_ANALYSIS.md  (145 lines)
│   ├── LEGACY_CODE_REMOVAL_PLAN.md  (121 lines)
│   ├── LICENSE.md  (17 lines)
│   ├── MIGRATION_GUIDE.md  (133 lines)
│   ├── NETWORK_ARCHITECTURE.md  (108 lines)
│   ├── PHASE2_COMPLETION_SUMMARY.md  (90 lines)
│   ├── PIPELINE.md  (145 lines)
│   ├── PROJECT_STATUS_SUMMARY.md  (60 lines)
│   ├── README.md  (0 lines)
│   ├── REFACTORING_STATUS.md  (170 lines)
│   ├── SECRETS.md  (49 lines)
│   ├── SECURITY.md  (140 lines)
│   ├── SECURITY_ARCHITECTURE.md  (135 lines)
│   ├── SHARED_MODULE_IMPROVEMENTS.md  (164 lines)
│   ├── SUPABASE_INTEGRATION.md  (135 lines)
│   ├── SYSTEM_ANALYSIS_CRITIQUE.md  (125 lines)
│   ├── TEST_SUMMARY.md  (148 lines)
│   ├── UNIFIED_TASKS.md  (155 lines)
│   ├── USAGE.md  (136 lines)
│   ├── ZONES_ENHANCEMENT_SUMMARY.md  (67 lines)
│   ├── analysis_summary.md  (76 lines)
│   ├── config_consolidation_summary.md  (74 lines)
│   ├── container_debug_todo.md  (65 lines)
│   ├── design.md  (285 lines)
│   ├── folder_structure_analysis.md  (172 lines)
│   ├── immediatetasklist..md  (146 lines)
│   ├── index.md  (9 lines)
│   ├── market_data_api.md  (211 lines)
│   ├── newestchecklist.md  (185 lines)
│   ├── newfolders.md  (37 lines)
│   ├── pipeline_performance_baseline.md  (161 lines)
│   ├── requirements.md  (84 lines)
│   ├── rules.md  (49 lines)
│   ├── task.md  (109 lines)
│   ├── tasks.bot.md  (57 lines)
│   ├── tasks.md  (171 lines)
│   ├── tasks123.md  (232 lines)
│   ├── test_checklist.md  (58 lines)
│   ├── todo.md  (164 lines)
│   └── tomorrow.md  (328 lines)
├── nginx/
│   ├── logs/
│   ├── nginx.conf  (125 lines)
│   ├── ssl/
│   └── webhook.conf  (51 lines)
├── ngrok.yml  (11 lines)
├── notebooks/
│   ├── data_provider_analysis.ipynb  (152 lines)
│   ├── requirements/environments/production.txt  (7 lines)
│   └── supertrend_analysis.ipynb  (207 lines)
├── optimize_bot_performance.py  (116 lines)
├── optimize_performance.py  (96 lines)
├── performance_optimization.py  (68 lines)
├── pipeline_events.py  (358 lines)
├── pipeline_monitoring_report.md  (89 lines)
├── postgres/
│   └── logs/
├── pyproject.toml  (29 lines)
├── pytest.ini  (20 lines)
├── pytest_comprehensive.ini  (44 lines)
├── quick_start_db.py  (62 lines)
├── redis/
│   └── logs/
├── redis.conf  (0 lines)
├── requirements/
│   └── services/
├── requirements/environments/production.txt  (62 lines)
├── response_metrics.json  (36 lines)
├── scripts/
│   ├── additional_files_to_delete.sh  (48 lines)
│   ├── auto_cleanup.sh  (26 lines)
│   ├── check_before_delete.sh  (80 lines)
│   ├── check_docker_configs.sh  (50 lines)
│   ├── cleanup_pycache.sh  (12 lines)
│   ├── consolidate_supabase.sh  (53 lines)
│   ├── consolidate_watchlist.sh  (223 lines)
│   ├── data/
│   │   └── .gitkeep  (1 lines)
│   ├── docker_security_scan.sh  (88 lines)
│   ├── enhance_pipeline.sh  (600 lines)
│   ├── final_cleanup.sh  (76 lines)
│   ├── fix_data_providers.py  (146 lines)
│   ├── fix_docker_compose_secrets.sh  (75 lines)
│   ├── fix_env_variables.sh  (36 lines)
│   ├── fix_security_issues.sh  (318 lines)
│   ├── generate_deprecation_report.py  (270 lines)
│   ├── generate_docker_secrets.sh  (111 lines)
│   ├── generate_secrets.py  (111 lines)
│   ├── generate_self_signed_cert.sh  (28 lines)
│   ├── integrate_pipeline_monitoring.sh  (511 lines)
│   ├── list_files_for_deletion.sh  (80 lines)
│   ├── logs/
│   ├── manage_migrations.py  (87 lines)
│   ├── manage_secrets.py  (116 lines)
│   ├── monitor_deprecation.py  (118 lines)
│   ├── print_zones.py  (23 lines)
│   ├── run_pipeline_monitoring.sh  (199 lines)
│   ├── run_security_checks.sh  (38 lines)
│   ├── run_security_tests.py  (32 lines)
│   ├── run_tests.py  (88 lines)
│   ├── safe_hardcoded_fix.py  (138 lines)
│   ├── security_cleanup.py  (186 lines)
│   ├── security_test.py  (211 lines)
│   ├── setup_letsencrypt.sh  (92 lines)
│   ├── test_ai_memory.py  (344 lines)
│   ├── test_db_in_docker.sh  (39 lines)
│   ├── test_fallback_remediation.py  (174 lines)
│   ├── test_full_analysis.py  (63 lines)
│   └── update_imports.py  (124 lines)
├── security-requirements/environments/production.txt  (7 lines)
├── simple_db_test.py  (78 lines)
├── simple_pipeline_emitter.py  (155 lines)
├── simple_test.py  (74 lines)
├── src/
│   ├── __init__.py  (0 lines)
│   ├── __pycache__/
│   ├── analysis/
│   │   ├── __init__.py  (18 lines)
│   │   ├── __pycache__/
│   │   ├── ai/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── calculators/
│   │   │   │   ├── __init__.py  (0 lines)
│   │   │   │   └── sentiment_calculator.py  (82 lines)
│   │   │   ├── enhancement_strategy.py  (226 lines)
│   │   │   └── recommendation_engine.py  (375 lines)
│   │   ├── fundamental/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── calculators/
│   │   │   │   ├── __init__.py  (0 lines)
│   │   │   │   ├── growth_calculator.py  (226 lines)
│   │   │   │   └── pe_calculator.py  (144 lines)
│   │   │   └── metrics.py  (135 lines)
│   │   ├── orchestration/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── analysis_orchestrator.py  (289 lines)
│   │   │   └── enhancement_strategy.py  (178 lines)
│   │   ├── probability/
│   │   │   ├── __init__.py  (3 lines)
│   │   │   └── probability_engine.py  (607 lines)
│   │   ├── risk/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── assessment.py  (131 lines)
│   │   │   └── calculators/
│   │   │       ├── __init__.py  (0 lines)
│   │   │       ├── __pycache__/
│   │   │       ├── beta_calculator.py  (171 lines)
│   │   │       └── volatility_calculator.py  (143 lines)
│   │   ├── technical/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── calculators/
│   │   │   │   ├── __init__.py  (0 lines)
│   │   │   │   ├── macd_calculator.py  (196 lines)
│   │   │   │   └── rsi_calculator.py  (161 lines)
│   │   │   ├── config.py  (86 lines)
│   │   │   ├── indicators.py  (188 lines)
│   │   │   ├── price_targets.py  (399 lines)
│   │   │   └── timeframe_confirmation.py  (541 lines)
│   │   └── utils/
│   │       ├── __init__.py  (0 lines)
│   │       └── data_validators.py  (190 lines)
│   ├── api/
│   │   ├── __init__.py  (0 lines)
│   │   ├── __pycache__/
│   │   ├── analytics/
│   │   │   └── __init__.py  (0 lines)
│   │   ├── config.py  (48 lines)
│   │   ├── data/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── cache.py  (366 lines)
│   │   │   ├── cache_warming_scheduler.py  (235 lines)
│   │   │   ├── constants.py  (18 lines)
│   │   │   ├── market_data_service.py  (176 lines)
│   │   │   ├── metrics.py  (553 lines)
│   │   │   ├── providers/
│   │   │   │   ├── __init__.py  (0 lines)
│   │   │   │   ├── __pycache__/
│   │   │   │   ├── alpha_vantage.py  (145 lines)
│   │   │   │   ├── base.py  (238 lines)
│   │   │   │   ├── data_source_manager.py  (1179 lines)
│   │   │   │   ├── finnhub.py  (217 lines)
│   │   │   │   └── polygon.py  (319 lines)
│   │   │   └── scheduled_tasks.py  (215 lines)
│   │   ├── main.py  (136 lines)
│   │   ├── middleware/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── security.py  (182 lines)
│   │   │   └── security_utils.py  (52 lines)
│   │   ├── routers/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   └── market_data.py  (167 lines)
│   │   ├── routes/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── analytics.py  (256 lines)
│   │   │   ├── bot_health.py  (236 lines)
│   │   │   ├── dashboard.py  (355 lines)
│   │   │   ├── debug.py  (58 lines)
│   │   │   ├── feedback.py  (57 lines)
│   │   │   ├── health.py  (131 lines)
│   │   │   ├── market_data.py  (259 lines)
│   │   │   └── metrics.py  (35 lines)
│   │   ├── schemas/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── feedback_schema.py  (72 lines)
│   │   │   └── metrics_schema.py  (46 lines)
│   │   └── webhooks/
│   │       └── __init__.py  (0 lines)
│   ├── bot/
│   │   ├── __init__.py  (0 lines)
│   │   ├── __main__.py  (75 lines)
│   │   ├── __pycache__/
│   │   ├── audit/
│   │   │   ├── __init__.py  (10 lines)
│   │   │   ├── rate_limiter.py  (204 lines)
│   │   │   ├── request_visualizer.py  (70 lines)
│   │   │   ├── request_visualizer.py.fixed  (70 lines)
│   │   │   ├── request_visualizer_patch.py  (52 lines)
│   │   │   └── session_manager.py  (68 lines)
│   │   ├── client.py  (1117 lines)
│   │   ├── client_audit_integration.py  (160 lines)
│   │   ├── client_original.py  (1378 lines)
│   │   ├── client_with_monitoring.py  (111 lines)
│   │   ├── core/
│   │   │   ├── __pycache__/
│   │   │   ├── bot.py  (139 lines)
│   │   │   ├── error_handler.py  (32 lines)
│   │   │   └── services.py  (124 lines)
│   │   ├── database_manager.py  (163 lines)
│   │   ├── enhancements/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── discord_ux.py  (357 lines)
│   │   │   └── pipeline_visualizer.py  (437 lines)
│   │   ├── events/
│   │   │   └── __init__.py  (0 lines)
│   │   ├── extensions/
│   │   │   ├── __init__.py  (3 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── alerts.py  (403 lines)
│   │   │   ├── analyze.py  (429 lines)
│   │   │   ├── ask.py  (388 lines)
│   │   │   ├── batch_analyze.py  (462 lines)
│   │   │   ├── error_handler.py  (49 lines)
│   │   │   ├── help.py  (570 lines)
│   │   │   ├── portfolio.py  (564 lines)
│   │   │   ├── recommendations.py  (651 lines)
│   │   │   ├── status.py  (70 lines)
│   │   │   ├── utility.py  (29 lines)
│   │   │   ├── watchlist.py  (582 lines)
│   │   │   └── zones.py  (671 lines)
│   │   ├── main.py  (24 lines)
│   │   ├── metrics_collector.py  (221 lines)
│   │   ├── monitoring/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   └── health_monitor.py  (346 lines)
│   │   ├── permissions.py  (287 lines)
│   │   ├── pipeline/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── ask/
│   │   │   │   ├── __init__.py  (0 lines)
│   │   │   │   └── stages/
│   │   │   │       ├── __init__.py  (0 lines)
│   │   │   │       ├── ai_chat_processor.py  (72 lines)
│   │   │   │       ├── ai_service_wrapper.py  (43 lines)
│   │   │   │       └── conversation_memory_service.py  (420 lines)
│   │   │   ├── commands/
│   │   │   │   ├── __init__.py  (0 lines)
│   │   │   │   ├── __pycache__/
│   │   │   │   ├── analyze/
│   │   │   │   │   ├── __init__.py  (0 lines)
│   │   │   │   │   ├── __pycache__/
│   │   │   │   │   ├── parallel_pipeline.py  (568 lines)
│   │   │   │   │   ├── pipeline.py  (414 lines)
│   │   │   │   │   └── stages/
│   │   │   │   │       ├── __init__.py  (13 lines)
│   │   │   │   │       ├── enhanced_analysis.py  (91 lines)
│   │   │   │   │       ├── fetch_data.py  (140 lines)
│   │   │   │   │       ├── price_targets.py  (54 lines)
│   │   │   │   │       ├── report_generator.py  (116 lines)
│   │   │   │   │       ├── report_template.py  (215 lines)
│   │   │   │   │       └── technical_analysis.py  (61 lines)
│   │   │   │   ├── ask/
│   │   │   │   │   ├── __init__.py  (7 lines)
│   │   │   │   │   ├── __pycache__/
│   │   │   │   │   ├── batch_processor.py  (320 lines)
│   │   │   │   │   ├── config.py  (569 lines)
│   │   │   │   │   ├── error_handler.py  (31 lines)
│   │   │   │   │   ├── events.py  (34 lines)
│   │   │   │   │   ├── executor.py  (109 lines)
│   │   │   │   │   ├── executor_with_grading.py  (162 lines)
│   │   │   │   │   ├── modules/
│   │   │   │   │   │   ├── __init__.py  (3 lines)
│   │   │   │   │   │   ├── config/
│   │   │   │   │   │   │   └── __init__.py  (3 lines)
│   │   │   │   │   │   ├── models/
│   │   │   │   │   │   │   ├── __init__.py  (3 lines)
│   │   │   │   │   │   │   └── data_models.py  (212 lines)
│   │   │   │   │   │   ├── services/
│   │   │   │   │   │   │   └── __init__.py  (3 lines)
│   │   │   │   │   │   ├── tests/
│   │   │   │   │   │   │   └── __init__.py  (3 lines)
│   │   │   │   │   │   └── utils/
│   │   │   │   │   │       ├── __init__.py  (115 lines)
│   │   │   │   │   │       ├── cache.py  (237 lines)
│   │   │   │   │   │       ├── cache_manager.py  (223 lines)
│   │   │   │   │   │       ├── retry.py  (252 lines)
│   │   │   │   │   │       ├── validation.py  (255 lines)
│   │   │   │   │   │       └── validators.py  (198 lines)
│   │   │   │   │   ├── pipeline.py  (287 lines)
│   │   │   │   │   ├── stages/
│   │   │   │   │   │   ├── __init__.py  (24 lines)
│   │   │   │   │   │   ├── __pycache__/
│   │   │   │   │   │   ├── advanced_classifier.py  (394 lines)
│   │   │   │   │   │   ├── ai_cache.py  (389 lines)
│   │   │   │   │   │   ├── ai_chat_processor.py  (138 lines)
│   │   │   │   │   │   ├── ai_models_config.yaml  (41 lines)
│   │   │   │   │   │   ├── ai_models_config_loader.py  (206 lines)
│   │   │   │   │   │   ├── ai_routing_service.py  (467 lines)
│   │   │   │   │   │   ├── ai_service_wrapper.py  (43 lines)
│   │   │   │   │   │   ├── ai_symbol_extractor.py  (252 lines)
│   │   │   │   │   │   ├── ask_sections.py  (801 lines)
│   │   │   │   │   │   ├── botlogs.txt  (186 lines)
│   │   │   │   │   │   ├── config.py  (194 lines)
│   │   │   │   │   │   ├── conversation_memory_service.py  (446 lines)
│   │   │   │   │   │   ├── core/
│   │   │   │   │   │   │   ├── __init__.py  (16 lines)
│   │   │   │   │   │   │   ├── ai_client.py  (32 lines)
│   │   │   │   │   │   │   ├── base.py  (113 lines)
│   │   │   │   │   │   │   ├── enhanced_ai_client.py  (287 lines)
│   │   │   │   │   │   │   ├── error_handler.py  (233 lines)
│   │   │   │   │   │   │   ├── market_context_processor.py  (365 lines)
│   │   │   │   │   │   │   ├── response_parser.py  (21 lines)
│   │   │   │   │   │   │   └── technical_analysis_processor.py  (483 lines)
│   │   │   │   │   │   ├── cross_platform_context.py  (386 lines)
│   │   │   │   │   │   ├── depth_style_analyzer.py  (471 lines)
│   │   │   │   │   │   ├── discord_formatter.py  (318 lines)
│   │   │   │   │   │   ├── enhanced_analyzer.py  (199 lines)
│   │   │   │   │   │   ├── enhanced_context.py  (463 lines)
│   │   │   │   │   │   ├── language_detector.py  (240 lines)
│   │   │   │   │   │   ├── market_context_service.py  (390 lines)
│   │   │   │   │   │   ├── ml_sentiment_analyzer.py  (312 lines)
│   │   │   │   │   │   ├── models.py  (69 lines)
│   │   │   │   │   │   ├── pipeline_sections.py  (301 lines)
│   │   │   │   │   │   ├── postprocessor/
│   │   │   │   │   │   │   ├── __init__.py  (14 lines)
│   │   │   │   │   │   │   ├── memory_updater.py  (21 lines)
│   │   │   │   │   │   │   ├── metrics_collector.py  (21 lines)
│   │   │   │   │   │   │   ├── response_formatter.py  (16 lines)
│   │   │   │   │   │   │   └── response_generator.py  (293 lines)
│   │   │   │   │   │   ├── preprocessor/
│   │   │   │   │   │   │   ├── __init__.py  (15 lines)
│   │   │   │   │   │   │   ├── context_builder.py  (21 lines)
│   │   │   │   │   │   │   ├── context_processor.py  (208 lines)
│   │   │   │   │   │   │   ├── input_processor.py  (122 lines)
│   │   │   │   │   │   │   ├── input_validator.py  (19 lines)
│   │   │   │   │   │   │   └── prompt_formatter.py  (16 lines)
│   │   │   │   │   │   ├── prompts.py  (362 lines)
│   │   │   │   │   │   ├── query_analyzer.py  (481 lines)
│   │   │   │   │   │   ├── quick_commands.py  (340 lines)
│   │   │   │   │   │   ├── response_audit.py  (219 lines)
│   │   │   │   │   │   ├── response_templates.py  (1483 lines)
│   │   │   │   │   │   ├── response_validator.py  (232 lines)
│   │   │   │   │   │   ├── symbol_validator.py  (207 lines)
│   │   │   │   │   │   ├── test_ai_models_config.py  (165 lines)
│   │   │   │   │   │   ├── test_infrastructure.py  (153 lines)
│   │   │   │   │   │   ├── utils/
│   │   │   │   │   │   │   ├── __init__.py  (15 lines)
│   │   │   │   │   │   │   ├── cache_integration.py  (108 lines)
│   │   │   │   │   │   │   ├── cache_manager.py  (24 lines)
│   │   │   │   │   │   │   ├── enhanced_cache_manager.py  (372 lines)
│   │   │   │   │   │   │   ├── fallback_handler.py  (17 lines)
│   │   │   │   │   │   │   └── rate_limiter.py  (20 lines)
│   │   │   │   │   │   └── voice_processor.py  (155 lines)
│   │   │   │   │   ├── test_modular_system.py  (44 lines)
│   │   │   │   │   └── utility.py  (33 lines)
│   │   │   │   └── watchlist/
│   │   │   │       ├── __init__.py  (0 lines)
│   │   │   │       └── stages/
│   │   │   │           └── __init__.py  (0 lines)
│   │   │   ├── core/
│   │   │   │   ├── __init__.py  (0 lines)
│   │   │   │   ├── __pycache__/
│   │   │   │   ├── circuit_breaker.py  (268 lines)
│   │   │   │   ├── context_manager.py  (239 lines)
│   │   │   │   ├── parallel_pipeline.py  (198 lines)
│   │   │   │   ├── pipeline_engine.py  (375 lines)
│   │   │   │   └── pipeline_optimizer.py  (256 lines)
│   │   │   ├── data/
│   │   │   │   └── __init__.py  (0 lines)
│   │   │   ├── logs/
│   │   │   │   └── __init__.py  (0 lines)
│   │   │   ├── monitoring/
│   │   │   │   └── __init__.py  (0 lines)
│   │   │   ├── performance_optimizer.py  (244 lines)
│   │   │   ├── shared/
│   │   │   │   ├── __init__.py  (0 lines)
│   │   │   │   ├── data_collectors/
│   │   │   │   │   └── __init__.py  (0 lines)
│   │   │   │   ├── formatters/
│   │   │   │   │   └── __init__.py  (0 lines)
│   │   │   │   └── validators/
│   │   │   │       └── __init__.py  (0 lines)
│   │   │   ├── test_pipeline.py  (96 lines)
│   │   │   └── utils/
│   │   │       ├── __init__.py  (0 lines)
│   │   │       ├── __pycache__/
│   │   │       ├── circuit_breaker.py  (165 lines)
│   │   │       └── metrics.py  (216 lines)
│   │   ├── pipeline_framework.py  (175 lines)
│   │   ├── rate_limiter.py  (295 lines)
│   │   ├── security/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   └── advanced_security.py  (360 lines)
│   │   ├── services/
│   │   ├── setup_audit.py  (241 lines)
│   │   ├── token_validator.py  (139 lines)
│   │   ├── update_imports.py  (39 lines)
│   │   ├── utils/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── component_checker.py  (50 lines)
│   │   │   ├── disclaimer_manager.py  (149 lines)
│   │   │   ├── error_handler.py  (181 lines)
│   │   │   ├── input_sanitizer.py  (240 lines)
│   │   │   └── rate_limiter.py  (107 lines)
│   │   ├── watchlist_alerts.py  (452 lines)
│   │   └── watchlist_realtime.py  (361 lines)
│   ├── core/
│   │   ├── __init__.py  (38 lines)
│   │   ├── __pycache__/
│   │   ├── automation/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   ├── analysis_scheduler.py  (403 lines)
│   │   │   ├── discord_handler.py  (248 lines)
│   │   │   ├── report_engine.py  (958 lines)
│   │   │   ├── report_formatter.py  (673 lines)
│   │   │   └── report_scheduler.py  (511 lines)
│   │   ├── config_manager.py  (491 lines)
│   │   ├── data_quality_validator.py  (181 lines)
│   │   ├── enums/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   └── stock_analysis.py  (59 lines)
│   │   ├── exceptions.py  (80 lines)
│   │   ├── feedback_mechanism.py  (441 lines)
│   │   ├── formatting/
│   │   │   ├── __init__.py  (9 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── analysis_template.py  (239 lines)
│   │   │   ├── response_templates.py  (198 lines)
│   │   │   ├── technical_analysis.py  (261 lines)
│   │   │   └── text_formatting.py  (100 lines)
│   │   ├── logger.py  (22 lines)
│   │   ├── market_calendar.py  (440 lines)
│   │   ├── monitoring_pkg/
│   │   │   ├── __init__.py  (4 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── bot_monitor.py  (289 lines)
│   │   │   └── performance_tracker.py  (131 lines)
│   │   ├── pipeline_engine.py  (188 lines)
│   │   ├── prompts/
│   │   │   ├── __init__.py  (26 lines)
│   │   │   ├── models.py  (87 lines)
│   │   │   ├── prompt_manager.py  (386 lines)
│   │   │   └── templates/
│   │   │       ├── __init__.py  (3 lines)
│   │   │       └── system_prompt.txt  (56 lines)
│   │   ├── response_generator.py  (199 lines)
│   │   ├── risk_management/
│   │   │   ├── __init__.py  (19 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── atr_calculator.py  (508 lines)
│   │   │   └── compliance_framework.py  (370 lines)
│   │   ├── scheduler.py  (61 lines)
│   │   ├── secure_cache.py  (140 lines)
│   │   ├── trade_scanner.py  (154 lines)
│   │   ├── utils.py  (205 lines)
│   │   ├── validation/
│   │   │   ├── __init__.py  (3 lines)
│   │   │   ├── __pycache__/
│   │   │   └── financial_validator.py  (391 lines)
│   │   └── watchlist/
│   │       └── __init__.py  (6 lines)
│   ├── data/
│   │   ├── __init__.py  (34 lines)
│   │   ├── __pycache__/
│   │   ├── cache/
│   │   │   ├── __init__.py  (12 lines)
│   │   │   ├── __pycache__/
│   │   │   └── manager.py  (244 lines)
│   │   ├── models/
│   │   │   ├── __init__.py  (16 lines)
│   │   │   ├── indicators.py  (56 lines)
│   │   │   └── stock_data.py  (95 lines)
│   │   └── providers/
│   │       ├── __init__.py  (105 lines)
│   │       ├── __pycache__/
│   │       ├── alpha_vantage_provider.py  (266 lines)
│   │       ├── base.py  (497 lines)
│   │       ├── config.py  (141 lines)
│   │       ├── finnhub_provider.py  (247 lines)
│   │       ├── manager.py  (285 lines)
│   │       ├── polygon_provider.py  (260 lines)
│   │       └── yfinance_provider.py  (290 lines)
│   ├── database/
│   │   ├── __init__.py  (0 lines)
│   │   ├── __pycache__/
│   │   ├── config.py  (84 lines)
│   │   ├── connection.py  (231 lines)
│   │   ├── db.py  (93 lines)
│   │   ├── migrations/
│   │   │   ├── __init__.py  (0 lines)
│   │   │   └── env.py  (80 lines)
│   │   ├── models/
│   │   │   ├── __init__.py  (20 lines)
│   │   │   ├── alerts.py  (69 lines)
│   │   │   ├── analysis.py  (67 lines)
│   │   │   ├── interactions.py  (57 lines)
│   │   │   └── market_data.py  (50 lines)
│   │   ├── query_wrapper.py  (210 lines)
│   │   ├── repositories/
│   │   │   └── __init__.py  (0 lines)
│   │   ├── smart_db.py  (140 lines)
│   │   ├── supabase_client.py  (202 lines)
│   │   ├── unified_client.py  (209 lines)
│   │   ├── unified_db.py  (241 lines)
│   │   └── working_db.py  (133 lines)
│   ├── logs/
│   │   └── __init__.py  (0 lines)
│   ├── main.py  (202 lines)
│   ├── security/
│   │   ├── __init__.py  (3 lines)
│   │   └── middleware.py  (7 lines)
│   ├── services/
│   │   ├── __init__.py  (0 lines)
│   │   └── analytics_service.py  (81 lines)
│   ├── shared/
│   │   ├── __init__.py  (3 lines)
│   │   ├── __pycache__/
│   │   ├── ai/
│   │   │   ├── __init__.py  (3 lines)
│   │   │   ├── depth_controller.py  (191 lines)
│   │   │   ├── model_fine_tuner.py  (376 lines)
│   │   │   └── recommendation_engine.py  (483 lines)
│   │   ├── ai_chat/
│   │   │   ├── __init__.py  (31 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── ai_client.py  (286 lines)
│   │   │   ├── config.py  (52 lines)
│   │   │   ├── data_fetcher.py  (122 lines)
│   │   │   ├── fallbacks.py  (35 lines)
│   │   │   ├── models.py  (29 lines)
│   │   │   ├── processor.py  (171 lines)
│   │   │   └── response_formatter.py  (151 lines)
│   │   ├── ai_debugger/
│   │   │   ├── __init__.py  (8 lines)
│   │   │   ├── live_ai_debugger.py  (345 lines)
│   │   │   └── local_pattern_debugger.py  (299 lines)
│   │   ├── ai_services/
│   │   │   ├── __init__.py  (3 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── ai_chat_processor.py  (100 lines)
│   │   │   ├── ai_chat_processor_legacy.py  (661 lines)
│   │   │   ├── ai_service_wrapper.py  (243 lines)
│   │   │   ├── circuit_breaker.py  (131 lines)
│   │   │   ├── fallback_handler.py  (267 lines)
│   │   │   ├── fast_price_lookup.py  (112 lines)
│   │   │   ├── intelligent_chatbot.py  (539 lines)
│   │   │   ├── intelligent_chatbot_backup.py  (522 lines)
│   │   │   ├── openrouter_key.py  (6 lines)
│   │   │   ├── performance_optimizer.py  (103 lines)
│   │   │   ├── query_cache.py  (140 lines)
│   │   │   ├── query_router.py  (327 lines)
│   │   │   ├── response_synthesizer.py  (265 lines)
│   │   │   ├── simple_query_analyzer.py  (195 lines)
│   │   │   ├── timeout_manager.py  (82 lines)
│   │   │   └── tool_registry.py  (326 lines)
│   │   ├── analytics/
│   │   │   ├── __init__.py  (3 lines)
│   │   │   └── performance_tracker.py  (402 lines)
│   │   ├── background/
│   │   │   ├── __init__.py  (4 lines)
│   │   │   ├── celery_app.py  (64 lines)
│   │   │   └── tasks/
│   │   │       ├── __init__.py  (3 lines)
│   │   │       ├── indicators.py  (68 lines)
│   │   │       └── market_intelligence.py  (356 lines)
│   │   ├── cache/
│   │   │   ├── __pycache__/
│   │   │   └── cache_service.py  (177 lines)
│   │   ├── config/
│   │   │   ├── __pycache__/
│   │   │   └── config_manager.py  (167 lines)
│   │   ├── configuration/
│   │   │   ├── __init__.py  (32 lines)
│   │   │   └── validators.py  (233 lines)
│   │   ├── data_providers/
│   │   │   ├── __init__.py  (16 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── aggregator.py  (242 lines)
│   │   │   ├── alpaca_provider.py  (299 lines)
│   │   │   ├── alpha_vantage.py  (148 lines)
│   │   │   ├── base.py  (58 lines)
│   │   │   ├── fallback_provider.py  (91 lines)
│   │   │   ├── polygon_provider.py  (181 lines)
│   │   │   ├── unified_base.py  (62 lines)
│   │   │   └── yfinance_provider.py  (217 lines)
│   │   ├── data_validation.py  (482 lines)
│   │   ├── database/
│   │   │   ├── __init__.py  (23 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── db_manager.py  (198 lines)
│   │   │   ├── supabase_base.py  (43 lines)
│   │   │   ├── supabase_http_client.py  (183 lines)
│   │   │   ├── supabase_sdk_client.py  (383 lines)
│   │   │   └── usage_example.py  (58 lines)
│   │   ├── error_handling/
│   │   │   ├── __init__.py  (13 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── fallback.py  (260 lines)
│   │   │   ├── logging.py  (177 lines)
│   │   │   └── retry.py  (369 lines)
│   │   ├── market_analysis/
│   │   │   ├── __init__.py  (19 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── confidence_scorer.py  (225 lines)
│   │   │   ├── signal_analyzer.py  (72 lines)
│   │   │   ├── signals.py  (66 lines)
│   │   │   ├── unified_signal_analyzer.py  (325 lines)
│   │   │   └── utils.py  (91 lines)
│   │   ├── metrics/
│   │   │   ├── __pycache__/
│   │   │   └── metrics_service.py  (187 lines)
│   │   ├── monitoring/
│   │   │   ├── __init__.py  (22 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── intelligent_grader.py  (161 lines)
│   │   │   ├── performance_monitor.py  (137 lines)
│   │   │   ├── pipeline_grader.py  (498 lines)
│   │   │   ├── pipeline_monitor.py  (13 lines)
│   │   │   └── step_logger.py  (138 lines)
│   │   ├── redis/
│   │   │   ├── __init__.py  (5 lines)
│   │   │   ├── cache_manager.py  (297 lines)
│   │   │   └── redis_manager.py  (206 lines)
│   │   ├── sentiment/
│   │   │   ├── __init__.py  (3 lines)
│   │   │   └── sentiment_analyzer.py  (520 lines)
│   │   ├── services/
│   │   │   ├── __pycache__/
│   │   │   ├── optimization_service.py  (258 lines)
│   │   │   └── performance_monitor.py  (225 lines)
│   │   ├── technical_analysis/
│   │   │   ├── __init__.py  (26 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── calculator.py  (214 lines)
│   │   │   ├── enhanced_indicators.py  (515 lines)
│   │   │   ├── indicators.py  (293 lines)
│   │   │   ├── multi_timeframe_analyzer.py  (613 lines)
│   │   │   ├── options_greeks_calculator.py  (221 lines)
│   │   │   ├── signal_generator.py  (109 lines)
│   │   │   ├── strategy_calculator.py  (212 lines)
│   │   │   ├── test_indicators.py  (59 lines)
│   │   │   ├── volume_analyzer.py  (421 lines)
│   │   │   └── zones.py  (1096 lines)
│   │   ├── utils/
│   │   │   ├── __init__.py  (5 lines)
│   │   │   ├── __pycache__/
│   │   │   ├── deprecation.py  (16 lines)
│   │   │   ├── discord_helpers.py  (239 lines)
│   │   │   └── symbol_extraction.py  (158 lines)
│   │   └── watchlist/
│   │       ├── __init__.py  (3 lines)
│   │       ├── __pycache__/
│   │       ├── base_manager.py  (296 lines)
│   │       ├── bot_manager.py  (37 lines)
│   │       ├── models.py  (26 lines)
│   │       ├── supabase_manager.py  (238 lines)
│   │       └── webhook_manager.py  (30 lines)
│   ├── templates/
│   │   ├── __init__.py  (0 lines)
│   │   ├── analysis_response.py  (196 lines)
│   │   ├── ask.py  (872 lines)
│   │   └── core.py  (116 lines)
│   └── utils/
│       └── .gitkeep  (1 lines)
├── start_ai_automation.py  (215 lines)
├── start_bot.py  (52 lines)
├── start_dashboard.py  (82 lines)
├── start_enhanced.sh  (85 lines)
├── start_enhanced_bot.py  (151 lines)
├── system_api.py  (387 lines)
├── system_dashboard.html  (889 lines)
├── temp_logger_fix.py  (16 lines)
├── test_ai_vs_regex.py  (73 lines)
├── test_ask_command.py  (57 lines)
├── test_ast_parsing.py  (135 lines)
├── test_consolidated_architecture.py  (129 lines)
├── test_dashboard.py  (130 lines)
├── test_docker_setup.sh  (49 lines)
├── test_results/
│   ├── comprehensive_test_report.md  (137 lines)
│   ├── detailed_logs/
│   │   ├── ai_enhanced_test_runner.py  (321 lines)
│   │   ├── comprehensive_test_logger.py  (387 lines)
│   │   ├── comprehensive_test_runner.py  (405 lines)
│   │   ├── data_flow_data_OP_000003.json  (19 lines)
│   │   ├── data_flow_data_OP_000037.json  (16 lines)
│   │   ├── data_flow_data_OP_000038.json  (16 lines)
│   │   ├── data_flow_data_OP_000044.json  (16 lines)
│   │   ├── data_flow_data_OP_000050.json  (16 lines)
│   │   ├── data_flow_data_OP_000051.json  (16 lines)
│   │   ├── data_flow_data_OP_000057.json  (220 lines)
│   │   ├── final_test_report_test_session_1757706260.json  (205 lines)
│   │   ├── final_test_report_test_session_1757706366.json  (205 lines)
│   │   ├── pytest_comprehensive_logging.py  (289 lines)
│   │   ├── test_summary_test_session_1757706260.json  (252 lines)
│   │   └── test_summary_test_session_1757706366.json  (252 lines)
│   ├── individual_tests/
│   │   └── test_analysis_results.txt  (227 lines)
│   └── updated_comprehensive_test_report.md  (175 lines)
├── test_supabase_docker.sh  (74 lines)
├── tests/
│   ├── __pycache__/
│   ├── archive/
│   ├── broken/
│   ├── conftest.py  (141 lines)
│   ├── e2e/
│   │   └── test_bot_commands.py  (504 lines)
│   ├── failing_tests.txt  (38 lines)
│   ├── generate_report.py  (70 lines)
│   ├── generate_test_grades.py  (101 lines)
│   ├── integration/
│   │   ├── __pycache__/
│   │   ├── test_alpha_vantage_provider.py  (163 lines)
│   │   ├── test_cross_platform_context.py  (204 lines)
│   │   ├── test_enhanced_ai_context.py  (201 lines)
│   │   ├── test_market_data_service.py  (81 lines)
│   │   ├── test_polygon_provider.py  (85 lines)
│   │   ├── test_qqq_options_estimation.py  (315 lines)
│   │   ├── test_supabase_integration.py  (52 lines)
│   │   └── test_supertrend_analysis.py  (89 lines)
│   ├── load/
│   │   └── test_bot_load.py  (365 lines)
│   ├── run_tests.py  (146 lines)
│   ├── test_advanced_security.py  (96 lines)
│   ├── test_ai_automation.py  (195 lines)
│   ├── test_ai_chat_processor.py  (229 lines)
│   ├── test_ai_debugger_demo.py  (122 lines)
│   ├── test_ai_pipeline.py  (36 lines)
│   ├── test_ai_response.py  (76 lines)
│   ├── test_ai_routing_service_fix.py  (158 lines)
│   ├── test_alpaca_data.py  (146 lines)
│   ├── test_alpha_vantage_config.py  (62 lines)
│   ├── test_alpha_vantage_debug.py  (67 lines)
│   ├── test_analysis.py  (65 lines)
│   ├── test_analyze_pipeline.py  (59 lines)
│   ├── test_ask_command.py  (47 lines)
│   ├── test_ask_pipeline.py  (49 lines)
│   ├── test_ask_pipeline_fix.py  (107 lines)
│   ├── test_async_database.py  (69 lines)
│   ├── test_audit_visualization.py  (374 lines)
│   ├── test_automation.py  (203 lines)
│   ├── test_backward_compatibility.py  (139 lines)
│   ├── test_batch_processing.py  (104 lines)
│   ├── test_bot_real_data.py  (74 lines)
│   ├── test_cache_metrics.py  (180 lines)
│   ├── test_cache_warming.py  (148 lines)
│   ├── test_comprehensive.py  (198 lines)
│   ├── test_comprehensive_pipeline.py  (239 lines)
│   ├── test_comprehensive_symbol_extraction.py  (203 lines)
│   ├── test_config.py  (77 lines)
│   ├── test_config_integration.py  (130 lines)
│   ├── test_consolidated_providers.py  (218 lines)
│   ├── test_contextual_logger.py  (88 lines)
│   ├── test_correlation_integration.py  (138 lines)
│   ├── test_correlation_standalone.py  (127 lines)
│   ├── test_correlation_wrappers.py  (178 lines)
│   ├── test_critical_fixes.py  (221 lines)
│   ├── test_critical_pipeline_fixes.py  (211 lines)
│   ├── test_current_credentials.py  (100 lines)
│   ├── test_data_gap_detection.py  (274 lines)
│   ├── test_data_provider.py  (36 lines)
│   ├── test_data_provider_integration_fix.py  (235 lines)
│   ├── test_data_providers.py  (146 lines)
│   ├── test_data_quality_scoring.py  (340 lines)
│   ├── test_database_connection.py  (24 lines)
│   ├── test_db_connection.py  (45 lines)
│   ├── test_db_connections.py  (51 lines)
│   ├── test_db_manager.py  (108 lines)
│   ├── test_debug_logging.py  (52 lines)
│   ├── test_different_questions.py  (66 lines)
│   ├── test_discord_integration.py  (144 lines)
│   ├── test_discord_interaction.py  (59 lines)
│   ├── test_discord_optimizations.py  (155 lines)
│   ├── test_enhanced_analysis.py  (266 lines)
│   ├── test_enhanced_analysis_mock.py  (191 lines)
│   ├── test_enhanced_engines_only.py  (139 lines)
│   ├── test_enhanced_indicators.py  (276 lines)
│   ├── test_enhanced_stage_only.py  (113 lines)
│   ├── test_execute_ask_pipeline.py  (188 lines)
│   ├── test_execute_ask_pipeline_real_data.py  (112 lines)
│   ├── test_fallback_remediation.py  (162 lines)
│   ├── test_feedback_mechanism.py  (149 lines)
│   ├── test_final_enhancements.py  (262 lines)
│   ├── test_finnhub_direct.py  (100 lines)
│   ├── test_finnhub_fix.py  (58 lines)
│   ├── test_finnhub_provider.py  (80 lines)
│   ├── test_fixed_pipeline.py  (102 lines)
│   ├── test_fixes.py  (103 lines)
│   ├── test_full_analysis.py  (63 lines)
│   ├── test_imports.py  (78 lines)
│   ├── test_integrated_analysis.py  (102 lines)
│   ├── test_live_data.py  (193 lines)
│   ├── test_local_debugger_demo.py  (107 lines)
│   ├── test_market_api.py  (36 lines)
│   ├── test_market_calendar.py  (306 lines)
│   ├── test_market_hours.py  (70 lines)
│   ├── test_market_hours_fix.py  (147 lines)
│   ├── test_message_length_enforcement.py  (141 lines)
│   ├── test_metrics_api.py  (45 lines)
│   ├── test_mock_fix.py  (42 lines)
│   ├── test_multi_symbol_integration.py  (209 lines)
│   ├── test_multi_timeframe.py  (161 lines)
│   ├── test_new_credentials.py  (62 lines)
│   ├── test_optimizations.py  (140 lines)
│   ├── test_options_greeks.py  (104 lines)
│   ├── test_outlier_detection.py  (323 lines)
│   ├── test_performance_optimization.py  (79 lines)
│   ├── test_phase2_improvements.py  (93 lines)
│   ├── test_phase3_improvements.py  (106 lines)
│   ├── test_phase4_improvements.py  (140 lines)
│   ├── test_phase5_improvements.py  (187 lines)
│   ├── test_pipeline_compatibility.py  (93 lines)
│   ├── test_pipeline_fix.py  (85 lines)
│   ├── test_pipeline_monitoring.py  (169 lines)
│   ├── test_pipeline_optimization.py  (291 lines)
│   ├── test_pipeline_optimization_simple.py  (265 lines)
│   ├── test_pipeline_visualization.py  (217 lines)
│   ├── test_pipeline_with_mock_data.py  (179 lines)
│   ├── test_production_deployment.py  (155 lines)
│   ├── test_prompt_system.py  (160 lines)
│   ├── test_provider_attribution.py  (293 lines)
│   ├── test_provider_status.py  (68 lines)
│   ├── test_quick_enhancements.py  (261 lines)
│   ├── test_real_data_providers.py  (240 lines)
│   ├── test_real_data_quality.py  (201 lines)
│   ├── test_recommendation_engine.py  (250 lines)
│   ├── test_redis_docker.py  (42 lines)
│   ├── test_redis_manager.py  (54 lines)
│   ├── test_refactored_bot.py  (34 lines)
│   ├── test_report_engine_mock.py  (65 lines)
│   ├── test_response_audit.py  (0 lines)
│   ├── test_response_depth_fix.py  (149 lines)
│   ├── test_response_generator.py  (78 lines)
│   ├── test_response_template_fix.py  (191 lines)
│   ├── test_results/
│   ├── test_show_full_report.py  (143 lines)
│   ├── test_simple_audit.py  (506 lines)
│   ├── test_simple_correlation.py  (40 lines)
│   ├── test_simple_pipeline_debug.py  (312 lii nes)
│   ├── test_specific_symbol.py  (97 lines)
│   ├── test_stale_data_detection.py  (329 lines)
│   ├── test_supabase_connection.py  (60 lines)
│   ├── test_supply_demand_zones.py  (242 lines)
│   ├── test_suspicious_data_detection.py  (109 lines)
│   ├── test_symbol_extraction_fix.py  (116 lines)
│   ├── test_technical_analysis_integration.py  (79 lines)
│   ├── test_technical_indicators.py  (181 lines)
│   ├── test_template_format_fix_comprehensive.py  (188 lines)
│   ├── test_unified_symbol_extraction.py  (139 lines)
│   ├── test_uniform_alerts.py  (87 lines)
│   ├── test_volume_analyzer.py  (231 lines)
│   ├── test_webhook.py  (43 lines)
│   ├── test_webhook_integration.py  (384 lines)
│   ├── test_webhook_unique.py  (50 lines)
│   ├── test_zone_integration_real_data.py  (309 lines)
│   ├── unit/
│   │   ├── __pycache__/
│   │   ├── test_ml_sentiment_analyzer.py  (164 lines)
│   │   ├── test_options_greeks_calculator.py  (109 lines)
│   │   └── test_strategy_calculator.py  (175 lines)
│   └── working/
├── tradingview-ingest/
│   ├── Dockerfile  (41 lines)
│   ├── algo.pine  (3359 lines)
│   ├── config/
│   │   └── tradingview_config.py  (64 lines)
│   ├── create_analysis_table.sql  (25 lines)
│   ├── create_watchlist_tables.sql  (57 lines)
│   ├── docker-compose.secure.yml  (136 lines)
│   ├── init-db-optimized.sql  (210 lines)
│   ├── init_db.sql  (48 lines)
│   ├── logs/
│   │   └── .gitkeep  (1 lines)
│   ├── migrations/
│   │   ├── 01_create_webhook_tables.sql  (77 lines)
│   │   └── 02_fix_supabase_security_issues.sql  (80 lines)
│   ├── nginx/
│   │   └── webhook.conf  (34 lines)
│   ├── ngrok.yml  (7 lines)
│   ├── requirements/environments/production.txt  (19 lines)
│   ├── requirements.webhook.txt  (7 lines)
│   ├── research_queries.sql  (107 lines)
│   ├── src/
│   │   ├── __init__.py  (1 lines)
│   │   ├── alert_engine.py  (285 lines)
│   │   ├── analyzer.py  (266 lines)
│   │   ├── automated_analyzer.py  (452 lines)
│   │   ├── config.py  (81 lines)
│   │   ├── config_dir/
│   │   │   ├── __init__.py  (20 lines)
│   │   │   ├── config.py  (27 lines)
│   │   │   └── legacy_flags.py  (54 lines)
│   │   ├── data_parser.py  (218 lines)
│   │   ├── database/
│   │   │   ├── __init__.py  (23 lines)
│   │   │   ├── manager.py  (93 lines)
│   │   │   └── operations.py  (311 lines)
│   │   ├── debug/
│   │   │   ├── __init__.py  (100 lines)
│   │   │   ├── database.py  (179 lines)
│   │   │   ├── performance.py  (135 lines)
│   │   │   ├── visualizer.py  (141 lines)
│   │   │   └── webhook_processor.py  (73 lines)
│   │   ├── discord_notifier.py  (154 lines)
│   │   ├── error_handler.py  (226 lines)
│   │   ├── main.py  (347 lines)
│   │   ├── metrics/
│   │   │   ├── __init__.py  (22 lines)
│   │   │   └── legacy_metrics.py  (145 lines)
│   │   ├── models/
│   │   │   ├── __init__.py  (26 lines)
│   │   │   └── base.py  (110 lines)
│   │   ├── models.py  (77 lines)
│   │   ├── parser.py  (331 lines)
│   │   ├── shared/
│   │   │   ├── __init__.py  (4 lines)
│   │   │   ├── database/
│   │   │   └── watchlist/
│   │   │       ├── __init__.py  (3 lines)
│   │   │       └── ingest_watchlist_manager.py  (30 lines)
│   │   ├── simple_ingestion.py  (178 lines)
│   │   ├── storage_manager.py  (897 lines)
│   │   ├── supabase_client.py  (70 lines)
│   │   ├── visualizer.py  (325 lines)
│   │   ├── watchlist_manager.py  (15 lines)
│   │   ├── webhook_core.py  (197 lines)
│   │   ├── webhook_processor.py  (368 lines)
│   │   └── webhook_receiver.py  (352 lines)
│   ├── start_ngrok.sh  (36 lines)
│   ├── start_secure_system.sh  (95 lines)
│   ├── test_crypto_simple.py  (123 lines)
│   ├── test_high_volume.py  (215 lines)
│   ├── test_multi_ticker.py  (204 lines)
│   ├── test_uniform_alerts.py  (87 lines)
│   ├── test_webhook.py  (129 lines)
│   └── tests/
│       ├── load_test_no_legacy.py  (161 lines)
│       ├── test_config.py  (61 lines)
│       ├── test_external_integrations.py  (154 lines)
│       ├── test_legacy_deprecation.py  (76 lines)
│       └── test_no_legacy_data_processing.py  (88 lines)
├── unified_docs/
│   ├── DOCKER_CONSOLIDATION_PLAN.md  (154 lines)
│   ├── DOCKER_SUMMARY.md  (86 lines)
│   ├── PROJECT_OVERVIEW.md  (242 lines)
│   └── documentation_index.md  (28 lines)
├── visualize_recent_query.py  (125 lines)
├── working_db_solution.py  (100 lines)
├── working_solution.py  (205 lines)
└── working_with_existing_tables.py  (136 lines)
```

### Audit Notes

Use this section to add detailed notes on specific files or directories.

- **`src/`**: **CRITICAL AUDIT FINDINGS**
  - **Large files requiring attention:**
    - `data_source_manager.py` (1,179 lines) - Complex data provider management
    - `client_original.py` (1,378 lines) - Legacy client implementation
    - `client.py` (1,117 lines) - Current client implementation
    - `probability_engine.py` (607 lines) - Complex probability calculations
    - `timeframe_confirmation.py` (541 lines) - Technical analysis logic
    - `parallel_pipeline.py` (568 lines) - Pipeline orchestration
    - `ask_sections.py` (801 lines) - Large command handler
    - `conversation_memory_service.py` (420 lines) - AI memory management
  
  - **Architecture concerns:**
    - Multiple client implementations suggest refactoring needed
    - Complex pipeline structure with nested stages
    - Heavy AI integration across multiple modules
    - Extensive analysis modules (technical, fundamental, risk, probability)

- **`backups/stub-removed/`**: **LEGACY CODE ALERT**
  - Contains original implementations of core commands
  - `ask_original.py` (252 lines) vs current `ask.py` (388 lines)
  - Suggests significant refactoring has occurred
  - May contain deprecated patterns or security issues

- **Documentation proliferation**: **MAINTENANCE CONCERN**
  - 6 "FINAL_" summary files suggest multiple completion attempts
  - 50+ markdown files in root directory
  - Multiple audit reports and fix summaries
  - Indicates complex development history with many iterations

- **`architecture_detailed.json`** (4,068 lines): **SYSTEM COMPLEXITY**
  - Massive architecture documentation
  - Suggests highly complex system design
  - May indicate over-engineering or need for simplification

- **`dashboard/`**: **MONITORING INFRASTRUCTURE**
  - Complete monitoring and visualization system
  - `system_dashboard.html` (990 lines) - Large dashboard implementation
  - Pipeline monitoring and analysis tools
  - Indicates production-ready monitoring capabilities

- **`tests/`**: **TESTING COVERAGE**
  - Comprehensive test suite across multiple areas
  - Load testing, integration testing, legacy deprecation tests
  - Suggests mature testing practices

- **Security concerns:**
  - Multiple security-related files and reports
  - `SECURITY_AUDIT_REPORT.md` present
  - Security middleware and utilities implemented
  - Suggests security has been a focus area

- **Docker infrastructure:**
  - Complete Docker setup with production configs
  - Health monitoring and deployment automation
  - Suggests containerized deployment strategy
