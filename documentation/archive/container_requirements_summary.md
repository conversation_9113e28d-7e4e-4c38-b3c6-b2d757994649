# TradingView Automation System - Container Requirements Summary

## Overview
This document provides a summary of all containers in the TradingView Automation system and their interdependencies.

## Container List

1. [API Container](./containers/api_container_requirements.md) - Provides REST endpoints for data access and webhook processing
2. [Discord Bot Container](./containers/discord_bot_container_requirements.md) - Processes commands and sends notifications to Discord channels
3. [Redis Container](./containers/redis_container_requirements.md) - Provides caching and message queuing services
4. [Webhook Ingest Container](./containers/webhook_ingest_container_requirements.md) - Receives and processes TradingView alerts

## Network Architecture

### Networks
1. **tradingview-network** - Main network connecting all services
2. **internal-network** - Internal services network for core components
3. **external-network** - External communication network for Discord bot

### Network Isolation
- Core services (API, Redis, Webhook Ingest) are isolated on internal-network
- Discord bot has access to both internal and external networks
- Redis is accessible from both internal-network and tradingview-network

## Service Dependencies

```
API Container
├── Depends on Redis (health check)
└── Connects to Database (Supabase)

Discord Bot Container
├── Connects to Redis
├── Connects to Database (Supabase)
└── Uses external APIs (Polygon, Finnhub, etc.)

Redis Container
├── Required by API Container
├── Required by Discord Bot Container
└── Required by Webhook Ingest Container

Webhook Ingest Container
├── Depends on Redis (health check)
└── Connects to Database (Supabase)
```

## Data Flow

1. **TradingView Alerts** → Webhook Ingest Container → Redis/Database → Discord Bot Container → Discord
2. **Discord Commands** → Discord Bot Container → External APIs/Database → Discord
3. **API Requests** → API Container → Database/Redis → External APIs → Response

## Security Architecture

- All containers use environment variables for sensitive configuration
- Redis requires password authentication
- Webhook Ingest validates payloads with WEBHOOK_SECRET
- JWT-based authentication for API endpoints
- Discord bot token is secured in environment variables
- Non-root user execution for Webhook Ingest container

## Monitoring and Health Checks

- All containers have health check mechanisms
- Redis uses `redis-cli ping` for health checks
- API and Webhook Ingest use HTTP endpoint checks
- Discord Bot has custom Python health check
- Prometheus metrics collection available
- Structured logging with structlog

## Performance Considerations

- Lightweight Alpine image for Redis
- Multi-stage builds for Python containers
- Volume mounting for code hot-reloading in development
- Read-only configuration volumes where appropriate
- Efficient inter-container communication through Docker networks