# Webhook Ingest Container Requirements

## Overview
The Webhook Ingest container receives and processes TradingView alerts. It serves as the entry point for automated trading signals from TradingView and forwards them to the appropriate processing services.

## Container Configuration
- **Service Name**: webhook-ingest
- **Container Name**: tradingview-webhook-ingest-dev
- **Base Image**: Custom Python 3.11-slim image (multi-stage build)
- **Port**: 8001
- **Command**: `uvicorn src.webhook_receiver:app --host 0.0.0.0 --port 8001`

## Build Context
- **Context**: ./tradingview-ingest
- **Dockerfile**: ./docker/dockerfiles/services/tradingview-ingest.Dockerfile

## Environment Variables
- ENVIRONMENT=development
- DEBUG=true
- PYTHONPATH=/app
- LOG_LEVEL=INFO
- REDIS_URL=redis://${REDIS_PASSWORD}@redis:6379/0
- DATABASE_URL=${DATABASE_URL}
- SUPABASE_URL=${SUPABASE_URL}
- SUPABASE_KEY=${SUPABASE_KEY}
- USE_SUPABASE=true
- WEBHOOK_SECRET=${WEBHOOK_SECRET}
- REDIS_PASSWORD=${REDIS_PASSWORD}

## Dependencies
Dependencies are defined in tradingview-ingest/requirements/environments/production.txt:
- FastAPI == 0.104.1
- Uvicorn[standard] == 0.24.0
- Pydantic == 2.5.0
- Pydantic-settings == 2.1.0
- Redis == 5.0.1
- Psycopg2-binary == 2.9.9
- Asyncpg == 0.29.0
- SQLAlchemy == 2.0.23
- Alembic == 1.13.1
- Pandas == 2.1.4
- Numpy == 1.25.2
- Python-multipart == 0.0.6
- HTTPX == 0.25.2
- Python-jose[cryptography] == 3.3.0
- Passlib[bcrypt] == 1.7.4
- Python-dotenv == 1.0.0
- Structlog == 23.2.0
- Prometheus-client == 0.19.0
- Aiohttp == 3.9.1

## Volumes
- ./tradingview-ingest/src:/app/src (Source code)
- ./tradingview-ingest/config:/app/config:ro (Configuration files - read-only)
- ./logs:/app/logs (Log files)

## Networks
- internal-network (Internal services)
- tradingview-network (Main network)

## Health Check
- Test: `curl -f http://localhost:8001/health`
- Interval: 30s
- Timeout: 10s
- Retries: 3
- Start period: 40s

## Key Responsibilities
1. Receive webhook payloads from TradingView
2. Validate webhook signatures using WEBHOOK_SECRET
3. Parse and process alert data
4. Store alerts in the database
5. Forward alerts to Redis for further processing
6. Provide health check endpoint
7. Handle authentication and authorization for webhook endpoints
8. Log all webhook activities for monitoring and debugging

## Required Services
- Redis (caching and messaging)
- Database (Supabase/PostgreSQL)

## Security Considerations
- Webhook secret validation for payload authenticity
- Secure communication with Redis using password authentication
- Environment variables for sensitive configuration
- Non-root user execution (appuser) for security isolation

## DNS Configuration
- Uses Google DNS (*******) and Cloudflare DNS (*******)

## Performance Considerations
- Multi-stage Docker build for smaller image size
- Non-root user execution
- Health checks for service monitoring
- Efficient parsing and processing of webhook data