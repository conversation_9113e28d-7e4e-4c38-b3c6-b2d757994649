📌 Task List: Code Deduplication & Security Remediation

This document tracks actionable tasks for addressing code duplication and critical security issues in the tradingview-automation codebase.
Refer to the full remediation plan (Code Deduplication and Security Remediation Plan.md) for strategy and rationale.

✅ Phase 0: Critical Security Fixes (Week 1)

Goal: Patch urgent vulnerabilities before deduplication work.

🔐 Security

 Webhook URL hardcoding

Move DEFAULT_WEBHOOK_URL from src/bot/setup_audit.py to config/env

Owner: @security-engineer | Backup: @lead-developer

 Verification code storage

Replace in-memory dict in src/core/advanced_security.py with secure cache (Redis)

Owner: @security-engineer

 Role-based access control

Replace placeholder UserRole.VIEWER with actual role fetch

Owner: @security-engineer

 Adaptive rate limiter

Fix flawed recovery logic in src/core/advanced_security.py

Owner: @backend-engineer

 Rate limiting storage

Replace in-memory limiter (src/bot/audit/rate_limiter.py) with distributed cache

Owner: @backend-engineer

⚙️ Implementation

 Circular dependency

Fix in src/api/data/cache_warming_scheduler.py by moving constants → constants.py

Owner: @backend-engineer

📂 Phase 1: Preparation & Analysis (Week 1)

Goal: Set up canonical structure, compatibility shims, and metrics.

 Create canonical dirs

/src/shared/ai_services/

/src/shared/market_analysis/

 Backward compatibility stubs

Add import + DeprecationWarning to old module paths

 Usage metrics

Increment counters when deprecated modules are imported

 Database connection imports

Add missing async imports to /src/database/connection.py

 Confirm test coverage

Identify missing tests for AI Service, Signal Analysis, and DB connection

🔄 Phase 2: Implementation (Weeks 2–3)

Goal: Move duplicated code to canonical locations and update imports.

 AI Service Components

Move ai_service_wrapper.py + ai_chat_processor.py → /src/shared/ai_services/

Update all imports

Keep stubs at old locations

 Signal Analysis

Keep /src/shared/market_analysis/signal_analyzer.py as canonical

Remove duplicate logic from ai_chat_processor.py

Update imports

 Pipeline Structure

Merge /src/bot/pipeline/ask/ into /src/bot/pipeline/commands/

Move unique functionality, remove duplicates

 Tests

Update imports in test files

Add tests to ensure stubs work

🧪 Phase 3: Testing & Validation (Week 4)

Goal: Verify correctness and performance after consolidation.

 Run full unit + integration + E2E tests

 Verify canonical imports only

 Performance benchmarking:

Response times

Memory usage

Throughput under load

 Validate rate limiter + security fixes in production-like environment

🗑️ Phase 4: Gradual Removal (Weeks 5–6)

Goal: Remove duplicated code while monitoring impact.

 Remove duplicate code from /src/bot/pipeline/ask/

 Keep backward compatibility stubs active

 Update developer documentation to reflect canonical imports

 Monitor metrics for deprecated imports

 Send dev-team announcement of upcoming removals

🧹 Phase 5: Final Cleanup (Week 7)

Goal: Finish deduplication and remove deprecated code.

 Remove all backward compatibility stubs

 Run full regression test suite

 Confirm no imports from deprecated modules

 Final update to architecture docs

 Tag release post-dedup-v1.0



















# Code Deduplication and Security Remediation Plan

This document outlines a comprehensive plan for addressing code duplication issues and critical security vulnerabilities in the tradingview-automation codebase.

## Identified Duplication Issues

1. **AI Service Components**
   - `/src/bot/pipeline/ask/stages/ai_service_wrapper.py`
   - `/src/bot/pipeline/commands/ask/stages/ai_service_wrapper.py`
   - `/src/bot/pipeline/ask/stages/ai_chat_processor.py`
   - `/src/bot/pipeline/commands/ask/stages/ai_chat_processor.py`

2. **Signal Analysis**
   - Duplicated between `/src/bot/pipeline/commands/ask/stages/ai_chat_processor.py` and `/src/shared/market_analysis/signal_analyzer.py`

3. **Pipeline Structure Duplication**
   - `/src/bot/pipeline/ask/` vs `/src/bot/pipeline/commands/ask/`
   - Similar stages and processing logic duplicated across different pipeline paths

4. **Database Connection Issues**
   - Missing imports in `/src/database/connection.py` for async functionality

## Critical Security and Implementation Issues

1. **Security Vulnerabilities**
   - **Critical**: Hardcoded webhook URL in `src/bot/setup_audit.py`
   - **Critical**: Insecure in-memory storage of verification codes in `src/core/advanced_security.py`
   - **Critical**: Placeholder role in role_required decorator in `src/core/advanced_security.py`
   - **Major**: In-memory storage for rate limiting in `src/bot/audit/rate_limiter.py`

2. **Implementation Flaws**
   - **Critical**: Circular dependency in `src/api/data/cache_warming_scheduler.py`
   - **Critical**: Flawed adaptive rate limiter in `src/core/advanced_security.py`
   - **Major**: Synchronous database driver in an asynchronous application in `src/database/connection.py`
   - **Major**: Hardcoded data providers in `src/bot/pipeline/commands/ask/stages/ai_service_wrapper.py`
   - **Major**: Inefficient aiohttp.ClientSession usage in `src/bot/audit/request_visualizer.py`
   - **Major**: Use of asyncio.get_event_loop() and loop.run_until_complete() in `src/bot/client_audit_integration.py`

## Code Ownership

To ensure clear accountability and coordination, specific owners should be assigned to each component:

| Component | Owner | Backup |
|-----------|-------|--------|
| AI Service Components | @lead-developer | @backend-engineer |
| Signal Analysis | @data-engineer | @backend-engineer |
| Pipeline Structure | @lead-developer | @devops-engineer |
| Database Connection | @backend-engineer | @lead-developer |
| Security Vulnerabilities | @security-engineer | @lead-developer |
| Implementation Flaws | @backend-engineer | @data-engineer |

Each owner is responsible for implementing changes, testing, and documentation for their assigned component.

## Canonical Locations

Based on the codebase structure, here are the recommended canonical locations for each duplicated component:

1. **AI Service Components**:
   - Canonical: `/src/shared/ai_services/`
   - Move both `ai_service_wrapper.py` and `ai_chat_processor.py` here

2. **Signal Analysis**:
   - Canonical: `/src/shared/market_analysis/signal_analyzer.py`
   - Keep the implementation here and remove duplicates

3. **Pipeline Structure**:
   - Canonical: `/src/bot/pipeline/commands/`
   - Consolidate `/src/bot/pipeline/ask/` into this structure

4. **Database Connection**:
   - Fix imports in existing location: `/src/database/connection.py`

## Security and Implementation Remediation Plan

### Phase 0: Critical Security Fixes (1 week)

1. **Fix Hardcoded Credentials and URLs**
   - Move hardcoded webhook URL in `src/bot/setup_audit.py` to environment variables or secure configuration
   - Example:
     ```python
     # Before
     DEFAULT_WEBHOOK_URL = "https://hooks.example.com/services/T00000000/B00000000/XXXXXXXXXXXXXXXXXXXXXXXX"
     
     # After
     from src.core.config_manager import get_config
     DEFAULT_WEBHOOK_URL = get_config().get('audit', 'webhook_url', '')
     ```

2. **Fix In-Memory Storage for Sensitive Data**
   - Replace in-memory dictionary in `src/core/advanced_security.py` with Redis or another secure distributed cache
   - Example:
     ```python
     # Before
     self._verification_codes = {}
     
     # After
     from src.core.cache import get_secure_cache
     self._verification_cache = get_secure_cache('verification_codes')
     ```

3. **Fix Role-Based Access Control**
   - Replace placeholder role in `src/core/advanced_security.py` with proper role retrieval
   - Example:
     ```python
     # Before
     current_user_role = UserRole.VIEWER  # Placeholder
     
     # After
     current_user_role = get_current_user_role(request)
     ```

4. **Fix Circular Dependencies**
   - Resolve circular dependency in `src/api/data/cache_warming_scheduler.py`
   - Move shared constants to a separate module
   - Example:
     ```python
     # Before - in cache.py
     TOP_SYMBOLS = [...]
     
     # After - in constants.py
     TOP_SYMBOLS = [...]
     
     # In both cache.py and cache_warming_scheduler.py
     from src.api.data.constants import TOP_SYMBOLS
     ```

5. **Fix Flawed Rate Limiter**
   - Correct the adaptive rate limiter in `src/core/advanced_security.py` to properly increase limits
   - Example:
     ```python
     # Add recovery logic
     def _recover_rate_limit(self):
         if self.current_limit < self.base_limit:
             self.current_limit = min(self.current_limit * 1.5, self.base_limit)
     ```

## Deduplication Strategy

### Phase 1: Preparation and Analysis (1 week)

1. **Create Backward Compatibility Modules**
   - Create stub modules at original locations that import from canonical locations
   - Add deprecation warnings to these modules
   - Example:
     ```python
     # src/bot/pipeline/ask/stages/ai_service_wrapper.py
     import warnings
     warnings.warn(
         "This module is deprecated. Import from src.shared.ai_services.ai_service_wrapper instead.",
         DeprecationWarning,
         stacklevel=2
     )
     from src.shared.ai_services.ai_service_wrapper import *
     ```

2. **Add Usage Metrics**
   - Add metrics to track usage of deprecated modules
   - Example: `deprecated_import_counter.inc()`
   - Use these metrics to determine if it's safe to remove the code

3. **Create Directory Structure**
   - Create the canonical directory structure
   - Example: `mkdir -p /src/shared/ai_services/`

4. **Fix Database Connection Imports**
   - Add missing imports to `/src/database/connection.py`:
     ```python
     import asyncio
     from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession, create_async_engine
     from contextlib import asynccontextmanager
     from typing import AsyncGenerator
     ```

### Phase 2: Implementation (2 weeks)

1. **Move AI Service Components**
   - Move `ai_service_wrapper.py` and `ai_chat_processor.py` to `/src/shared/ai_services/`
   - Update imports in all files that reference these modules
   - Create backward compatibility modules at original locations

2. **Consolidate Signal Analysis**
   - Ensure `/src/shared/market_analysis/signal_analyzer.py` has all functionality
   - Remove duplicated code from `/src/bot/pipeline/commands/ask/stages/ai_chat_processor.py`
   - Update imports in all files that reference this module

3. **Consolidate Pipeline Structure**
   - Move unique functionality from `/src/bot/pipeline/ask/` to `/src/bot/pipeline/commands/ask/`
   - Create backward compatibility modules at original locations

4. **Update Tests**
   - Update all tests to use canonical imports
   - Add tests to verify backward compatibility modules work correctly

### Phase 3: Testing and Validation (1 week)

1. **Run Comprehensive Tests**
   - Run all unit tests, integration tests, and end-to-end tests
   - Verify that all functionality works correctly
   - Fix any issues that arise

2. **Validate Import Paths**
   - Verify that all imports are using canonical paths
   - Check for any remaining references to deprecated modules

3. **Performance Testing**
   - Verify that performance is maintained or improved
   - Check for any regressions in response time or resource usage

### Phase 4: Gradual Removal (1-2 weeks)

1. **Remove Duplicated Code**
   - Remove duplicated code from original locations
   - Keep backward compatibility modules for now

2. **Update Documentation**
   - Update all documentation to reference canonical modules
   - Document the new structure and import paths

3. **Monitor Usage**
   - Monitor usage of backward compatibility modules
   - Identify any remaining code that needs to be updated

### Phase 5: Final Cleanup (1 week)

1. **Remove Backward Compatibility Modules**
   - Remove backward compatibility modules
   - Verify that all code is using canonical imports

2. **Final Testing**
   - Run comprehensive tests to ensure everything still works
   - Verify that all metrics and logs are clean

## Version Control Strategy

To maintain clear history and enable easy rollbacks if needed, we'll follow this version control strategy:

1. **Main Branch Protection**
   - Protect the main branch to prevent direct commits
   - Require code reviews for all pull requests

2. **Feature Branches**
   - Create dedicated feature branches for each phase:
     - `dedup/phase1-preparation`
     - `dedup/phase2-implementation`
     - `dedup/phase3-testing`
     - `dedup/phase4-removal`
     - `dedup/phase5-cleanup`

3. **Tagging Strategy**
   - Tag the codebase before starting each phase: `pre-dedup-v1.0`
   - Tag after each phase completion: `post-dedup-phase1-v1.1`

4. **Pull Request Templates**
   - Use PR templates that include deduplication checklist
   - Require test results and metrics in PR descriptions

## Risk Mitigation

1. **Rollback Plan**
   - Keep backups of all moved code
   - Maintain ability to quickly restore original structure if issues arise
   - Create rollback scripts for each phase

2. **Gradual Deployment**
   - Deploy changes incrementally
   - Monitor each deployment for issues before proceeding
   - Use feature flags to enable quick disabling of problematic changes

3. **Communication**
   - Inform all team members about the deduplication effort
   - Provide clear documentation on new import paths
   - Schedule regular status updates throughout the process

## Timeline

- **Phase 1**: Week 1
- **Phase 2**: Weeks 2-3
- **Phase 3**: Week 4
- **Phase 4**: Weeks 5-6
- **Phase 5**: Week 7

Total estimated time: 7 weeks
