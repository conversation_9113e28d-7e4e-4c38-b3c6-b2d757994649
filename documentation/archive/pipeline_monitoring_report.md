# Pipeline Monitoring Report

Generated on: 2025-09-11 16:20:15

Total pipelines analyzed: 8

## Pipeline Summary

| Pipeline | Grade | Score | Success Rate | Execution Time (s) |
|----------|-------|-------|--------------|-------------------|
| complex_pipeline | A- | 90.3 | 92.3% | 15.97 |
| complex_pipeline | A- | 90.2 | 92.3% | 12.45 |
| high_quality_pipeline | A+ | 98.8 | 100.0% | 5.45 |
| high_quality_pipeline | A+ | 98.5 | 100.0% | 6.07 |
| low_quality_pipeline | F | 44.2 | 25.0% | 4.82 |
| low_quality_pipeline | F | 58.7 | 50.0% | 5.34 |
| medium_quality_pipeline | C+ | 78.3 | 75.0% | 5.23 |
| medium_quality_pipeline | A | 94.0 | 100.0% | 5.22 |

## Step Details

### complex_pipeline

| Step | Grade | Score | Success | Execution Time (s) |
|------|-------|-------|---------|-------------------|
| initialize | F | 0.0 | ❌ | 0.58 |
| fetch_market_data | B | 83.7 | ✅ | 0.84 |
| validate_data | B | 85.8 | ✅ | 1.21 |
| normalize_data | B+ | 87.6 | ✅ | 1.85 |
| technical_analysis | B | 85.1 | ✅ | 0.67 |
| fundamental_analysis | B | 84.0 | ✅ | 1.40 |
| sentiment_analysis | B | 84.6 | ✅ | 1.72 |
| combine_signals | B | 86.2 | ✅ | 1.43 |
| generate_recommendations | B | 84.4 | ✅ | 0.39 |
| format_results | B | 83.6 | ✅ | 1.67 |
| store_results | B | 86.4 | ✅ | 1.94 |
| notify_users | B- | 80.0 | ✅ | 1.10 |
| cleanup | B- | 82.1 | ✅ | 1.18 |


### complex_pipeline

| Step | Grade | Score | Success | Execution Time (s) |
|------|-------|-------|---------|-------------------|
| initialize | C+ | 79.3 | ✅ | 0.31 |
| fetch_market_data | B | 84.4 | ✅ | 1.70 |
| validate_data | B- | 82.0 | ✅ | 0.78 |
| normalize_data | B | 83.5 | ✅ | 0.91 |
| technical_analysis | F | 0.0 | ❌ | 1.00 |
| fundamental_analysis | B | 86.0 | ✅ | 0.27 |
| sentiment_analysis | B- | 82.7 | ✅ | 1.47 |
| combine_signals | B | 83.5 | ✅ | 1.01 |
| generate_recommendations | B | 85.4 | ✅ | 0.99 |
| format_results | B | 84.1 | ✅ | 0.92 |
| store_results | B | 83.2 | ✅ | 0.91 |
| notify_users | B | 85.4 | ✅ | 0.93 |
| cleanup | B+ | 87.8 | ✅ | 1.27 |


### high_quality_pipeline

| Step | Grade | Score | Success | Execution Time (s) |
|------|-------|-------|---------|-------------------|
| fetch_data | A+ | 97.7 | ✅ | 0.59 |
| process_data | A | 93.5 | ✅ | 1.74 |
| analyze_results | A | 95.1 | ✅ | 1.52 |
| generate_report | A+ | 97.4 | ✅ | 1.59 |


### high_quality_pipeline

| Step | Grade | Score | Success | Execution Time (s) |
|------|-------|-------|---------|-------------------|
| fetch_data | A- | 92.9 | ✅ | 1.34 |
| process_data | A | 96.2 | ✅ | 1.52 |
| analyze_results | A | 96.0 | ✅ | 1.45 |
| generate_report | A | 94.9 | ✅ | 1.76 |


### low_quality_pipeline

| Step | Grade | Score | Success | Execution Time (s) |
|------|-------|-------|---------|-------------------|
| fetch_data | F | 0.0 | ❌ | 0.72 |
| process_data | F | 0.0 | ❌ | 1.53 |
| analyze_results | F | 0.0 | ❌ | 0.81 |
| generate_report | F | 56.0 | ✅ | 1.76 |


### low_quality_pipeline

| Step | Grade | Score | Success | Execution Time (s) |
|------|-------|-------|---------|-------------------|
| fetch_data | F | 0.0 | ❌ | 1.63 |
| process_data | D- | 61.5 | ✅ | 1.10 |
| analyze_results | F | 0.0 | ❌ | 0.67 |
| generate_report | F | 54.7 | ✅ | 1.93 |


### medium_quality_pipeline

| Step | Grade | Score | Success | Execution Time (s) |
|------|-------|-------|---------|-------------------|
| fetch_data | B- | 82.9 | ✅ | 1.26 |
| process_data | B- | 82.0 | ✅ | 1.08 |
| analyze_results | C+ | 78.9 | ✅ | 1.57 |
| generate_report | F | 0.0 | ❌ | 1.32 |


### medium_quality_pipeline

| Step | Grade | Score | Success | Execution Time (s) |
|------|-------|-------|---------|-------------------|
| fetch_data | C+ | 78.7 | ✅ | 0.26 |
| process_data | B- | 80.2 | ✅ | 1.33 |
| analyze_results | C+ | 78.5 | ✅ | 1.72 |
| generate_report | B- | 82.0 | ✅ | 1.90 |

