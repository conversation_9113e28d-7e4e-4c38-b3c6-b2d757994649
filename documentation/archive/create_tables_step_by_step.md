# Step-by-Step Table Creation Guide

## Method 1: Using Supabase Table Editor (Easiest)

1. **Go to Supabase Dashboard**: https://supabase.com/dashboard
2. **Select your project**: sgxjackuhalscowqrulv
3. **Navigate to**: Table Editor (left sidebar)
4. **Click**: "Create a new table"

### Create Table 1: trading_alerts
- **Table name**: `trading_alerts`
- **Columns**:
  - `id` - int8 - Primary Key - Auto-increment
  - `symbol` - varchar - Not null
  - `alert_type` - varchar - Not null  
  - `price` - numeric
  - `timestamp` - timestamptz - Default: now()
  - `timeframe` - varchar
  - `signal_strength` - varchar
  - `created_at` - timestamptz - Default: now()
  - `updated_at` - timestamptz - Default: now()

### Create Table 2: webhook_alerts
- **Table name**: `webhook_alerts`
- **Columns**:
  - `id` - int8 - Primary Key - Auto-increment
  - `symbol` - varchar - Not null
  - `signal` - varchar - Not null
  - `timestamp` - int8
  - `timeframe` - varchar
  - `entry_price` - numeric
  - `tp1_price` - numeric
  - `tp2_price` - numeric
  - `tp3_price` - numeric
  - `sl_price` - numeric
  - `raw_text` - text
  - `created_at` - timestamptz - Default: now()

### Create Table 3: analysis_history
- **Table name**: `analysis_history`
- **Columns**:
  - `id` - int8 - Primary Key - Auto-increment
  - `user_id` - varchar - Not null
  - `symbol` - varchar - Not null
  - `query_text` - text - Not null
  - `analysis_type` - varchar - Not null
  - `ai_response` - text - Not null
  - `confidence_score` - numeric
  - `processing_time_ms` - int4
  - `created_at` - timestamptz - Default: now()
  - `expires_at` - timestamptz - Default: now() + interval '7 days'

### Create Table 4: user_preferences
- **Table name**: `user_preferences`
- **Columns**:
  - `id` - int8 - Primary Key - Auto-increment
  - `user_id` - varchar - Not null - Unique
  - `default_timeframe` - varchar - Default: '1d'
  - `risk_tolerance` - varchar - Default: 'moderate'
  - `timezone` - varchar - Default: 'UTC'
  - `currency` - varchar - Default: 'USD'
  - `theme` - varchar - Default: 'dark'
  - `created_at` - timestamptz - Default: now()
  - `updated_at` - timestamptz - Default: now()

## Method 2: SQL Editor (If Method 1 doesn't work)

1. **Go to**: SQL Editor (left sidebar)
2. **Paste this simplified SQL**:

```sql
-- Create trading_alerts table
CREATE TABLE trading_alerts (
    id BIGSERIAL PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    alert_type VARCHAR(50) NOT NULL,
    price DECIMAL(15,4),
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    timeframe VARCHAR(20),
    signal_strength VARCHAR(20),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

3. **Click "Run"**
4. **Repeat for each table** (one at a time)

## Method 3: Check Permissions

If both methods fail:
1. **Go to**: Settings → API
2. **Check**: service_role key (not anon key)
3. **Use service_role key** for table creation
4. **Switch back to anon key** for normal operations

## Verification

After creating tables, run this to verify:
```sql
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;
```
