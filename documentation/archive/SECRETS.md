# Secrets Management

## Overview
This project uses environment variables for development mode. All sensitive data is stored in `.env` files and loaded into containers at runtime.

## Development Mode (.env)
- **File**: `.env` (not committed to Git)
- **Usage**: Local development and testing
- **Security**: File is gitignored, contains all API keys and secrets
- **Format**: Simple key=value pairs

## Required Environment Variables
All of these must be set in your `.env` file:

### Redis
- `REDIS_PASSWORD` - Redis authentication password

### API Keys
- `SUPABASE_KEY` - Supabase database access
- `DISCORD_BOT_TOKEN` - Discord bot authentication
- `OPENROUTER_API_KEY` - AI service access
- `POLYGON_API_KEY` - Market data provider
- `FINNHUB_API_KEY` - Financial data provider
- `ALPACA_API_KEY` - Trading platform access

### Security
- `JWT_SECRET` - JWT token signing for API authentication

## Best Practices
1. **Never commit .env to Git** - File is gitignored
2. **Use strong passwords** - Generate secure values for each secret
3. **Keep .env.local** - Use for environment-specific overrides
4. **Rotate regularly** - Update secrets periodically
5. **Use .env.example** - Template for team members

## File Structure
```
.env                    # Your actual secrets (gitignored)
.env.example           # Template with placeholders
.env.local             # Local overrides (optional)
```

## Updating Secrets
To update any secret:
1. Edit the corresponding value in `.env`
2. Restart the affected container(s)
3. Verify the new secret is working

## Production Notes
For production deployment:
- Use cloud secrets manager (AWS, GCP, etc.)
- Inject as environment variables
- Never use .env files in production
- Implement proper key rotation

## Security Notes
- All secrets are loaded as environment variables
- No file-based secrets in development mode
- Environment variables are accessible to the application
- Use strong, unique values for each environment
