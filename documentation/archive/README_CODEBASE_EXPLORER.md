# Codebase Explorer Dashboard

A comprehensive system for visualizing and monitoring your trading bot application. This dashboard provides real-time insights into the architecture, pipelines, configuration, and system health.

## Components

### 1. Architecture Analysis (`generate_architecture.py`)

Generates a structured representation of your codebase using AST parsing to extract metadata about commands, pipelines, and file structure.

**Features:**
- AST-based parsing of Python files
- Command metadata extraction (names, descriptions, parameters)
- File structure mapping
- Pipeline stage identification

**Usage:**
```bash
python generate_architecture.py
```

This generates `architecture.json` and `architecture_detailed.json` files that can be used by the dashboard.

### 2. Configuration API (`config_api.py`)

Provides a secure API endpoint for viewing application configuration with sensitive data masked.

**Features:**
- Environment variable exposure
- YAML configuration file parsing
- Automatic masking of sensitive keys (API keys, passwords, etc.)
- Search functionality

**Endpoints:**
- `GET /api/system/config` - Get all configuration
- `GET /api/system/config/{key}` - Get specific configuration item
- `GET /api/system/config/search/{pattern}` - Search configuration

**Usage:**
```bash
uvicorn config_api:app --host 0.0.0.0 --port 8001
```

### 3. Pipeline Events WebSocket Server (`pipeline_events.py`)

Real-time pipeline visualization through WebSocket events.

**Features:**
- WebSocket-based event streaming
- Pipeline and stage lifecycle events
- Metric updates and logging
- Subscription management

**Event Types:**
- `pipeline_started`
- `pipeline_completed`
- `pipeline_failed`
- `stage_started`
- `stage_completed`
- `stage_failed`
- `metric_update`
- `log_message`

**Usage:**
```bash
uvicorn pipeline_events:app --host 0.0.0.0 --port 8002
```

**WebSocket Connection:**
```javascript
const ws = new WebSocket('ws://localhost:8002/ws');
ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('Pipeline event:', data);
};
```

### 4. System Monitoring API (`system_api.py`)

API for monitoring system components like circuit breakers and cache.

**Features:**
- Circuit breaker status monitoring
- Cache inspection and management
- Health status reporting
- Reset functionality

**Endpoints:**
- `GET /api/system/circuit_breakers` - Get all circuit breakers
- `GET /api/system/circuit_breakers/{name}` - Get specific circuit breaker
- `POST /api/system/circuit_breakers/{name}/reset` - Reset circuit breaker
- `GET /api/system/cache/info` - Get cache information
- `GET /api/system/cache/keys` - List cache keys
- `GET /api/system/cache/items` - Get cache items
- `GET /api/system/cache/items/{key}` - Get specific cache item
- `DELETE /api/system/cache/items/{key}` - Delete cache item
- `POST /api/system/cache/clear` - Clear cache
- `GET /api/system/health` - Get system health

**Usage:**
```bash
uvicorn system_api:app --host 0.0.0.0 --port 8003
```

### 5. Dashboard UI (`system_dashboard.html`)

Interactive web-based dashboard for visualizing all system components.

**Features:**
- Architecture map visualization
- Pipeline flow diagrams
- Real-time event streaming
- System health monitoring
- Circuit breaker status
- Cache statistics

**Usage:**
Simply open `system_dashboard.html` in a web browser, or serve it with any web server.

## Installation

1. Install dependencies:
```bash
pip install fastapi uvicorn pyyaml
```

2. Run the services:
```bash
# Terminal 1: Configuration API
uvicorn config_api:app --host 0.0.0.0 --port 8001

# Terminal 2: Pipeline Events
uvicorn pipeline_events:app --host 0.0.0.0 --port 8002

# Terminal 3: System Monitoring
uvicorn system_api:app --host 0.0.0.0 --port 8003
```

3. Generate architecture data:
```bash
python generate_architecture.py
```

4. Open the dashboard:
Open `system_dashboard.html` in your web browser.

## Integration with Your Application

To integrate these components with your trading bot:

1. **Architecture Analysis**: Run `generate_architecture.py` whenever your codebase changes to update the architecture data.

2. **Configuration API**: Import and use the configuration loading functions in your application to ensure consistent configuration management.

3. **Pipeline Events**: Use the `PipelineEventEmitter` class in your pipeline implementations to emit events during execution:
```python
emitter = PipelineEventEmitter("ask_pipeline", correlation_id)
await emitter.emit_stage_started("ai_processing")
# ... pipeline logic ...
await emitter.emit_stage_completed("ai_processing", {"result": "success"})
```

4. **System Monitoring**: Integrate the `CircuitBreakerInterface` and `CacheInterface` classes with your actual circuit breaker and cache implementations.

## Security Considerations

- The Configuration API automatically masks sensitive data
- In production, restrict CORS origins in the WebSocket and API services
- Use HTTPS for all services
- Implement authentication for sensitive endpoints
- Regularly update dependencies

## Customization

Each component can be customized for your specific needs:

- Modify the sensitive key patterns in `config_api.py`
- Add new event types in `pipeline_events.py`
- Extend the circuit breaker and cache interfaces in `system_api.py`
- Customize the dashboard UI in `system_dashboard.html`

## Debug Console Integration

The `claude.html` dashboard includes a Debug Console that can automatically fetch and display real-time information from your trading system services using Server-Sent Events (SSE).

**Features:**
- Real-time pipeline execution monitoring
- Tool usage tracking
- System health updates
- Command processing visualization

**Automatic Fetching:**
To enable automatic fetching of real-time data:

1. Start the debug console updater:
```bash
python scripts/update_debug_console.py
```

2. Open `claude.html` in your browser and navigate to the Debug Console tab

**Integration with Services:**
Services can post debug events to the API:
- Pipeline services: Report stage execution
- Data providers: Report tool usage and performance
- Health monitors: Report system status

See `README_DEBUG_CONSOLE.md` for detailed integration instructions.

## Contributing

Contributions are welcome! Please feel free to submit issues and pull requests.