# API Container Requirements

## Overview
The API container provides REST endpoints for data access and webhook processing. It serves as the main interface for external systems to interact with the TradingView Automation system.

## Container Configuration
- **Service Name**: api
- **Container Name**: tradingview-api-dev
- **Base Image**: Custom Python 3.12-slim image
- **Port**: 8000
- **Command**: `uvicorn src.api.main:app --host 0.0.0.0 --port 8000 --reload`

## Environment Variables
- ENVIRONMENT=development
- DEBUG=true
- PYTHONPATH=/app
- LOG_LEVEL=INFO
- REDIS_URL=redis://${REDIS_PASSWORD}@redis:6379/0
- JWT_SECRET=${JWT_SECRET}
- USE_SUPABASE=true
- SUPABASE_URL=${SUPABASE_URL}
- SUPABASE_KEY=${SUPABASE_KEY}
- DATABASE_URL=${DATABASE_URL}
- SUPABASE_FALLBACK_IP=${SUPABASE_FALLBACK_IP}
- OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
- POLYGON_API_KEY=${POLYGON_API_KEY}
- FINNHUB_API_KEY=${FINNHUB_API_KEY}
- ALPACA_API_KEY=${ALPACA_API_KEY}
- DISCORD_BOT_TOKEN=${DISCORD_BOT_TOKEN}
- REDIS_PASSWORD=${REDIS_PASSWORD}

## Dependencies
All dependencies are defined in the main requirements/environments/production.txt file:
- FastAPI >= 0.100.0
- Uvicorn >= 0.23.0
- HTTPX >= 0.24.0
- SQLAlchemy >= 2.0.0
- Supabase >= 2.0.0
- Redis >= 4.5.0
- Pydantic
- Python-dotenv >= 1.0.0
- Structlog >= 23.0.0
- Prometheus-client >= 0.17.0

## Volumes
- ./src:/app/src (Source code)
- ./config:/app/config:ro (Configuration files - read-only)
- ./logs:/app/logs (Log files)
- ./data:/app/data (Data files)
- ./tests:/app/tests (Test files)

## Networks
- internal-network (Internal services)
- tradingview-network (Main network)

## Health Check
- Depends on Redis service being healthy

## Key Responsibilities
1. Serve REST API endpoints for the system
2. Handle authentication and authorization
3. Interface with the database for data persistence
4. Communicate with Redis for caching and messaging
5. Integrate with external APIs (Polygon, Finnhub, Alpaca)
6. Provide health check endpoints
7. Handle webhook processing from external systems

## Required Services
- Redis (caching and messaging)
- Database (Supabase/PostgreSQL)

## Security Considerations
- JWT-based authentication
- API key management for external services
- Secure communication with Redis using password authentication
- Environment variables for sensitive configuration