# Legacy Code Removal Plan

This document outlines a plan for safely removing legacy code from the tradingview-ingest codebase.

## Identified Legacy Components

1. **Legacy Visualizer Files**
   - `webhook_visualizer.py`
   - `simple_visualizer.py`
   - These have been replaced by the unified `visualizer.py` module

2. **Legacy Parsing Methods**
   - Legacy emoji-based format parsing in `text_parser.py` and `parser.py`
   - Methods like `_parse_legacy_format()`, `_determine_legacy_alert_type()`, `_extract_legacy_signal()`

3. **Legacy Imports**
   - Backward compatibility imports in `webhook_processor.py` and `webhook_receiver.py`
   - Example: `# Keep legacy import for backward compatibility` for supabase_client

4. **Legacy Storage Methods**
   - `_store_webhook_alert_legacy()` in `storage_manager.py`
   - Handles multiple legacy data formats including pipe-separated format

## Code Ownership

To ensure clear accountability and coordination, specific owners are assigned to each component:

| Component | Owner | Backup |
|-----------|-------|--------|
| Visualizer Files | @lead-developer | @backend-engineer |
| Parsing Methods | @backend-engineer | @data-engineer |
| Legacy Imports | @lead-developer | @devops-engineer |
| Storage Methods | @data-engineer | @backend-engineer |

Each owner is responsible for implementing changes, testing, and documentation for their assigned component.

## Removal Strategy

### Phase 1: Deprecation and Monitoring (1-2 weeks)

1. **Add Deprecation Warnings**
   - Add logging warnings to all legacy code paths
   - Example: `logger.warning("Using deprecated legacy parser, will be removed in future version")`
   - Monitor logs to see if these code paths are still being used

2. **Add Feature Flags**
   - Add feature flags to control legacy code usage
   - Example: `USE_LEGACY_PARSERS = config.get_bool("USE_LEGACY_PARSERS", default=True)`
   - This allows for easy toggling during testing

3. **Add Usage Metrics**
   - Add metrics to track usage of legacy code paths
   - Example: `legacy_parser_usage_counter.inc()`
   - Use these metrics to determine if it's safe to remove the code

4. **Automated Deprecation Warning Tests**
   - Implement automated tests that specifically detect deprecation warnings
   - Create test cases that trigger each legacy code path
   - Configure CI to fail if new usages of deprecated code are introduced
   - Example: `assert not deprecation_warnings, f"Deprecation warnings detected: {deprecation_warnings}"`

5. **Formal External Impact Analysis**
   - Identify all external systems potentially affected by legacy code removal
   - Document API dependencies and integration points
   - Create a communication plan for affected stakeholders
   - Prepare migration guides for external systems

### Phase 2: Testing and Validation (1 week)

1. **Disable Legacy Code in Test Environment**
   - Turn off legacy feature flags in test environment
   - Run comprehensive tests to ensure everything still works
   - Fix any issues that arise

2. **Validate Data Processing**
   - Ensure that all webhook formats are still properly processed
   - Compare results with legacy code enabled vs. disabled
   - Document any differences or edge cases

3. **Load Testing**
   - Perform load testing with legacy code disabled
   - Ensure performance is maintained or improved

4. **External System Integration Testing**
   - Test integration with all identified external systems
   - Verify that external systems can work with the updated code
   - Document any required changes for external systems

### Phase 3: Gradual Removal (1-2 weeks)

1. **Remove Legacy Files**
   - Remove `webhook_visualizer.py` and `simple_visualizer.py`
   - Update any remaining imports to use the new `visualizer.py`

2. **Remove Legacy Methods**
   - Remove legacy parsing methods from `parser.py`
   - Remove legacy storage methods from `storage_manager.py`

3. **Clean Up Imports**
   - Remove backward compatibility imports
   - Update any code that still depends on these imports

### Phase 4: Final Cleanup (1 week)

1. **Remove Feature Flags**
   - Remove all legacy feature flags
   - Clean up any conditional code paths

2. **Update Documentation**
   - Update API documentation to remove references to legacy formats
   - Document the new unified formats and interfaces

3. **Final Testing**
   - Run comprehensive tests to ensure everything still works
   - Verify that all metrics and logs are clean

## Version Control Strategy

To maintain clear history and enable easy rollbacks if needed, we'll follow this version control strategy:

1. **Main Branch Protection**
   - Protect the main branch to prevent direct commits
   - Require code reviews for all pull requests

2. **Feature Branches**
   - Create dedicated feature branches for each phase:
     - `legacy/phase1-deprecation`
     - `legacy/phase2-testing`
     - `legacy/phase3-removal`
     - `legacy/phase4-cleanup`

3. **Tagging Strategy**
   - Tag the codebase before starting each phase: `pre-legacy-removal-v1.0`
   - Tag after each phase completion: `post-legacy-phase1-v1.1`

4. **Pull Request Templates**
   - Use PR templates that include legacy code removal checklist
   - Require test results and metrics in PR descriptions

## Risk Mitigation

1. **Rollback Plan**
   - Keep backups of all removed code
   - Maintain ability to quickly restore legacy code if issues arise
   - Create rollback scripts for each phase

2. **Gradual Deployment**
   - Deploy changes incrementally
   - Monitor each deployment for issues before proceeding
   - Use feature flags to enable quick disabling of problematic changes

3. **Communication**
   - Inform all stakeholders about the removal of legacy code
   - Provide migration guides for any external systems that might be affected
   - Schedule regular status updates throughout the process

## Timeline

- **Phase 1**: Weeks 1-2
- **Phase 2**: Week 3
- **Phase 3**: Weeks 4-5
- **Phase 4**: Week 6

Total estimated time: 6 weeks
