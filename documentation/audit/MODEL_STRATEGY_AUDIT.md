# AI Model Strategy Audit & Fix Plan

## Current State: Complete Mess

### 🚨 Problems Identified

1. **Random Model Assignment**: We're throwing random models at random jobs
2. **Hardcoded Models Everywhere**: Models hardcoded in 15+ files
3. **No Job-Based Strategy**: Using "heavy/light" instead of actual job requirements
4. **Config Chaos**: Environment vars, YAML, and hardcoded values all conflicting
5. **Unused Complexity**: Complex routing systems that aren't actually used

### 📊 Current Model Usage Audit

#### What We Actually Use:
- **Main Ask Command**: `deepcogito/cogito-v2-preview-deepseek-671b` (HARDCODED)
- **Fallback**: `moonshotai/kimi-k2-0905` (HARDCODED)
- **Test Files**: Random models scattered everywhere

#### What We Have Configured (But Don't Use):
- Complex YAML configs with 20+ models
- Smart routing systems
- Performance tracking
- Cross-validation systems

### 🎯 Actual Jobs That Need AI Models

Based on codebase analysis, here are the REAL jobs:

1. **Discord Chat Response** - User asks questions, needs immediate response
2. **Symbol Extraction** - Extract stock symbols from user text
3. **Intent Classification** - Determine what user wants (price, analysis, etc.)
4. **Technical Analysis** - Calculate indicators from market data
5. **Data Validation** - Verify AI responses against real data
6. **Error Handling** - Generate user-friendly error messages

### 🧠 Model Strategy by Job

| Job | Current Model | Recommended Model | Why |
|-----|---------------|-------------------|-----|
| **Discord Chat** | `deepcogito/cogito-v2-preview-deepseek-671b` | `gpt-4o-mini` | Fast, cheap, good for chat |
| **Symbol Extraction** | `moonshotai/kimi-k2-0905` | `gpt-4o-mini` | Simple pattern matching |
| **Intent Classification** | `moonshotai/kimi-k2-0905` | `gpt-4o-mini` | Simple classification |
| **Technical Analysis** | N/A (calculated) | N/A | Math, not AI |
| **Data Validation** | N/A | N/A | Logic, not AI |
| **Error Handling** | `moonshotai/kimi-k2-0905` | `gpt-4o-mini` | Simple text generation |

### 🔧 Configuration Strategy

#### Priority Order:
1. **Environment Variables** (for Docker/override)
2. **YAML Config** (for development)
3. **Hardcoded Defaults** (last resort)

#### Single Source of Truth:
```yaml
# config/services/ai_models.yaml
models:
  chat_response:
    model_id: "gpt-4o-mini"
    max_tokens: 2000
    temperature: 0.7
    
  symbol_extraction:
    model_id: "gpt-4o-mini" 
    max_tokens: 500
    temperature: 0.1
    
  intent_classification:
    model_id: "gpt-4o-mini"
    max_tokens: 300
    temperature: 0.1
```

### 📝 Files That Need Fixing

#### High Priority (Actually Used):
- `src/bot/extensions/ask_ai_first_fixed.py` - Line 62: HARDCODED model
- `src/shared/ai_chat/ai_client.py` - Line 166: HARDCODED fallback
- `src/shared/ai_services/smart_model_router.py` - Multiple hardcoded models

#### Medium Priority (Test Files):
- `test_new_model.py` - Line 35: HARDCODED model
- `test_json_parsing_fix.py` - Line 29: HARDCODED model
- `test_fixed_ai_first.py` - Line 30: HARDCODED model

#### Low Priority (Unused Complexity):
- `config/services/ai_models.yaml` - Overly complex, not used
- `src/shared/ai_services/cross_validation_ai.py` - Not used
- `src/bot/pipeline/commands/ask/stages/ai_routing_service.py` - Not used

### 🚀 Implementation Plan

#### Phase 1: Simplify & Fix (Immediate)
1. Create simple job-based config
2. Replace hardcoded models with config variables
3. Remove unused complex systems
4. Test with real Discord commands

#### Phase 2: Optimize (Future)
1. Add model performance tracking
2. Implement A/B testing
3. Add cost monitoring
4. Create model fallback chains

### 💰 Cost Analysis

Current: Random expensive models
Proposed: `gpt-4o-mini` for everything
Savings: ~80% cost reduction

### ✅ Success Criteria

- [ ] No hardcoded models in source code
- [ ] All models loaded from config
- [ ] Discord commands work reliably
- [ ] Cost under control
- [ ] Simple, maintainable code

---

**Next Steps:**
1. Create simple model config
2. Fix hardcoded models
3. Test with Discord
4. Remove unused complexity
