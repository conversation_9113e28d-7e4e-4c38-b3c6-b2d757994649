module,count
typing,640
datetime,451
logging,447
asyncio,431
os,343
time,231
json,227
dataclasses,212
sys,198
src.shared.error_handling.logging,180
enum,158
traceback,137
re,128
discord,118
pandas,104
numpy,70
src.core.config_manager,60
pytest,59
discord.ext,54
pathlib,50
config,43
src.shared.data_providers.aggregator,41
fastapi,35
hashlib,35
unittest.mock,35
functools,32
aiohttp,31
collections,31
structlog,30
uuid,30
src.bot.permissions,25
random,25
src.shared.technical_analysis.calculator,23
src.shared.error_handling.fallback,23
src.shared.ai_services.ai_chat_processor,21
src.api.data.market_data_service,21
sqlalchemy,21
src.shared.utils.symbol_extraction,20
src.bot.pipeline.core.context_manager,20
prometheus_client,20
models,20
src.shared.data_providers.unified_base,19
src.database.unified_db,19
pydantic,19
src.bot.pipeline.commands.ask.stages.enhanced_ai_analysis,19
src.api.data.cache,17
fastapi.responses,16
supabase,16
src.bot.utils.input_sanitizer,16
core.base,16
httpx,16
unified_base,16
src.shared.data_providers.alpha_vantage,15
src.shared.data_providers.yfinance_provider,15
src.data.models.stock_data,15
hmac,14
pytz,14
abc,14
dotenv,14
src.bot.pipeline.commands.ask.stages.response_templates,14
src.shared.monitoring.performance_monitor,13
src.bot.client,13
contextlib,13
yaml,13
redis.asyncio,13
openai,13
src.api.data.providers.base,13
src.api.data.providers.data_source_manager,13
src.database.connection,13
subprocess,13
src.shared.utils.discord_helpers,12
src.core.market_calendar,12
src.bot.pipeline.commands.analyze.pipeline,12
src.bot.pipeline.commands.ask.pipeline,12
pipeline,12
src.shared.technical_analysis.indicators,12
yfinance,12
src.shared.watchlist.supabase_manager,11
src.shared.ai_services.timeout_manager,11
src.core.automation.report_engine,11
src.shared.monitoring,10
exceptions,10
src.bot.utils.disclaimer_manager,10
src.bot.pipeline.commands.ask.executor,10
signals,10
sqlalchemy.orm,10
src.shared.ai_services.circuit_breaker,9
warnings,9
src.shared.data_validation,9
src.shared.data_providers.alpaca_provider,9
statistics,9
src.core.utils,9
src.core.logger,9
src.api.data.providers.finnhub,9
uvicorn,8
apscheduler.schedulers.asyncio,8
rate_limiter,8
src.core.formatting.response_templates,8
src.bot.watchlist_alerts,8
src.analysis.probability.probability_engine,8
query_wrapper,8
indicators,8
src.bot.pipeline.commands.analyze.parallel_pipeline,8
src.shared.config.config_manager,8
core.context_manager,8
src.bot.pipeline.performance_optimizer,8
response_templates,8
ai_cache,8
argparse,8
shared.technical_analysis.enhanced_indicators,8
fastapi.middleware.cors,7
storage_manager,7
src.shared.data_providers.finnhub_provider,7
src.shared.technical_analysis.signal_generator,7
psutil,7
src.shared.ai_services.ai_processor_robust,7
inspect,7
redis,7
base,7
src.api.data.metrics,7
requests,7
src.core.exceptions,7
src.analysis.technical.price_targets,7
text_parser,6
src.shared.data_providers.polygon_provider,6
src.core.monitoring_pkg,6
apscheduler.triggers.interval,6
src.shared.ai_services.ai_service_wrapper,6
signal,6
pipeline_engine,6
logger,6
src.analysis.fundamental.metrics,6
src.shared.cache.cache_service,6
src.shared.ai_services.smart_model_router,6
threading,6
query_analyzer,6
importlib,6
confidence_scorer,6
src.shared.technical_analysis.zones,6
src.core.risk_management.atr_calculator,6
calculator,6
src.bot.pipeline.commands.analyze.stages.enhanced_analysis,6
src.api.data.providers.polygon,6
starlette.middleware.base,5
starlette.responses,5
src.core.feedback_mechanism,5
src.bot.core.bot,5
src.core.monitoring_pkg.bot_monitor,5
src.shared.technical_analysis.strategy_calculator,5
src.bot.enhancements.pipeline_visualizer,5
src.shared.services.optimization_service,5
src.bot.pipeline.core.pipeline_engine,5
error_handler,5
src.shared.ai_services.query_cache,5
src.bot.pipeline.commands.ask.stages.response_validator,5
ast,5
src.core.automation.discord_handler,5
src.shared.technical_analysis.options_greeks_calculator,5
src.shared.configuration,5
data_parser,5
visualizer,5
config.tradingview_config,5
security,4
pipeline.commands.ask.pipeline,4
src.bot.core.services,4
audit.request_visualizer,4
src.api.routes.health,4
src.api.routes.analytics,4
src.api.middleware.security,4
src.bot.database_manager,4
src.shared.redis.redis_manager,4
connection,4
manager,4
src.bot.metrics_collector,4
secrets,4
request_visualizer,4
session_manager,4
html,4
context_manager,4
core.pipeline_engine,4
src.shared.data_providers,4
ai_chat,4
pipeline_sections,4
src.bot.pipeline.commands.ask.stages.ai_models_config_loader,4
src.bot.pipeline.commands.ask.stages.ai_routing_service,4
src.shared.ai_services.ai_client,4
ai_models_config_loader,4
market_context_service,4
ai_client,4
response_formatter,4
validation,4
prometheus_client.core,4
src.api.data.constants,4
apscheduler.triggers.cron,4
fastapi.security,4
jwt,4
analysis_template,4
report_engine,4
report_formatter,4
src.api.routes.bot_health,4
base_manager,4
signal_analyzer,4
alpha_vantage,4
fallback_provider,4
polygon_provider,4
src.shared.market_analysis.unified_signal_analyzer,4
math,4
zones,4
src.bot.pipeline.commands.ask.stages.zero_hallucination_generator,4
sqlalchemy.ext.asyncio,4
src.bot.pipeline.commands.ask.stages.ml_sentiment_analyzer,4
src.analysis.technical.timeframe_confirmation,4
src.shared.redis,4
src.bot.commands.recommendations_command,4
src.bot.monitoring.health_monitor,3
urllib.parse,3
base64,3
src.shared.watchlist.models,3
src.analysis.templates.analysis_response_template,3
src.bot.pipeline.commands.ask.batch_processor,3
src.shared.services.enhanced_performance_optimizer,3
fastapi.middleware.trustedhost,3
concurrent.futures,3
src.analysis.ai.recommendation_engine,3
src.shared.database,3
src.shared.background.celery_app,3
logging.config,3
alembic,3
src.core.automation.report_formatter,3
src.core.automation.report_scheduler,3
shutil,3
src.bot.pipeline.commands.ask.stages.ai_controlled_analysis,3
src.shared.monitoring.pipeline_grader,3
src.bot.utils.enhanced_input_validator,3
src.bot.pipeline.commands.analyze.stages.report_generator,3
shared.technical_analysis.multi_timeframe_analyzer,3
src.bot.pipeline.commands.ask.stages.depth_style_analyzer,3
src.shared.market_analysis.signal_analyzer,3
analyzer,3
webhook_core,3
config_dir,3
test_config,3
src.parser,3
src.storage_manager,3
src.bot.commands.analyze_async,3
src.bot.pipeline.commands.ask.stages.cross_platform_context,3
comprehensive_test_logger,3
src.bot.restart_manager,2
cache,2
middleware,2
src.analysis.risk.calculators.volatility_calculator,2
client,2
discord.app_commands,2
pipeline.commands.ask.executor,2
pipeline.commands.ask.batch_processor,2
pipeline.commands.ask.stages.response_audit,2
pipeline.commands.ask.stages.ask_sections,2
permissions,2
watchlist_alerts,2
database_manager,2
utils.component_checker,2
utils.input_sanitizer,2
utils.disclaimer_manager,2
watchlist_realtime,2
pipeline.commands.ask.stages.query_analyzer,2
aiofiles,2
src.bot.pipeline.commands.ask.executor_with_grading,2
setup_audit,2
token_validator,2
src.api.routes.market_data,2
src.api.routes.metrics,2
src.api.routes.feedback,2
src.api.routes,2
ai.enhancement_strategy,2
src.core.watchlist.manager,2
src.core.trade_scanner,2
src.bot.setup_audit,2
config_manager,2
monitoring_pkg,2
monitoring_pkg.bot_monitor,2
metrics_tracker,2
backoff,2
stock_data,2
src.bot.watchlist_realtime,2
src.bot.pipeline.commands.ask.stages.voice_processor,2
src.bot.utils.embed_builder,2
src.bot.utils.permissions,2
ipaddress,2
performance_optimizer,2
discord.abc,2
discord.channel,2
request_visualizer_patch,2
src.shared.metrics.metrics_service,2
src.bot.rate_limiter,2
services,2
circuit_breaker,2
src.shared.monitoring.intelligent_grader,2
executor_with_grading,2
stages.pipeline_sections,2
stages.ask_sections,2
executor,2
src.core.formatting.analysis_template,2
stages.technical_analysis,2
stages.price_targets,2
stages.enhanced_analysis,2
stages.report_generator,2
src.bot.pipeline.core.parallel_pipeline,2
io,2
speech_recognition,2
pydub,2
src.bot.pipeline.commands.ask.stages.config,2
src.bot.pipeline.commands.ask.stages.core.base,2
src.bot.pipeline.commands.ask.stages.core.error_handler,2
response_validator,2
discord_formatter,2
utils.circuit_breaker,2
utils.metrics,2
enhanced_ai_analysis,2
structured_response_generator,2
ml_sentiment_analyzer,2
cross_platform_context,2
enhanced_context,2
advanced_classifier,2
stages.query_analyzer,2
langdetect,2
langdetect.lang_detect_exception,2
ai_controlled_analysis,2
cache_integration,2
fallback_handler,2
input_processor,2
context_processor,2
input_validator,2
context_builder,2
prompt_formatter,2
enhanced_ai_client,2
technical_analysis_processor,2
market_context_processor,2
response_parser,2
response_generator,2
memory_updater,2
metrics_collector,2
src.core.validation.financial_validator,2
src.api.schemas.metrics_schema,2
src.api.schemas.feedback_schema,2
bot_health,2
src.api.middleware.security_utils,2
passlib.context,2
modules,2
auditing,2
rate_limiting,2
monte_carlo_simulator,2
probability_engine,2
src.analysis.risk.assessment,2
textblob,2
bs4,2
financial_validator,2
prompt_manager,2
technical_analysis,2
queue,2
heapq,2
apscheduler.jobstores.memory,2
discord_handler,2
compliance_framework,2
performance_tracker,2
bot_monitor,2
celery,2
redis_manager,2
src.shared.technical_analysis,2
unified_signal_analyzer,2
src.shared.utils.deprecation,2
yfinance_provider,2
alpaca_provider,2
yfinance.utils,2
ratelimit,2
yfinance.exceptions,2
finnhub_provider,2
src.shared.ai_services.tool_registry,2
fallbacks,2
processor,2
data_fetcher,2
src.shared.ai_services.openrouter_key,2
src.shared.ai_chat.ai_client,2
naming_conventions,2
deprecation,2
pipeline_grader,2
fallback,2
ai_processor_robust,2
src.shared.ai_services.simple_query_analyzer,2
src.shared.ai_services.query_router,2
src.shared.ai_services.response_synthesizer,2
src.shared.monitoring.step_logger,2
src.shared.ai_services.fast_price_lookup,2
unified_calculator,2
signal_generator,2
volume_analyzer,2
live_ai_debugger,2
market_data,2
users,2
alerts,2
analysis,2
src.database.models,2
src.analysis.technical.indicators,2
src.analysis.probability.probability_response_service,2
simple_pipeline_emitter,2
cryptography.fernet,2
fastapi.testclient,2
src.api.main,2
src.bot.pipeline.commands.ask.stages.ai_chat_processor,2
src.bot.security.advanced_security,2
src.core.formatting.technical_analysis,2
src.bot.pipeline.commands.ask.stages.pipeline_sections,2
src.bot.pipeline.commands.ask.stages.ask_sections,2
src.bot.pipeline.core.pipeline_optimizer,2
src.database.query_wrapper,2
src.shared.configuration.validators,2
unittest,2
src.data.providers.fallback_provider,2
src.shared.ai_debugger,2
alert_engine,2
parser,2
webhook_processor,2
supabase_client,2
src.alert_engine,2
src.bot.commands.zones_enhanced,2
src.bot.pipeline.commands.ask.stages.structured_response_generator,1
src.shared.ai_chat.response_formatter,1
src.bot.pipeline.commands.ask.stages.data_provider,1
websockets,1
test_simple_pipeline_debug,1
src.bot.pipeline.commands.ask.ask_command,1
src.bot.pipeline.commands.ask.stages.ai_symbol_extractor,1
tests.live_command_tester,1
pipeline_events,1
importlib.util,1
src.database.models.market_data,1
string,1
getpass,1
matplotlib.pyplot,1
src.core.deprecation_monitor,1
src.data.providers.finnhub_provider,1
src.data.providers.polygon_provider,1
src.data.providers.alpha_vantage_provider,1
src.data.providers.base,1
core.config_manager,1
core.data_quality_validator,1
core.config_validator,1
fnmatch,1
src.bot.audit.request_visualizer,1
src.core.stale_data_detector,1
src.data.providers.manager,1
src.data.providers.config,1
src.templates.ask,1
src.core.response_generator,1
src.data.providers,1
src.bot.pipeline.commands.ask.stages.conversation_memory_service,1
tradingview_ingest.src.parser,1
src.bot.utils.rate_limiter,1
src.bot.utils.error_handler,1
src.shared.ai_chat.models,1
tradingview_ingest.src.ai_alert_parser,1
src.database.query_optimizer,1
core.watchlist.watchlist_manager,1
core.automation.analysis_scheduler,1
src.core.prompts,1
src.bot.pipeline.commands.ask.stages.quick_commands,1
src.bot.pipeline.commands.ask.stages.discord_formatter,1
src.shared.ai_debugger.local_pattern_debugger,1
src.core.monitoring,1
src.shared.data_providers.yfinance,1
glob,1
tempfile,1
pyotp,1
bot.pipeline.commands.ask.stages.core.technical_analysis_processor,1
src.bot.pipeline.commands.ask.stages.fallback_data_generator,1
src.core.pipeline_engine,1
src.core.outlier_detector,1
src.api.data.scheduled_tasks,1
src.data.providers.yfinance_provider,1
src.shared.database.supabase_sdk_client,1
src.analysis.orchestration.analysis_orchestrator,1
src.bot.pipeline.commands.analyze.executor,1
src.utils.fix_mock_issue,1
src.core.data_quality_validator,1
src.data.providers.enhanced_config,1
src.shared.watchlist.ingest_watchlist_manager,1
ai_alert_parser,1
discord_notifier,1
automated_analyzer,1
src.webhook_core,1
src.main,1
src.supabase_client,1
src.config.legacy_flags,1
operations,1
legacy_flags,1
src.shared.watchlist.base_manager,1
src.bot.commands.watchlist_enhanced,1
src.bot.pipeline.commands.ask.stages.enhanced_analyzer,1
src.bot.pipeline.commands.ask.stages.enhanced_context,1
src.bot.pipeline.commands.ask.stages.advanced_classifier,1
