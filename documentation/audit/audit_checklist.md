# Codebase Deep-Dive Audit Checklist

Generated: 2025-09-19 13:05 UTC

Source: architecture.json (AST-based scan)


## Summary
- Areas: 12
- Files: 413

## Canonical vs Legacy — Initial Decisions (Focused Areas)

- /ask command
  - Canonical: src/bot/pipeline/commands/ask/pipeline.py (AskPipeline)
  - Legacy/Wrapper: src/bot/pipeline/commands/ask/executor.py (wraps default pipeline), any uses of default_pipeline
  - Notes: Extension currently calls executor; plan to route through AskPipeline and keep executor as transitional wrapper.

- /analyze command
  - Canonical: src/bot/pipeline/commands/analyze/parallel_pipeline.py
  - Legacy/Fallback: src/bot/pipeline/commands/analyze/pipeline.py (serial)
  - Notes: Keep serial as fallback only; ensure imports are gated for environments without analysis deps.

- AI services
  - Canonical: src/shared/ai_services/ai_processor_robust.py
  - Legacy/Wrapper: src/shared/ai_services/ai_chat_processor.py (compat wrapper)
  - Notes: Prefer direct robust processor usage; keep wrapper for back-compat where expected.

- Data providers
  - Canonical: src/shared/data_providers/* (with aggregator)
  - Legacy/Wrapper: src/api/data/providers/*, src/data/providers/*
  - Notes: Aggregator initializes providers conditionally; ensure is_configured() is standardized.

- Security
  - Canonical: src/api/middleware/security.py (JWT, rate-limit, headers)
  - Legacy/Wrapper: any bot-specific security modules
  - Notes: Consider structured auth failure logs and distributed rate-limit storage.


## analysis
- Legend: [ ] = not started, [/] = in progress, [x] = complete

- [ ] src/analysis/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/ai/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/ai/calculators/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/ai/calculators/sentiment_calculator.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/ai/enhancement_strategy.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/ai/recommendation_engine.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/enhanced_evaluator.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/fundamental/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/fundamental/calculators/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/fundamental/calculators/growth_calculator.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/fundamental/calculators/pe_calculator.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/fundamental/metrics.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/orchestration/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/orchestration/analysis_orchestrator.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/orchestration/enhancement_strategy.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/probability/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/probability/monte_carlo_simulator.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/probability/probability_engine.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/probability/probability_response_service.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/risk/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/risk/assessment.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/risk/calculators/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/risk/calculators/beta_calculator.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/risk/calculators/volatility_calculator.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/technical/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/templates/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/templates/analysis_response_template.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/utils/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/analysis/utils/data_validators.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:

## api
- Legend: [ ] = not started, [/] = in progress, [x] = complete

- [ ] src/api/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/analytics/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/config.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/data/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/data/cache.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/data/cache_warming_scheduler.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/data/constants.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/data/market_data_service.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/data/metrics.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/data/providers/__init__.py (legacy?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/data/providers/alpha_vantage.py (legacy?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/data/providers/base.py (legacy?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/data/providers/data_source_manager.py (legacy?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/data/providers/finnhub.py (legacy?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/data/providers/modules/__init__.py (legacy?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/data/providers/modules/auditing.py (legacy?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/data/providers/modules/config.py (legacy?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/data/providers/modules/rate_limiting.py (legacy?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/data/providers/modules/validation.py (legacy?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/data/providers/polygon.py (legacy?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/data/scheduled_tasks.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/main.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/middleware/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/middleware/security.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/middleware/security_utils.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/routers/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/routers/market_data.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/routes/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/routes/analytics.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/routes/bot_health.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/routes/dashboard.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/routes/debug.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/routes/feedback.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/routes/health.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/routes/market_data.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/routes/metrics.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/schemas/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/schemas/feedback_schema.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/schemas/metrics_schema.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/api/webhooks/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:

## bot (other)
- Legend: [ ] = not started, [/] = in progress, [x] = complete

- [ ] src/bot/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/__main__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/audit/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/audit/rate_limiter.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/audit/request_visualizer.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/audit/request_visualizer.py.fixed
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/audit/request_visualizer_patch.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/audit/session_manager.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/client.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/client_audit_integration.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/client_with_monitoring.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/core/bot.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/core/error_handler.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/core/services.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/database_manager.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/enhancements/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/enhancements/discord_ux.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/enhancements/pipeline_visualizer.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/events/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/main.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/metrics_collector.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/monitoring/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/monitoring/health_monitor.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/permissions.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/rate_limiter.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/security/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/security/advanced_security.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/setup_audit.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/token_validator.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/update_imports.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/utils/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/utils/component_checker.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/utils/disclaimer_manager.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/utils/enhanced_input_validator.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/utils/error_handler.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/utils/input_sanitizer.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/utils/rate_limiter.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/watchlist_alerts.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/watchlist_realtime.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:

## bot/extensions
- Legend: [ ] = not started, [/] = in progress, [x] = complete

- [ ] src/bot/extensions/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/extensions/alerts.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/extensions/analyze.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/extensions/ask.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/extensions/batch_analyze.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/extensions/error_handler.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/extensions/help.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/extensions/performance_monitor.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/extensions/portfolio.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/extensions/recommendations.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/extensions/status.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/extensions/utility.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/extensions/watchlist.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/extensions/zones.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:

## bot/pipeline
- Legend: [ ] = not started, [/] = in progress, [x] = complete

- [ ] src/bot/pipeline/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/ask/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/ask/stages/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/ask/stages/conversation_memory_service.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/analyze/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/analyze/parallel_pipeline.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/analyze/pipeline.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/analyze/stages/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/analyze/stages/enhanced_analysis.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/analyze/stages/fetch_data.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/analyze/stages/price_targets.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/analyze/stages/report_generator.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/analyze/stages/report_template.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/analyze/stages/technical_analysis.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/batch_processor.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/config.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/config.yaml (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/error_handler.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/events.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/executor.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/executor_with_grading.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/modules/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/modules/config/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/modules/models/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/modules/models/data_models.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/modules/services/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/modules/tests/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/modules/utils/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/modules/utils/retry.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/modules/utils/validation.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/modules/utils/validators.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/pipeline.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/advanced_classifier.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/ai_cache.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/ai_controlled_analysis.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/ai_models_config.yaml (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/ai_models_config_loader.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/ai_routing_service.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/ai_symbol_extractor.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/ask_sections.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/botlogs.txt (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/config.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/conversation_memory_service.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/core/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/core/ai_client.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/core/base.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/core/enhanced_ai_client.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/core/error_handler.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/core/market_context_processor.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/core/response_parser.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/core/technical_analysis_processor.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/cross_platform_context.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/depth_style_analyzer.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/discord_formatter.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/enhanced_ai_analysis.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/enhanced_analyzer.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/enhanced_context.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/language_detector.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/market_context_service.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/ml_sentiment_analyzer.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/models.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/pipeline_sections.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/postprocessor/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/postprocessor/memory_updater.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/postprocessor/metrics_collector.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/postprocessor/response_formatter.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/postprocessor/response_generator.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/preprocessor/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/preprocessor/context_builder.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/preprocessor/context_processor.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/preprocessor/input_processor.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/preprocessor/input_validator.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/preprocessor/prompt_formatter.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/prompts.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/query_analyzer.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/quick_commands.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/response_audit.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/response_templates.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/response_validator.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/structured_response_generator.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/test_ai_models_config.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/test_infrastructure.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/utils/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/utils/cache_integration.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/utils/fallback_handler.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/utils/rate_limiter.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/voice_processor.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/stages/zero_hallucination_generator.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/test_modular_system.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/ask/utility.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/watchlist/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/commands/watchlist/stages/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/core/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/core/circuit_breaker.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/core/context_manager.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/core/parallel_pipeline.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/core/pipeline_engine.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/core/pipeline_optimizer.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/data/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/logs/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/monitoring/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/performance_optimizer.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/shared/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/shared/data_collectors/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/shared/formatters/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/shared/validators/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/test_pipeline.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/utils/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/utils/circuit_breaker.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline/utils/metrics.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/bot/pipeline_framework.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:

## core
- Legend: [ ] = not started, [/] = in progress, [x] = complete

- [ ] src/core/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/automation/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/automation/analysis_scheduler.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/automation/discord_handler.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/automation/report_engine.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/automation/report_formatter.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/automation/report_scheduler.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/config_manager.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/data_quality_validator.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/enums/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/enums/stock_analysis.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/exceptions.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/feedback_mechanism.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/formatting/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/formatting/analysis_template.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/formatting/response_templates.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/formatting/technical_analysis.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/formatting/text_formatting.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/logger.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/market_calendar.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/monitoring_pkg/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/monitoring_pkg/bot_monitor.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/monitoring_pkg/performance_tracker.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/pipeline_engine.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/prompts/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/prompts/models.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/prompts/prompt_manager.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/prompts/templates/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/prompts/templates/system_prompt.txt
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/response_generator.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/risk_management/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/risk_management/atr_calculator.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/risk_management/compliance_framework.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/scheduler.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/secure_cache.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/trade_scanner.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/utils.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/validation/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/validation/financial_validator.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/core/watchlist/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:

## data (legacy)
- Legend: [ ] = not started, [/] = in progress, [x] = complete

- [ ] src/data/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/data/cache/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/data/cache/manager.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/data/models/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/data/models/indicators.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/data/models/stock_data.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/database/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/database/config.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/database/migrations/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/database/migrations/env.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/database/models/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/database/models/alerts.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/database/models/analysis.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/database/models/interactions.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/database/models/market_data.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/database/query_optimizer.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/database/query_wrapper.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/database/repositories/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/database/unified_client.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/database/unified_db.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:

## security (legacy)
- Legend: [ ] = not started, [/] = in progress, [x] = complete

- [ ] src/security/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/security/middleware.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:

## services
- Legend: [ ] = not started, [/] = in progress, [x] = complete

- [ ] src/services/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/services/analytics_service.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:

## shared
- Legend: [ ] = not started, [/] = in progress, [x] = complete

- [ ] src/shared/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai/depth_controller.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai/model_fine_tuner.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai/recommendation_engine.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_chat/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_chat/ai_client.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_chat/config.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_chat/data_fetcher.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_chat/fallbacks.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_chat/models.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_chat/processor.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_chat/response_formatter.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_debugger/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_debugger/live_ai_debugger.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_debugger/local_pattern_debugger.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_services/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_services/ai_chat_processor.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_services/ai_processor_clean.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_services/ai_processor_robust.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_services/ai_service_wrapper.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_services/circuit_breaker.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_services/fallback_handler.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_services/fast_price_lookup.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_services/intelligent_chatbot.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_services/openrouter_key.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_services/performance_optimizer.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_services/query_cache.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_services/query_router.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_services/response_synthesizer.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_services/simple_query_analyzer.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_services/smart_model_router.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_services/timeout_manager.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/ai_services/tool_registry.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/analytics/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/analytics/performance_tracker.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/background/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/background/celery_app.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/background/tasks/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/background/tasks/indicators.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/background/tasks/market_intelligence.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/cache/cache_service.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/config/config_manager.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/configuration/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/configuration/validators.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/data_providers/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/data_providers/aggregator.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/data_providers/alpaca_provider.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/data_providers/alpha_vantage.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/data_providers/base.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/data_providers/fallback_provider.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/data_providers/finnhub_provider.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/data_providers/polygon_provider.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/data_providers/unified_base.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/data_providers/yfinance_provider.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/data_validation.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/database/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/database/usage_example.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/error_handling/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/error_handling/fallback.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/error_handling/logging.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/error_handling/retry.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/market_analysis/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/market_analysis/confidence_scorer.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/market_analysis/signal_analyzer.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/market_analysis/signals.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/market_analysis/unified_signal_analyzer.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/market_analysis/utils.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/metrics/metrics_service.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/metrics/naming_conventions.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/metrics/unified_metrics_service.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/monitoring/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/monitoring/intelligent_grader.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/monitoring/performance_monitor.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/monitoring/pipeline_grader.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/monitoring/pipeline_monitor.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/monitoring/step_logger.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/redis/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/redis/redis_manager.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/sentiment/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/sentiment/sentiment_analyzer.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/services/enhanced_performance_optimizer.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/services/optimization_service.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/services/performance_monitor.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/technical_analysis/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/technical_analysis/calculator.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/technical_analysis/config.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/technical_analysis/enhanced_indicators.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/technical_analysis/indicators.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/technical_analysis/multi_timeframe_analyzer.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/technical_analysis/options_greeks_calculator.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/technical_analysis/signal_generator.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/technical_analysis/strategy_calculator.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/technical_analysis/test_indicators.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/technical_analysis/unified_calculator.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/technical_analysis/volume_analyzer.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/technical_analysis/zones.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/utils/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/utils/deprecation.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/utils/discord_helpers.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/utils/symbol_extraction.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/watchlist/__init__.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/watchlist/base_manager.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/watchlist/bot_manager.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/watchlist/models.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/watchlist/supabase_manager.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/shared/watchlist/webhook_manager.py (canonical?)
  - Canonical:
  - Legacy/Wrapper:
  - Notes:

## src (misc)
- Legend: [ ] = not started, [/] = in progress, [x] = complete

- [ ] src/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/logs/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/logs/app.log
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/main.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:

## templates
- Legend: [ ] = not started, [/] = in progress, [x] = complete

- [ ] src/templates/__init__.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
- [ ] src/templates/analysis_response.py
  - Canonical:
  - Legacy/Wrapper:
  - Notes:
