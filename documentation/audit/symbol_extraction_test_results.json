{"total_tests": 12, "ai_wins": 10, "regex_wins": 2, "ties": 0, "ai_total_time": 13.25408673286438, "regex_total_time": 0.0004901885986328125, "test_results": [{"test_case": "Company name recognition", "expected": ["AAPL"], "ai_symbols": ["AAPL", "OF", "WHAT", "STOCK", "PRICE", "APPLE"], "regex_symbols": [], "ai_accuracy": 0.2857142857142857, "regex_accuracy": 0.0, "winner": "ai", "ai_time": 13.250146389007568, "regex_time": 0.00023818016052246094}, {"test_case": "Dollar prefix symbols", "expected": ["TSLA", "NVDA"], "ai_symbols": ["BUY", "TSLA", "NVDA", "OR"], "regex_symbols": ["TSLA", "NVDA"], "ai_accuracy": 0.****************, "regex_accuracy": 1.0, "winner": "regex", "ai_time": 0.0007381439208984375, "regex_time": 0.0001399517059326172}, {"test_case": "Full company name", "expected": ["MSFT"], "ai_symbols": ["MSFT"], "regex_symbols": [], "ai_accuracy": 1.0, "regex_accuracy": 0.0, "winner": "ai", "ai_time": 0.0004925727844238281, "regex_time": 1.8596649169921875e-05}, {"test_case": "Multiple company names", "expected": ["GOOGL", "AMZN"], "ai_symbols": ["GOOGL", "VS", "AMZN"], "regex_symbols": [], "ai_accuracy": 0.8, "regex_accuracy": 0.0, "winner": "ai", "ai_time": 0.00041031837463378906, "regex_time": 1.7881393432617188e-05}, {"test_case": "Company name in news context", "expected": ["TSLA"], "ai_symbols": ["UP", "TODAY", "IS", "TESLA", "TSLA"], "regex_symbols": [], "ai_accuracy": 0.*****************, "regex_accuracy": 0.0, "winner": "ai", "ai_time": 0.00033593177795410156, "regex_time": 1.239776611328125e-05}, {"test_case": "Mixed symbols and company names", "expected": ["AAPL", "META", "MSFT"], "ai_symbols": ["AAPL", "AT", "META", "MSFT"], "regex_symbols": [], "ai_accuracy": 0.8571428571428571, "regex_accuracy": 0.0, "winner": "ai", "ai_time": 0.0002765655517578125, "regex_time": 9.059906005859375e-06}, {"test_case": "Cryptocurrency names", "expected": ["BTC", "ETH"], "ai_symbols": ["THINK", "ETH", "DO", "BTC", "WHAT", "ABOUT"], "regex_symbols": [], "ai_accuracy": 0.5, "regex_accuracy": 0.0, "winner": "ai", "ai_time": 0.0003066062927246094, "regex_time": 8.58306884765625e-06}, {"test_case": "Financial company names", "expected": ["BAC", "JPM"], "ai_symbols": ["OF", "JPM", "BAC", "VS", "CHASE", "BANK"], "regex_symbols": [], "ai_accuracy": 0.5, "regex_accuracy": 0.0, "winner": "ai", "ai_time": 0.00028061866760253906, "regex_time": 8.821487426757812e-06}, {"test_case": "Single company name query", "expected": ["DIS"], "ai_symbols": ["GOOD", "BUY", "DIS", "IS", "AT"], "regex_symbols": [], "ai_accuracy": 0.*****************, "regex_accuracy": 0.0, "winner": "ai", "ai_time": 0.0003063678741455078, "regex_time": 9.5367431640625e-06}, {"test_case": "Direct symbol mention", "expected": ["NVDA"], "ai_symbols": ["BEAT", "NVDA"], "regex_symbols": [], "ai_accuracy": 0.****************, "regex_accuracy": 0.0, "winner": "ai", "ai_time": 0.00028395652770996094, "regex_time": 9.775161743164062e-06}, {"test_case": "Complex ticker with class", "expected": ["BRK.B"], "ai_symbols": ["BRK.B", "B", "CLASS"], "regex_symbols": [], "ai_accuracy": 0.5, "regex_accuracy": 0.0, "winner": "ai", "ai_time": 0.00024628639221191406, "regex_time": 8.344650268554688e-06}, {"test_case": "No symbols mentioned", "expected": [], "ai_symbols": ["TODAY", "IS"], "regex_symbols": [], "ai_accuracy": 0.0, "regex_accuracy": 1.0, "winner": "regex", "ai_time": 0.00026297569274902344, "regex_time": 9.059906005859375e-06}]}