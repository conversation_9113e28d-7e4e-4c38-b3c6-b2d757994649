# Audit Similarity Report
Generated: 2025-09-19 13:05 UTC
Source: architecture.json (AST-based scan)

### Duplicate exact filenames across different paths
- __init__.py
  - src/__init__.py
  - src/analysis/__init__.py
  - src/analysis/ai/__init__.py
  - src/analysis/ai/calculators/__init__.py
  - src/analysis/fundamental/__init__.py
  - src/analysis/fundamental/calculators/__init__.py
  - src/analysis/orchestration/__init__.py
  - src/analysis/probability/__init__.py
  - src/analysis/risk/__init__.py
  - src/analysis/risk/calculators/__init__.py
  - src/analysis/technical/__init__.py
  - src/analysis/templates/__init__.py
  - src/analysis/utils/__init__.py
  - src/api/__init__.py
  - src/api/analytics/__init__.py
  - src/api/data/__init__.py
  - src/api/data/providers/__init__.py
  - src/api/data/providers/modules/__init__.py
  - src/api/middleware/__init__.py
  - src/api/routers/__init__.py
  - src/api/routes/__init__.py
  - src/api/schemas/__init__.py
  - src/api/webhooks/__init__.py
  - src/bot/__init__.py
  - src/bot/audit/__init__.py
  - src/bot/enhancements/__init__.py
  - src/bot/events/__init__.py
  - src/bot/extensions/__init__.py
  - src/bot/monitoring/__init__.py
  - src/bot/pipeline/__init__.py
  - src/bot/pipeline/ask/__init__.py
  - src/bot/pipeline/ask/stages/__init__.py
  - src/bot/pipeline/commands/__init__.py
  - src/bot/pipeline/commands/analyze/__init__.py
  - src/bot/pipeline/commands/analyze/stages/__init__.py
  - src/bot/pipeline/commands/ask/__init__.py
  - src/bot/pipeline/commands/ask/modules/__init__.py
  - src/bot/pipeline/commands/ask/modules/config/__init__.py
  - src/bot/pipeline/commands/ask/modules/models/__init__.py
  - src/bot/pipeline/commands/ask/modules/services/__init__.py
  - src/bot/pipeline/commands/ask/modules/tests/__init__.py
  - src/bot/pipeline/commands/ask/modules/utils/__init__.py
  - src/bot/pipeline/commands/ask/stages/__init__.py
  - src/bot/pipeline/commands/ask/stages/core/__init__.py
  - src/bot/pipeline/commands/ask/stages/postprocessor/__init__.py
  - src/bot/pipeline/commands/ask/stages/preprocessor/__init__.py
  - src/bot/pipeline/commands/ask/stages/utils/__init__.py
  - src/bot/pipeline/commands/watchlist/__init__.py
  - src/bot/pipeline/commands/watchlist/stages/__init__.py
  - src/bot/pipeline/core/__init__.py
  - src/bot/pipeline/data/__init__.py
  - src/bot/pipeline/logs/__init__.py
  - src/bot/pipeline/monitoring/__init__.py
  - src/bot/pipeline/shared/__init__.py
  - src/bot/pipeline/shared/data_collectors/__init__.py
  - src/bot/pipeline/shared/formatters/__init__.py
  - src/bot/pipeline/shared/validators/__init__.py
  - src/bot/pipeline/utils/__init__.py
  - src/bot/security/__init__.py
  - src/bot/utils/__init__.py
  - src/core/__init__.py
  - src/core/automation/__init__.py
  - src/core/enums/__init__.py
  - src/core/formatting/__init__.py
  - src/core/monitoring_pkg/__init__.py
  - src/core/prompts/__init__.py
  - src/core/prompts/templates/__init__.py
  - src/core/risk_management/__init__.py
  - src/core/validation/__init__.py
  - src/core/watchlist/__init__.py
  - src/data/__init__.py
  - src/data/cache/__init__.py
  - src/data/models/__init__.py
  - src/database/__init__.py
  - src/database/migrations/__init__.py
  - src/database/models/__init__.py
  - src/database/repositories/__init__.py
  - src/logs/__init__.py
  - src/security/__init__.py
  - src/services/__init__.py
  - src/shared/__init__.py
  - src/shared/ai/__init__.py
  - src/shared/ai_chat/__init__.py
  - src/shared/ai_debugger/__init__.py
  - src/shared/ai_services/__init__.py
  - src/shared/analytics/__init__.py
  - src/shared/background/__init__.py
  - src/shared/background/tasks/__init__.py
  - src/shared/configuration/__init__.py
  - src/shared/data_providers/__init__.py
  - src/shared/database/__init__.py
  - src/shared/error_handling/__init__.py
  - src/shared/market_analysis/__init__.py
  - src/shared/monitoring/__init__.py
  - src/shared/redis/__init__.py
  - src/shared/sentiment/__init__.py
  - src/shared/technical_analysis/__init__.py
  - src/shared/utils/__init__.py
  - src/shared/watchlist/__init__.py
  - src/templates/__init__.py
- ai_client.py
  - src/bot/pipeline/commands/ask/stages/core/ai_client.py
  - src/shared/ai_chat/ai_client.py
- alerts.py
  - src/bot/extensions/alerts.py
  - src/database/models/alerts.py
- alpha_vantage.py
  - src/api/data/providers/alpha_vantage.py
  - src/shared/data_providers/alpha_vantage.py
- base.py
  - src/api/data/providers/base.py
  - src/bot/pipeline/commands/ask/stages/core/base.py
  - src/shared/data_providers/base.py
- circuit_breaker.py
  - src/bot/pipeline/core/circuit_breaker.py
  - src/bot/pipeline/utils/circuit_breaker.py
  - src/shared/ai_services/circuit_breaker.py
- config.py
  - src/api/config.py
  - src/api/data/providers/modules/config.py
  - src/bot/pipeline/commands/ask/config.py
  - src/bot/pipeline/commands/ask/stages/config.py
  - src/database/config.py
  - src/shared/ai_chat/config.py
  - src/shared/technical_analysis/config.py
- config_manager.py
  - src/core/config_manager.py
  - src/shared/config/config_manager.py
- conversation_memory_service.py
  - src/bot/pipeline/ask/stages/conversation_memory_service.py
  - src/bot/pipeline/commands/ask/stages/conversation_memory_service.py
- enhancement_strategy.py
  - src/analysis/ai/enhancement_strategy.py
  - src/analysis/orchestration/enhancement_strategy.py
- error_handler.py
  - src/bot/core/error_handler.py
  - src/bot/extensions/error_handler.py
  - src/bot/pipeline/commands/ask/error_handler.py
  - src/bot/pipeline/commands/ask/stages/core/error_handler.py
  - src/bot/utils/error_handler.py
- fallback_handler.py
  - src/bot/pipeline/commands/ask/stages/utils/fallback_handler.py
  - src/shared/ai_services/fallback_handler.py
- indicators.py
  - src/data/models/indicators.py
  - src/shared/background/tasks/indicators.py
  - src/shared/technical_analysis/indicators.py
- main.py
  - src/api/main.py
  - src/bot/main.py
  - src/main.py
- market_data.py
  - src/api/routers/market_data.py
  - src/api/routes/market_data.py
  - src/database/models/market_data.py
- metrics.py
  - src/analysis/fundamental/metrics.py
  - src/api/data/metrics.py
  - src/api/routes/metrics.py
  - src/bot/pipeline/utils/metrics.py
- metrics_collector.py
  - src/bot/metrics_collector.py
  - src/bot/pipeline/commands/ask/stages/postprocessor/metrics_collector.py
- models.py
  - src/bot/pipeline/commands/ask/stages/models.py
  - src/core/prompts/models.py
  - src/shared/ai_chat/models.py
  - src/shared/watchlist/models.py
- parallel_pipeline.py
  - src/bot/pipeline/commands/analyze/parallel_pipeline.py
  - src/bot/pipeline/core/parallel_pipeline.py
- performance_monitor.py
  - src/bot/extensions/performance_monitor.py
  - src/shared/monitoring/performance_monitor.py
  - src/shared/services/performance_monitor.py
- performance_optimizer.py
  - src/bot/pipeline/performance_optimizer.py
  - src/shared/ai_services/performance_optimizer.py
- performance_tracker.py
  - src/core/monitoring_pkg/performance_tracker.py
  - src/shared/analytics/performance_tracker.py
- pipeline.py
  - src/bot/pipeline/commands/analyze/pipeline.py
  - src/bot/pipeline/commands/ask/pipeline.py
- pipeline_engine.py
  - src/bot/pipeline/core/pipeline_engine.py
  - src/core/pipeline_engine.py
- rate_limiter.py
  - src/bot/audit/rate_limiter.py
  - src/bot/pipeline/commands/ask/stages/utils/rate_limiter.py
  - src/bot/rate_limiter.py
  - src/bot/utils/rate_limiter.py
- recommendation_engine.py
  - src/analysis/ai/recommendation_engine.py
  - src/shared/ai/recommendation_engine.py
- response_formatter.py
  - src/bot/pipeline/commands/ask/stages/postprocessor/response_formatter.py
  - src/shared/ai_chat/response_formatter.py
- response_generator.py
  - src/bot/pipeline/commands/ask/stages/postprocessor/response_generator.py
  - src/core/response_generator.py
- response_templates.py
  - src/bot/pipeline/commands/ask/stages/response_templates.py
  - src/core/formatting/response_templates.py
- retry.py
  - src/bot/pipeline/commands/ask/modules/utils/retry.py
  - src/shared/error_handling/retry.py
- technical_analysis.py
  - src/bot/pipeline/commands/analyze/stages/technical_analysis.py
  - src/core/formatting/technical_analysis.py
- utility.py
  - src/bot/extensions/utility.py
  - src/bot/pipeline/commands/ask/utility.py
- utils.py
  - src/core/utils.py
  - src/shared/market_analysis/utils.py
- validation.py
  - src/api/data/providers/modules/validation.py
  - src/bot/pipeline/commands/ask/modules/utils/validation.py
- validators.py
  - src/bot/pipeline/commands/ask/modules/utils/validators.py
  - src/shared/configuration/validators.py
- zones.py
  - src/bot/extensions/zones.py
  - src/shared/technical_analysis/zones.py

### Duplicate file stems (same name ignoring extension)
- __init__
  - src/__init__.py
  - src/analysis/__init__.py
  - src/analysis/ai/__init__.py
  - src/analysis/ai/calculators/__init__.py
  - src/analysis/fundamental/__init__.py
  - src/analysis/fundamental/calculators/__init__.py
  - src/analysis/orchestration/__init__.py
  - src/analysis/probability/__init__.py
  - src/analysis/risk/__init__.py
  - src/analysis/risk/calculators/__init__.py
  - src/analysis/technical/__init__.py
  - src/analysis/templates/__init__.py
  - src/analysis/utils/__init__.py
  - src/api/__init__.py
  - src/api/analytics/__init__.py
  - src/api/data/__init__.py
  - src/api/data/providers/__init__.py
  - src/api/data/providers/modules/__init__.py
  - src/api/middleware/__init__.py
  - src/api/routers/__init__.py
  - src/api/routes/__init__.py
  - src/api/schemas/__init__.py
  - src/api/webhooks/__init__.py
  - src/bot/__init__.py
  - src/bot/audit/__init__.py
  - src/bot/enhancements/__init__.py
  - src/bot/events/__init__.py
  - src/bot/extensions/__init__.py
  - src/bot/monitoring/__init__.py
  - src/bot/pipeline/__init__.py
  - src/bot/pipeline/ask/__init__.py
  - src/bot/pipeline/ask/stages/__init__.py
  - src/bot/pipeline/commands/__init__.py
  - src/bot/pipeline/commands/analyze/__init__.py
  - src/bot/pipeline/commands/analyze/stages/__init__.py
  - src/bot/pipeline/commands/ask/__init__.py
  - src/bot/pipeline/commands/ask/modules/__init__.py
  - src/bot/pipeline/commands/ask/modules/config/__init__.py
  - src/bot/pipeline/commands/ask/modules/models/__init__.py
  - src/bot/pipeline/commands/ask/modules/services/__init__.py
  - src/bot/pipeline/commands/ask/modules/tests/__init__.py
  - src/bot/pipeline/commands/ask/modules/utils/__init__.py
  - src/bot/pipeline/commands/ask/stages/__init__.py
  - src/bot/pipeline/commands/ask/stages/core/__init__.py
  - src/bot/pipeline/commands/ask/stages/postprocessor/__init__.py
  - src/bot/pipeline/commands/ask/stages/preprocessor/__init__.py
  - src/bot/pipeline/commands/ask/stages/utils/__init__.py
  - src/bot/pipeline/commands/watchlist/__init__.py
  - src/bot/pipeline/commands/watchlist/stages/__init__.py
  - src/bot/pipeline/core/__init__.py
  - src/bot/pipeline/data/__init__.py
  - src/bot/pipeline/logs/__init__.py
  - src/bot/pipeline/monitoring/__init__.py
  - src/bot/pipeline/shared/__init__.py
  - src/bot/pipeline/shared/data_collectors/__init__.py
  - src/bot/pipeline/shared/formatters/__init__.py
  - src/bot/pipeline/shared/validators/__init__.py
  - src/bot/pipeline/utils/__init__.py
  - src/bot/security/__init__.py
  - src/bot/utils/__init__.py
  - src/core/__init__.py
  - src/core/automation/__init__.py
  - src/core/enums/__init__.py
  - src/core/formatting/__init__.py
  - src/core/monitoring_pkg/__init__.py
  - src/core/prompts/__init__.py
  - src/core/prompts/templates/__init__.py
  - src/core/risk_management/__init__.py
  - src/core/validation/__init__.py
  - src/core/watchlist/__init__.py
  - src/data/__init__.py
  - src/data/cache/__init__.py
  - src/data/models/__init__.py
  - src/database/__init__.py
  - src/database/migrations/__init__.py
  - src/database/models/__init__.py
  - src/database/repositories/__init__.py
  - src/logs/__init__.py
  - src/security/__init__.py
  - src/services/__init__.py
  - src/shared/__init__.py
  - src/shared/ai/__init__.py
  - src/shared/ai_chat/__init__.py
  - src/shared/ai_debugger/__init__.py
  - src/shared/ai_services/__init__.py
  - src/shared/analytics/__init__.py
  - src/shared/background/__init__.py
  - src/shared/background/tasks/__init__.py
  - src/shared/configuration/__init__.py
  - src/shared/data_providers/__init__.py
  - src/shared/database/__init__.py
  - src/shared/error_handling/__init__.py
  - src/shared/market_analysis/__init__.py
  - src/shared/monitoring/__init__.py
  - src/shared/redis/__init__.py
  - src/shared/sentiment/__init__.py
  - src/shared/technical_analysis/__init__.py
  - src/shared/utils/__init__.py
  - src/shared/watchlist/__init__.py
  - src/templates/__init__.py
- ai_client
  - src/bot/pipeline/commands/ask/stages/core/ai_client.py
  - src/shared/ai_chat/ai_client.py
- alerts
  - src/bot/extensions/alerts.py
  - src/database/models/alerts.py
- alpha_vantage
  - src/api/data/providers/alpha_vantage.py
  - src/shared/data_providers/alpha_vantage.py
- base
  - src/api/data/providers/base.py
  - src/bot/pipeline/commands/ask/stages/core/base.py
  - src/shared/data_providers/base.py
- circuit_breaker
  - src/bot/pipeline/core/circuit_breaker.py
  - src/bot/pipeline/utils/circuit_breaker.py
  - src/shared/ai_services/circuit_breaker.py
- config
  - src/api/config.py
  - src/api/data/providers/modules/config.py
  - src/bot/pipeline/commands/ask/config.py
  - src/bot/pipeline/commands/ask/config.yaml
  - src/bot/pipeline/commands/ask/stages/config.py
  - src/database/config.py
  - src/shared/ai_chat/config.py
  - src/shared/technical_analysis/config.py
- config_manager
  - src/core/config_manager.py
  - src/shared/config/config_manager.py
- conversation_memory_service
  - src/bot/pipeline/ask/stages/conversation_memory_service.py
  - src/bot/pipeline/commands/ask/stages/conversation_memory_service.py
- enhancement_strategy
  - src/analysis/ai/enhancement_strategy.py
  - src/analysis/orchestration/enhancement_strategy.py
- error_handler
  - src/bot/core/error_handler.py
  - src/bot/extensions/error_handler.py
  - src/bot/pipeline/commands/ask/error_handler.py
  - src/bot/pipeline/commands/ask/stages/core/error_handler.py
  - src/bot/utils/error_handler.py
- fallback_handler
  - src/bot/pipeline/commands/ask/stages/utils/fallback_handler.py
  - src/shared/ai_services/fallback_handler.py
- indicators
  - src/data/models/indicators.py
  - src/shared/background/tasks/indicators.py
  - src/shared/technical_analysis/indicators.py
- main
  - src/api/main.py
  - src/bot/main.py
  - src/main.py
- market_data
  - src/api/routers/market_data.py
  - src/api/routes/market_data.py
  - src/database/models/market_data.py
- metrics
  - src/analysis/fundamental/metrics.py
  - src/api/data/metrics.py
  - src/api/routes/metrics.py
  - src/bot/pipeline/utils/metrics.py
- metrics_collector
  - src/bot/metrics_collector.py
  - src/bot/pipeline/commands/ask/stages/postprocessor/metrics_collector.py
- models
  - src/bot/pipeline/commands/ask/stages/models.py
  - src/core/prompts/models.py
  - src/shared/ai_chat/models.py
  - src/shared/watchlist/models.py
- parallel_pipeline
  - src/bot/pipeline/commands/analyze/parallel_pipeline.py
  - src/bot/pipeline/core/parallel_pipeline.py
- performance_monitor
  - src/bot/extensions/performance_monitor.py
  - src/shared/monitoring/performance_monitor.py
  - src/shared/services/performance_monitor.py
- performance_optimizer
  - src/bot/pipeline/performance_optimizer.py
  - src/shared/ai_services/performance_optimizer.py
- performance_tracker
  - src/core/monitoring_pkg/performance_tracker.py
  - src/shared/analytics/performance_tracker.py
- pipeline
  - src/bot/pipeline/commands/analyze/pipeline.py
  - src/bot/pipeline/commands/ask/pipeline.py
- pipeline_engine
  - src/bot/pipeline/core/pipeline_engine.py
  - src/core/pipeline_engine.py
- rate_limiter
  - src/bot/audit/rate_limiter.py
  - src/bot/pipeline/commands/ask/stages/utils/rate_limiter.py
  - src/bot/rate_limiter.py
  - src/bot/utils/rate_limiter.py
- recommendation_engine
  - src/analysis/ai/recommendation_engine.py
  - src/shared/ai/recommendation_engine.py
- response_formatter
  - src/bot/pipeline/commands/ask/stages/postprocessor/response_formatter.py
  - src/shared/ai_chat/response_formatter.py
- response_generator
  - src/bot/pipeline/commands/ask/stages/postprocessor/response_generator.py
  - src/core/response_generator.py
- response_templates
  - src/bot/pipeline/commands/ask/stages/response_templates.py
  - src/core/formatting/response_templates.py
- retry
  - src/bot/pipeline/commands/ask/modules/utils/retry.py
  - src/shared/error_handling/retry.py
- technical_analysis
  - src/bot/pipeline/commands/analyze/stages/technical_analysis.py
  - src/core/formatting/technical_analysis.py
- utility
  - src/bot/extensions/utility.py
  - src/bot/pipeline/commands/ask/utility.py
- utils
  - src/core/utils.py
  - src/shared/market_analysis/utils.py
- validation
  - src/api/data/providers/modules/validation.py
  - src/bot/pipeline/commands/ask/modules/utils/validation.py
- validators
  - src/bot/pipeline/commands/ask/modules/utils/validators.py
  - src/shared/configuration/validators.py
- zones
  - src/bot/extensions/zones.py
  - src/shared/technical_analysis/zones.py

### Duplicate class names across files
- None found

### Duplicate top-level function names across files
- None found

## Heuristics to flag potential canonical vs wrapper pairs
- Look for pairs where one path is under src/shared/* or src/bot/* and another is under src/api/*, src/core/*, or src/data/*
- Paths containing 'legacy', 'deprecated', or old location patterns (e.g., src/data/providers) are likely wrappers/old
- Confirm with grep usages before removal; consider git blame/history for migration intent

---

### Import Usage (auto-appended) — 2025-09-19 13:18 UTC
- src.shared.ai_services.ai_chat_processor: 21 imports
- src.shared.ai_services.ai_processor_robust: 7 imports
- src.bot.pipeline.commands.ask.executor: 12 imports
- src.bot.pipeline.commands.ask.pipeline: 12 imports
- src.bot.pipeline.commands.analyze.pipeline: 12 imports
- src.bot.pipeline.commands.analyze.parallel_pipeline: 8 imports
- src.api.data.providers: 41 imports
- src.shared.data_providers: 117 imports