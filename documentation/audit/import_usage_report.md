# Import Usage Report
Generated: 2025-09-19 13:18 UTC

## Key module usage (canonical vs legacy focus)

- src.shared.data_providers.aggregator: 41 imports
- src.shared.ai_services.ai_chat_processor: 21 imports
- src.shared.data_providers.unified_base: 19 imports
- src.shared.data_providers.alpha_vantage: 15 imports
- src.shared.data_providers.yfinance_provider: 15 imports
- src.api.data.providers.base: 13 imports
- src.api.data.providers.data_source_manager: 13 imports
- src.bot.pipeline.commands.analyze.pipeline: 12 imports
- src.bot.pipeline.commands.ask.pipeline: 12 imports
- src.bot.pipeline.commands.ask.executor: 10 imports
- src.shared.data_providers.alpaca_provider: 9 imports
- src.api.data.providers.finnhub: 9 imports
- src.bot.pipeline.commands.analyze.parallel_pipeline: 8 imports
- src.shared.data_providers.finnhub_provider: 7 imports
- src.shared.ai_services.ai_processor_robust: 7 imports
- src.shared.data_providers.polygon_provider: 6 imports
- src.api.data.providers.polygon: 6 imports
- src.shared.data_providers: 4 imports
- src.bot.pipeline.commands.ask.executor_with_grading: 2 imports
- src.shared.data_providers.yfinance: 1 imports

## Details for key modules (first few files)

### src.shared.data_providers.aggregator (41 imports)
- fixed_ai_service_wrapper.py
- scripts/print_zones.py
- src/analysis/orchestration/analysis_orchestrator.py
- src/api/routers/market_data.py
- src/bot/client.py
- src/bot/core/services.py
- src/bot/extensions/portfolio.py
- src/bot/pipeline/commands/analyze/parallel_pipeline.py
- src/bot/pipeline/commands/analyze/pipeline.py
- src/bot/pipeline/commands/analyze/stages/fetch_data.py

### src.shared.ai_services.ai_chat_processor (21 imports)
- scripts/test_full_analysis.py
- src/bot/client.py
- src/bot/core/services.py
- src/bot/pipeline/commands/ask/stages/ask_sections.py
- src/shared/ai_services/intelligent_chatbot.py
- test_hybrid_ai.py
- test_simple_ai.py
- tests/test_ai_chat_processor.py
- tests/test_ai_response.py
- tests/test_backward_compatibility.py

### src.shared.data_providers.unified_base (19 imports)
- src/api/data/providers/finnhub.py
- src/api/data/providers/polygon.py
- src/api/routers/market_data.py
- src/api/routes/analytics.py
- src/data/__init__.py
- tests/integration/test_polygon_provider.py
- tests/test_consolidated_providers.py
- tests/test_data_provider_system.py

### src.shared.data_providers.alpha_vantage (15 imports)
- src/core/automation/report_engine.py
- src/data/__init__.py
- tests/integration/test_alpha_vantage_provider.py
- tests/test_alpha_vantage_config.py
- tests/test_alpha_vantage_integration.py
- tests/test_batch_processing.py
- tests/test_config_integration.py
- tests/test_consolidated_providers.py
- tests/test_data_provider_system.py
- tests/test_provider_status.py

### src.shared.data_providers.yfinance_provider (15 imports)
- src/api/data/providers/data_source_manager.py
- src/core/automation/report_engine.py
- src/data/__init__.py
- tests/test_batch_processing.py
- tests/test_consolidated_providers.py
- tests/test_data_provider_integration_fix.py
- tests/test_data_provider_system.py
- tests/test_market_hours.py
- tests/test_provider_status.py

### src.api.data.providers.base (13 imports)
- src/api/data/cache.py
- src/api/data/market_data_service.py
- src/api/data/metrics.py
- src/api/data/providers/alpha_vantage.py
- src/api/routes/market_data.py
- tests/integration/test_alpha_vantage_provider.py
- tests/integration/test_market_data_service.py
- tests/test_provider_attribution.py

### src.api.data.providers.data_source_manager (13 imports)
- src/analysis/orchestration/analysis_orchestrator.py
- src/api/data/cache.py
- src/api/data/market_data_service.py
- src/api/routes/analytics.py
- tests/test_consolidated_providers.py
- tests/test_data_provider_integration_fix.py
- tests/test_fixes.py
- tests/test_real_data_quality.py
- tests/test_technical_indicators.py

### src.bot.pipeline.commands.analyze.pipeline (12 imports)
- src/bot/extensions/batch_analyze.py
- src/shared/ai_services/tool_registry.py
- tests/test_analyze_pipeline.py
- tests/test_bot_pipeline_system.py
- tests/test_enhanced_stage_only.py
- tests/test_integrated_analysis.py
- tests/test_production_deployment.py
- tests/test_show_full_report.py

### src.bot.pipeline.commands.ask.pipeline (12 imports)
- fix_mock_issue.py
- src/bot/pipeline/test_pipeline.py
- tests/load/test_bot_load.py
- tests/test_ai_pipeline.py
- tests/test_comprehensive_pipeline.py
- tests/test_debug_logging.py
- tests/test_discord_interaction.py
- tests/test_fixed_pipeline.py
- tests/test_multi_symbol_integration.py

### src.bot.pipeline.commands.ask.executor (10 imports)
- src/bot/extensions/ask.py
- test_ask_command_hallucination_fix.py
- test_discord_ask.py
- tests/e2e/test_bot_commands.py
- tests/live_command_tester.py
- tests/test_ask_command.py
- tests/test_bot_pipeline_system.py
- tests/test_different_questions.py
- tests/test_pipeline_completion_issues.py

### src.shared.data_providers.alpaca_provider (9 imports)
- src/api/data/providers/data_source_manager.py
- src/core/automation/report_engine.py
- tests/test_alpaca_data.py
- tests/test_consolidated_providers.py

### src.api.data.providers.finnhub (9 imports)
- tests/integration/test_qqq_options_estimation.py
- tests/integration/test_supertrend_analysis.py
- tests/test_comprehensive.py
- tests/test_consolidated_providers.py
- tests/test_finnhub_provider.py
- tests/test_provider_status.py

### src.bot.pipeline.commands.analyze.parallel_pipeline (8 imports)
- src/bot/extensions/analyze.py
- src/bot/extensions/recommendations.py
- src/bot/extensions/zones.py
- tests/e2e/test_bot_commands.py
- tests/load/test_bot_load.py

### src.shared.data_providers.finnhub_provider (7 imports)
- src/api/data/providers/data_source_manager.py
- src/core/automation/report_engine.py
- src/data/__init__.py
- tests/test_data_provider_system.py

### src.shared.ai_services.ai_processor_robust (7 imports)
- src/bot/pipeline/commands/ask/pipeline.py
- src/shared/ai_services/ai_chat_processor.py
- test_ai_improvements.py
- test_data_issue.py
- test_robust_processor.py

### src.shared.data_providers.polygon_provider (6 imports)
- src/api/data/providers/data_source_manager.py
- src/core/automation/report_engine.py
- src/data/__init__.py

### src.api.data.providers.polygon (6 imports)
- tests/integration/test_polygon_provider.py
- tests/test_consolidated_providers.py
- tests/test_provider_status.py

### src.shared.data_providers (4 imports)
- src/bot/pipeline/commands/analyze/parallel_pipeline.py
- src/bot/pipeline/commands/analyze/pipeline.py

### src.bot.pipeline.commands.ask.executor_with_grading (2 imports)
- src/bot/client_with_monitoring.py

### src.shared.data_providers.yfinance (1 imports)
- tests/test_config_integration.py
