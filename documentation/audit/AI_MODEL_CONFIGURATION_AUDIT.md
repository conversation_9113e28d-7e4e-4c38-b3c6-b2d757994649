# AI Model Configuration Chaos Audit

## 🚨 Current State: Complete Chaos

### The Problem
We have **15+ files with hardcoded models**, **20+ environment variables** for what should be **6 simple AI jobs**. This is a maintenance nightmare with:

- Random models for random jobs - no strategy
- Complex routing systems that aren't used - over-engineering  
- Conflicting configurations across files
- Hardcoded models scattered everywhere
- Duplicate environment variables with different names

## 📊 Hardcoded Models Found

### Primary Offenders (Source Code)
1. **src/bot/extensions/ask_ai_first_fixed.py:62**
   - Hardcoded: `"deepcogito/cogito-v2-preview-deepseek-671b"`
   - Usage: AI client override

2. **src/shared/ai_services/smart_model_router.py:184,199,214**
   - Hardcoded fallbacks: `'moonshotai/kimi-k2-0905'`, `'anthropic/claude-3.5-sonnet'`
   - Usage: Default model fallbacks

3. **src/shared/ai_chat/ai_client.py** (implied from search)
   - Hardcoded fallback models

### Configuration Files (Expected but Inconsistent)
- **config/services/ai_models.yaml**: Over-engineered with 10+ models
- **docker-compose.yml:64-69**: 6 different model environment variables
- **.env:87-94**: 4 different model variables with different names

### Test Files (Lower Priority)
- test_model_comparison.py
- test_fixed_ai_first.py  
- test_upgraded_prompts.py

## 🔧 Environment Variable Chaos

### Current Variables (15+ total)
```bash
# From .env
AI_MODEL="moonshotai/kimi-k2-0905"
MODEL_GLOBAL_FALLBACK="moonshotai/kimi-k2-0905"  
MODEL_LLM="deepcogito/cogito-v2-preview-deepseek-671b"
MODEL_QUICK="moonshotai/kimi-k2-0905"
MODEL_ANALYSIS="deepcogito/cogito-v2-preview-deepseek-671b"
MODEL_HEAVY="deepcogito/cogito-v2-preview-deepseek-671b"

# From docker-compose.yml (different names!)
AI_MODEL=gpt-4o-mini
MODEL_QUICK=gpt-4o-mini
MODEL_ANALYSIS=gpt-4o-mini  
MODEL_HEAVY=gpt-4o-mini
MODEL_GLOBAL_FALLBACK=gpt-4o-mini
MODEL_LLM=gpt-4o-mini
```

**CONFLICT**: .env uses expensive models, docker-compose uses cheap gpt-4o-mini!

## 🎯 The 6 Actual AI Jobs

Based on code analysis, we only need models for:

1. **Symbol Extraction** - Parse "AAPL" from "How is Apple doing?"
2. **Intent Classification** - Determine what user wants  
3. **Market Analysis** - Analyze market data and trends
4. **Technical Analysis** - Chart patterns, indicators
5. **Risk Assessment** - Portfolio risk, compliance
6. **User Explanations** - Natural language responses

## 🏗️ Over-Engineered Systems Not Used

### Complex Routing (Unused)
- `src/bot/pipeline/commands/ask/stages/ai_routing_service.py`
- `src/shared/ai_services/cross_validation_ai.py`
- Complex task routing in ai_models.yaml

### Excessive Model Definitions
- 10+ models defined in YAML for 6 jobs
- Environment-specific overrides for dev/prod/test
- Provider-specific configurations
- Performance monitoring configs

## 🎯 Target Simple System

### Proposed Environment Variables (6 total)
```bash
# Core models
AI_MODEL_QUICK="gpt-4o-mini"           # Symbol extraction, intent
AI_MODEL_ANALYSIS="claude-3.5-sonnet"  # Market/technical analysis  
AI_MODEL_HEAVY="cogito-v2"             # Risk assessment, complex reasoning
AI_MODEL_FALLBACK="gpt-4o-mini"        # When others fail

# Provider config
OPENROUTER_API_KEY="..."
AI_TIMEOUT_SECONDS="30"
```

### Single Configuration File
- One simple YAML with 4 models max
- No complex routing logic
- No environment overrides
- No unused provider configs

## 📋 Cleanup Priority

### High Priority (Actually Used)
1. ✅ **ask_ai_first_fixed.py** - Remove hardcoded model
2. ✅ **smart_model_router.py** - Simplify to 4 models
3. ✅ **ai_client.py** - Remove hardcoded fallbacks
4. ✅ **.env** - Consolidate variables
5. ✅ **docker-compose.yml** - Fix conflicts

### Medium Priority  
6. ✅ **ai_models.yaml** - Simplify dramatically
7. ✅ Test files - Update to use config

### Low Priority (Remove)
8. ✅ **ai_routing_service.py** - Delete (unused)
9. ✅ **cross_validation_ai.py** - Delete (unused)
10. ✅ Complex routing configs - Delete

## 🚀 Success Metrics

- **From 15+ files → 4 files** with model references
- **From 20+ env vars → 6 env vars** 
- **From 10+ models → 4 models**
- **From 3 config files → 1 config file**
- **Zero hardcoded models** in source code
- **All 6 AI jobs still work** perfectly

This audit shows we can reduce complexity by **80%** while maintaining all functionality.
