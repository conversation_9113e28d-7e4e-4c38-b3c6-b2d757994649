{"summary": {"total_patterns": 1, "ai_replacement_candidates": 0, "replacement_percentage": 0.0}, "by_complexity": {"critical": 1}, "by_priority": {"keep": 1}, "by_category": {"security": 1}, "top_files": [["./src/bot/utils/enhanced_input_validator.py", 1]], "ai_candidates": [], "detailed_patterns": [{"pattern": "[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]", "file": "./src/bot/utils/enhanced_input_validator.py", "line": 278, "function": "_basic_sanitization", "complexity": "critical", "ai_priority": "keep", "category": "security", "description": "Pattern: [\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]"}]}