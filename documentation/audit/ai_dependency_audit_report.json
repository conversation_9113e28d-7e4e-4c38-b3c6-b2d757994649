{"summary": {"total_ai_files": 22, "used_files": 18, "unused_files": 4, "usage_percentage": 81.8, "total_import_statements": 79}, "used_files": [{"file": "src/shared/ai_services/ai_chat_processor.py", "import_frequency": 17, "classes": ["AIChatProcessorWrapper"], "functions": ["create_processor", "__init__", "_initialize_components", "_generate_final_response"], "line_count": 142, "file_size": 5149}, {"file": "src/shared/ai_services/ai_processor_robust.py", "import_frequency": 7, "classes": ["AnalysisType", "DataQuality", "ValidationMode", "ValidationResult", "TechnicalIndicators", "AntiHallucinationValidator", "MarketSnapshot", "TechnicalCalculator", "AnalysisSelector", "Response<PERSON>ormatter", "RobustFinancialAnalyzer", "CleanAIProcessor", "QueryResult"], "functions": ["create_processor", "to_dict", "__init__", "validate_response", "_validate_strict_mode", "_validate_price_mentions", "_validate_indicator_mentions", "_generate_pure_data_response", "is_valid", "__init__", "calculate_indicators", "_calculate_rsi", "_calculate_sma", "_calculate_macd", "_calculate_support_resistance", "_calculate_bollinger_bands", "_calculate_volume_average", "__init__", "select_analyses", "_analyze_user_intent", "__init__", "format_analysis_response", "_generate_analysis_section", "_price_analysis", "_trend_analysis", "_momentum_analysis", "_support_resistance_analysis", "_rsi_analysis", "_volatility_analysis", "__init__", "_generate_fallback_response", "_assess_data_quality", "_generate_insufficient_data_response", "_generate_error_response", "__init__", "_generate_mock_historical_data", "_generate_fallback_response", "_needs_web_search", "_needs_market_data", "__post_init__", "get", "__getitem__", "__setitem__", "__contains__"], "line_count": 1565, "file_size": 65298}, {"file": "src/shared/ai_services/timeout_manager.py", "import_frequency": 6, "classes": ["TimeoutConfig", "TimeoutManager"], "functions": ["get_timeout", "__init__", "timeout_wrapper", "decorator"], "line_count": 100, "file_size": 3885}, {"file": "src/shared/ai_services/circuit_breaker.py", "import_frequency": 5, "classes": ["CircuitState", "CircuitBreakerConfig", "CircuitBreaker", "AICircuitBreakerManager"], "functions": ["ai_circuit_breaker", "__init__", "__enter__", "__exit__", "_reset", "__init__", "get_breaker", "circuit_breaker_decorator", "decorator"], "line_count": 160, "file_size": 6228}, {"file": "src/shared/ai_services/ai_service_wrapper.py", "import_frequency": 5, "classes": ["AIServiceWrapper"], "functions": ["format_technical_analysis", "__init__", "_calculate_simple_rsi", "format_technical_analysis"], "line_count": 295, "file_size": 10980}, {"file": "src/shared/ai_services/smart_model_router.py", "import_frequency": 4, "classes": ["TaskType", "ModelConfig", "SmartModelRouter"], "functions": ["__init__", "_get_default_config_path", "_load_yaml_config", "_get_config_value", "_load_model_configs", "get_optimal_model", "get_model_for_query_analysis", "get_model_for_market_analysis", "get_model_for_complex_reasoning", "log_model_usage", "get_cache_config_for_intent", "should_bypass_cache", "get_cache_ttl", "get_cost_budget_for_model_type", "should_downgrade_for_budget", "get_provider_config", "get_performance_config", "get_model_by_name", "get_all_models_for_task", "estimate_cost", "get_cheapest_model_for_task", "get_fastest_model_for_task", "get_most_accurate_model_for_task", "score_model"], "line_count": 397, "file_size": 19181}, {"file": "src/shared/ai_services/query_cache.py", "import_frequency": 3, "classes": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SimpleQueryCache"], "functions": ["get_cache_stats", "is_expired", "to_dict", "__init__", "_generate_cache_key", "get_stats"], "line_count": 171, "file_size": 5947}, {"file": "src/shared/ai_services/enhanced_symbol_extractor.py", "import_frequency": 3, "classes": ["ExtractionMethod", "SymbolExtractionResult", "EnhancedSymbolExtractor"], "functions": ["__init__", "_parse_ai_response", "_is_valid_symbol_format", "_deduplicate_results"], "line_count": 371, "file_size": 13677}, {"file": "src/shared/ai_services/intelligent_text_parser.py", "import_frequency": 3, "classes": ["ParseType", "ParseResult", "IntelligentTextParser"], "functions": ["__init__", "_extract_json_from_response", "_calculate_ai_confidence", "_deduplicate_results"], "line_count": 384, "file_size": 15407}, {"file": "src/shared/ai_services/enhanced_intent_detector.py", "import_frequency": 3, "classes": ["IntentType", "UrgencyLevel", "ResponseStyle", "IntentAnalysis", "EnhancedIntentDetector"], "functions": ["__init__", "_parse_ai_response", "_extract_basic_entities", "_create_default_analysis"], "line_count": 434, "file_size": 15820}, {"file": "src/shared/ai_services/cross_validation_ai.py", "import_frequency": 2, "classes": ["ValidationImportance", "ConsensusLevel", "ModelResponse", "CrossValidationResult", "CrossValidationAI"], "functions": ["__init__", "_select_validation_models", "_analyze_consensus", "_detect_disagreements", "_generate_consensus_response", "_requires_human_review", "_extract_key_points", "_extract_numerical_claims", "_extract_sentiment", "_extract_recommendation", "_calculate_sentiment_consensus", "_find_consensus_points", "_calculate_recommendation_consensus"], "line_count": 444, "file_size": 18543}, {"file": "src/shared/ai_services/ai_security_detector.py", "import_frequency": 2, "classes": ["SecurityThreatType", "ThreatSeverity", "SecurityAnalysis", "AISecurityDetector"], "functions": ["__init__", "_analyze_with_regex", "_extract_json_from_response", "_parse_ai_analysis"], "line_count": 318, "file_size": 12511}, {"file": "src/shared/ai_services/response_synthesizer.py", "import_frequency": 1, "classes": ["ResponseSynthesizer"], "functions": ["__init__", "synthesize_response", "_synthesize_price_response", "_synthesize_technical_response", "_synthesize_options_response", "_synthesize_comparison_response", "_synthesize_recommendation_response", "_synthesize_general_response", "_add_risk_warning", "create_conversational_response", "_get_conversational_greeting", "_get_conversational_content", "_get_follow_up_suggestions"], "line_count": 319, "file_size": 14361}, {"file": "src/shared/ai_services/tool_registry.py", "import_frequency": 1, "classes": ["ToolRegistry"], "functions": ["__init__"], "line_count": 382, "file_size": 14643}, {"file": "src/shared/ai_services/fast_price_lookup.py", "import_frequency": 1, "classes": ["FastPriceLookup"], "functions": ["__init__", "_format_price_response"], "line_count": 145, "file_size": 5276}, {"file": "src/shared/ai_services/simple_query_analyzer.py", "import_frequency": 1, "classes": ["QueryIntent", "ProcessingRoute", "SymbolInfo", "QueryAnalysis", "SimpleQueryAnalyzer"], "functions": ["analyze_simple_query", "__init__", "analyze_query", "_extract_symbols", "_is_simple_price_query"], "line_count": 233, "file_size": 8666}, {"file": "src/shared/ai_services/query_router.py", "import_frequency": 1, "classes": ["QueryType", "QueryComplexity", "QueryContext", "Query<PERSON><PERSON>er"], "functions": ["__init__", "route_query", "_extract_symbols", "_classify_query_type", "_assess_complexity", "_determine_processing_steps", "_calculate_priority", "_determine_response_style", "_estimate_duration", "_requires_market_data", "_requires_ai_reasoning"], "line_count": 384, "file_size": 14300}, {"file": "src/shared/ai_services/openrouter_key.py", "import_frequency": 1, "classes": [], "functions": [], "line_count": 9, "file_size": 277}], "unused_files": [{"file": "src/shared/ai_services/ai_processor_clean.py", "classes": ["ProcessorConfig", "QueryResult", "ResponseValidator", "AIClient", "SymbolExtractor", "CleanAIProcessor"], "functions": ["create_processor", "from_env", "__post_init__", "to_dict", "__init__", "validate_response", "_extract_numerical_values", "_check_suspicious_patterns", "_check_response_completeness", "_check_professional_standards", "sanitize_response", "__init__", "_setup_client", "_parse_response", "extract_symbols", "__init__", "_parse_ai_response", "_fallback_analysis", "_validate_and_enhance_result"], "line_count": 517, "file_size": 19298}, {"file": "src/shared/ai_services/fallback_handler.py", "classes": ["AIServiceFallbackHandler"], "functions": ["__init__", "_extract_symbols", "_classify_query_intent", "_get_response_template"], "line_count": 323, "file_size": 14069}, {"file": "src/shared/ai_services/intelligent_chatbot.py", "classes": ["QueryComplexity", "ProcessingStep", "QueryStep", "QueryPlan", "IntelligentChatbot"], "functions": ["__post_init__", "__init__", "_map_step_name_to_type"], "line_count": 652, "file_size": 28270}, {"file": "src/shared/ai_services/performance_optimizer.py", "classes": ["AIServicePerformanceOptimizer"], "functions": ["optimize_ai_service_call", "__init__", "optimize_ai_call", "decorator"], "line_count": 126, "file_size": 5382}], "top_imported_modules": [{"module": "src.shared.ai_services.ai_chat_processor", "import_count": 17}, {"module": "src.shared.ai_services.ai_processor_robust", "import_count": 7}, {"module": "src.shared.ai_services.timeout_manager", "import_count": 6}, {"module": "src.shared.ai_services.circuit_breaker", "import_count": 5}, {"module": "src.shared.ai_services.ai_service_wrapper", "import_count": 5}, {"module": "src.shared.ai_chat.ai_client", "import_count": 5}, {"module": "src.shared.ai_services.smart_model_router", "import_count": 4}, {"module": "src.shared.ai_services.query_cache", "import_count": 3}, {"module": "src.shared.ai_services.enhanced_symbol_extractor", "import_count": 3}, {"module": "src.shared.ai_services.intelligent_text_parser", "import_count": 3}], "import_details": {"src.shared.ai_services.timeout_manager": [{"file": "start_enhanced_bot.py", "line": 147, "statement": "from src.shared.ai_services.timeout_manager import timeout_manager", "names": ["timeout_manager"]}, {"file": "src/bot/__main__.py", "line": 16, "statement": "from src.shared.ai_services.timeout_manager import timeout_manager", "names": ["timeout_manager"]}, {"file": "src/bot/main.py", "line": 121, "statement": "from src.shared.ai_services.timeout_manager import timeout_manager", "names": ["timeout_manager"]}, {"file": "src/shared/ai_chat/ai_client.py", "line": 22, "statement": "from src.shared.ai_services.timeout_manager import timeout_manager", "names": ["timeout_manager"]}, {"file": "src/shared/ai_services/ai_service_wrapper.py", "line": 21, "statement": "from src.shared.ai_services.timeout_manager import timeout_manager", "names": ["timeout_manager"]}, {"file": "src/shared/ai_services/performance_optimizer.py", "line": 14, "statement": "from src.shared.ai_services.timeout_manager import timeout_manager", "names": ["timeout_manager"]}], "src.shared.ai_services.circuit_breaker": [{"file": "start_enhanced_bot.py", "line": 148, "statement": "from src.shared.ai_services.circuit_breaker import ai_circuit_breaker_manager", "names": ["ai_circuit_breaker_manager"]}, {"file": "src/bot/__main__.py", "line": 17, "statement": "from src.shared.ai_services.circuit_breaker import ai_circuit_breaker_manager", "names": ["ai_circuit_breaker_manager"]}, {"file": "src/shared/ai_chat/ai_client.py", "line": 23, "statement": "from src.shared.ai_services.circuit_breaker import ai_circuit_breaker_manager", "names": ["ai_circuit_breaker_manager"]}, {"file": "src/shared/ai_services/ai_service_wrapper.py", "line": 20, "statement": "from src.shared.ai_services.circuit_breaker import ai_circuit_breaker_manager", "names": ["ai_circuit_breaker_manager"]}, {"file": "src/shared/ai_services/performance_optimizer.py", "line": 15, "statement": "from src.shared.ai_services.circuit_breaker import ai_circuit_breaker_manager", "names": ["ai_circuit_breaker_manager"]}], "src.shared.ai_services.query_cache": [{"file": "start_enhanced_bot.py", "line": 149, "statement": "from src.shared.ai_services.query_cache import simple_query_cache", "names": ["simple_query_cache"]}, {"file": "src/shared/ai_services/ai_service_wrapper.py", "line": 18, "statement": "from src.shared.ai_services.query_cache import simple_query_cache", "names": ["simple_query_cache"]}, {"file": "src/shared/ai_services/performance_optimizer.py", "line": 16, "statement": "from src.shared.ai_services.query_cache import simple_query_cache", "names": ["simple_query_cache"]}], "src.shared.ai_services.enhanced_symbol_extractor": [{"file": "scripts/test_enhanced_symbol_extraction.py", "line": 18, "statement": "from src.shared.ai_services.enhanced_symbol_extractor import enhanced_symbol_extractor", "names": ["enhanced_symbol_extractor"]}, {"file": "src/bot/pipeline/commands/ask/stages/ai_symbol_extractor.py", "line": 101, "statement": "from src.shared.ai_services.enhanced_symbol_extractor import enhanced_symbol_extractor", "names": ["enhanced_symbol_extractor"]}, {"file": "src/shared/utils/symbol_extraction.py", "line": 413, "statement": "from src.shared.ai_services.enhanced_symbol_extractor import enhanced_symbol_extractor", "names": ["enhanced_symbol_extractor"]}], "src.shared.ai_services.intelligent_text_parser": [{"file": "scripts/test_ai_vs_regex.py", "line": 19, "statement": "from src.shared.ai_services.intelligent_text_parser import IntelligentTextParser, ParseType", "names": ["IntelligentTextParser", "ParseType"]}, {"file": "tradingview-ingest/src/ai_alert_parser.py", "line": 245, "statement": "from src.shared.ai_services.intelligent_text_parser import intelligent_parser", "names": ["intelligent_parser"]}, {"file": "src/shared/utils/symbol_extraction.py", "line": 335, "statement": "from src.shared.ai_services.intelligent_text_parser import intelligent_parser", "names": ["intelligent_parser"]}], "src.shared.ai_services.ai_chat_processor": [{"file": "scripts/test_full_analysis.py", "line": 27, "statement": "from src.shared.ai_services.ai_chat_processor import AIChatProcessor", "names": ["AIChatProcessor"]}, {"file": "tests/test_ai_chat_processor.py", "line": 13, "statement": "from src.shared.ai_services.ai_chat_processor import AIChatProcessorWrapper as AIChatProcessor", "names": ["AIChatProcessorWrapper"]}, {"file": "tests/test_ai_chat_processor.py", "line": 15, "statement": "from src.shared.ai_services.ai_chat_processor import processor", "names": ["processor"]}, {"file": "tests/test_correlation_wrappers.py", "line": 12, "statement": "from src.shared.ai_services.ai_chat_processor import AIChatProcessorWrapper", "names": ["AIChatProcessorWrapper"]}, {"file": "tests/test_correlation_standalone.py", "line": 18, "statement": "from src.shared.ai_services.ai_chat_processor import AIChatProcessorWrapper", "names": ["AIChatProcessorWrapper"]}, {"file": "tests/test_backward_compatibility.py", "line": 15, "statement": "from src.shared.ai_services.ai_chat_processor import AIChatProcessorWrapper as CanonicalAIChatProcessor", "names": ["AIChatProcessorWrapper"]}, {"file": "tests/test_backward_compatibility.py", "line": 89, "statement": "from src.shared.ai_services.ai_chat_processor import processor", "names": ["processor"]}, {"file": "tests/test_backward_compatibility.py", "line": 121, "statement": "from src.shared.ai_services.ai_chat_processor import processor", "names": ["processor"]}, {"file": "tests/test_comprehensive_pipeline.py", "line": 66, "statement": "from src.shared.ai_services.ai_chat_processor import AIChatProcessor", "names": ["AIChatProcessor"]}, {"file": "tests/test_ai_response.py", "line": 28, "statement": "from src.shared.ai_services.ai_chat_processor import AIChatProcessor", "names": ["AIChatProcessor"]}, {"file": "tests/test_full_analysis.py", "line": 27, "statement": "from src.shared.ai_services.ai_chat_processor import AIChatProcessor", "names": ["AIChatProcessor"]}, {"file": "src/bot/client.py", "line": 49, "statement": "from src.shared.ai_services.ai_chat_processor import create_processor as create_ai_service", "names": ["create_processor"]}, {"file": "src/bot/core/services.py", "line": 76, "statement": "from src.shared.ai_services.ai_chat_processor import create_processor", "names": ["create_processor"]}, {"file": "src/bot/pipeline/commands/ask/stages/ask_sections.py", "line": 47, "statement": "from src.shared.ai_services.ai_chat_processor import AIChatProcessorWrapper", "names": ["AIChatProcessorWrapper"]}, {"file": "src/shared/ai_services/intelligent_chatbot.py", "line": 21, "statement": "from src.shared.ai_services.ai_chat_processor import AIChatProcessorWrapper", "names": ["AIChatProcessorWrapper"]}, {"file": "scripts/test/test_simple_ai.py", "line": 21, "statement": "from src.shared.ai_services.ai_chat_processor import AIChatProcessorWrapper", "names": ["AIChatProcessorWrapper"]}, {"file": "scripts/test/test_hybrid_ai.py", "line": 13, "statement": "from src.shared.ai_services.ai_chat_processor import AIChatProcessorWrapper", "names": ["AIChatProcessorWrapper"]}], "src.bot.pipeline.commands.ask.stages.ai_chat_processor": [{"file": "tests/test_final_enhancements.py", "line": 30, "statement": "from src.bot.pipeline.commands.ask.stages.ai_chat_processor import AIChatProcessor", "names": ["AIChatProcessor"]}, {"file": "tests/test_market_hours_fix.py", "line": 9, "statement": "from src.bot.pipeline.commands.ask.stages.ai_chat_processor import AIChatProcessor", "names": ["AIChatProcessor"]}], "src.shared.ai_chat.models": [{"file": "tests/test_ai_chat_processor.py", "line": 17, "statement": "from src.shared.ai_chat.models import AIAskResult", "names": ["AIAskResult"]}], "src.shared.ai_services.ai_service_wrapper": [{"file": "tests/test_pipeline_optimization.py", "line": 191, "statement": "from src.shared.ai_services.ai_service_wrapper import AIChatProcessor", "names": ["AIChatProcessor"]}, {"file": "tests/test_discord_optimizations.py", "line": 119, "statement": "from src.shared.ai_services.ai_service_wrapper import ai_service", "names": ["ai_service"]}, {"file": "tests/test_backward_compatibility.py", "line": 14, "statement": "from src.shared.ai_services.ai_service_wrapper import AIServiceWrapper as CanonicalAIServiceWrapper", "names": ["AIServiceWrapper"]}, {"file": "src/bot/__main__.py", "line": 15, "statement": "from src.shared.ai_services.ai_service_wrapper import AIServiceWrapper", "names": ["AIServiceWrapper"]}, {"file": "tests/integration/test_enhanced_ai_context.py", "line": 17, "statement": "from src.shared.ai_services.ai_service_wrapper import AIServiceWrapper", "names": ["AIServiceWrapper"]}], "src.shared.ai_services.smart_model_router": [{"file": "src/bot/extensions/analyze.py", "line": 22, "statement": "from src.shared.ai_services.smart_model_router import router", "names": ["router"]}, {"file": "src/bot/extensions/ask.py", "line": 195, "statement": "from src.shared.ai_services.smart_model_router import router", "names": ["router"]}, {"file": "src/shared/ai_services/ai_processor_robust.py", "line": 20, "statement": "from src.shared.ai_services.smart_model_router import router", "names": ["router"]}, {"file": "src/shared/ai_services/cross_validation_ai.py", "line": 16, "statement": "from src.shared.ai_services.smart_model_router import SmartModelRouter", "names": ["SmartModelRouter"]}], "src.shared.ai_services.ai_security_detector": [{"file": "src/bot/utils/enhanced_input_validator.py", "line": 155, "statement": "from src.shared.ai_services.ai_security_detector import ai_security_detector", "names": ["ai_security_detector"]}, {"file": "scripts/test/test_ai_security_detection.py", "line": 15, "statement": "from src.shared.ai_services.ai_security_detector import ai_security_detector, SecurityThreatType", "names": ["ai_security_detector", "SecurityThreatType"]}], "src.shared.ai_services.ai_processor_robust": [{"file": "src/bot/pipeline/commands/ask/pipeline.py", "line": 49, "statement": "from src.shared.ai_services.ai_processor_robust import CleanAIProcessor, ValidationMode", "names": ["CleanAIProcessor", "ValidationMode"]}, {"file": "src/shared/ai_services/cross_validation_ai.py", "line": 17, "statement": "from src.shared.ai_services.ai_processor_robust import RobustFinancialAnalyzer", "names": ["RobustFinancialAnalyzer"]}, {"file": "src/shared/ai_services/ai_chat_processor.py", "line": 36, "statement": "from src.shared.ai_services.ai_processor_robust import RobustFinancialAnalyzer", "names": ["RobustFinancialAnalyzer"]}, {"file": "scripts/test/test_ai_improvements.py", "line": 18, "statement": "from src.shared.ai_services.ai_processor_robust import CleanAIProcessor", "names": ["CleanAIProcessor"]}, {"file": "scripts/test/test_data_issue.py", "line": 17, "statement": "from src.shared.ai_services.ai_processor_robust import CleanAIProcessor", "names": ["CleanAIProcessor"]}, {"file": "scripts/test/test_query_result_fix.py", "line": 10, "statement": "from src.shared.ai_services.ai_processor_robust import QueryResult", "names": ["QueryResult"]}, {"file": "scripts/test/test_robust_processor.py", "line": 17, "statement": "from src.shared.ai_services.ai_processor_robust import CleanAIProcessor", "names": ["CleanAIProcessor"]}], "src.shared.ai_services.enhanced_intent_detector": [{"file": "src/bot/pipeline/commands/ask/stages/query_analyzer.py", "line": 253, "statement": "from src.shared.ai_services.enhanced_intent_detector import enhanced_intent_detector", "names": ["enhanced_intent_detector"]}, {"file": "src/bot/pipeline/commands/ask/stages/depth_style_analyzer.py", "line": 588, "statement": "from src.shared.ai_services.enhanced_intent_detector import enhanced_intent_detector", "names": ["enhanced_intent_detector"]}, {"file": "src/core/prompts/prompt_manager.py", "line": 105, "statement": "from src.shared.ai_services.enhanced_intent_detector import enhanced_intent_detector", "names": ["enhanced_intent_detector"]}], "ai_chat": [{"file": "src/bot/pipeline/commands/ask/stages/ai_controlled_analysis.py", "line": 218, "statement": "from ..ai_chat import AIChatProcessorWrapper", "names": ["AIChatProcessorWrapper"]}, {"file": "src/bot/pipeline/commands/ask/stages/enhanced_ai_analysis.py", "line": 622, "statement": "from ..ai_chat import AIChatProcessorWrapper", "names": ["AIChatProcessorWrapper"]}], "src.shared.ai_services.cross_validation_ai": [{"file": "src/bot/pipeline/commands/ask/stages/enhanced_validation.py", "line": 14, "statement": "from src.shared.ai_services.cross_validation_ai import CrossValidationAI, ValidationImportance", "names": ["CrossValidationAI", "ValidationImportance"]}, {"file": "src/bot/pipeline/commands/ask/stages/enhanced_validation.py", "line": 216, "statement": "from src.shared.ai_services.cross_validation_ai import CrossValidationResult, ConsensusLevel", "names": ["CrossValidationResult", "ConsensusLevel"]}], "src.shared.ai_services.ai_client": [{"file": "src/bot/pipeline/commands/ask/stages/ai_symbol_extractor.py", "line": 29, "statement": "from src.shared.ai_services.ai_client import AIClientWrapper", "names": ["AIClientWrapper"]}, {"file": "src/shared/utils/symbol_extraction.py", "line": 100, "statement": "from src.shared.ai_services.ai_client import AIClientWrapper", "names": ["AIClientWrapper"]}], "src.shared.ai_services.tool_registry": [{"file": "src/shared/ai_chat/data_fetcher.py", "line": 15, "statement": "from src.shared.ai_services.tool_registry import tool_registry", "names": ["tool_registry"]}], "src.shared.ai_services.openrouter_key": [{"file": "src/shared/ai_chat/ai_client.py", "line": 16, "statement": "from src.shared.ai_services.openrouter_key import OPENROUTER_API_KEY", "names": ["OPENROUTER_API_KEY"]}], "src.shared.ai_chat.ai_client": [{"file": "src/shared/ai_chat/processor.py", "line": 7, "statement": "from src.shared.ai_chat.ai_client import AIClientWrapper", "names": ["AIClientWrapper"]}, {"file": "src/shared/ai_services/enhanced_symbol_extractor.py", "line": 17, "statement": "from src.shared.ai_chat.ai_client import AIClientWrapper", "names": ["AIClientWrapper"]}, {"file": "src/shared/ai_services/intelligent_text_parser.py", "line": 16, "statement": "from src.shared.ai_chat.ai_client import AIClientWrapper", "names": ["AIClientWrapper"]}, {"file": "src/shared/ai_services/enhanced_intent_detector.py", "line": 17, "statement": "from src.shared.ai_chat.ai_client import AIClientWrapper", "names": ["AIClientWrapper"]}, {"file": "src/shared/ai_services/ai_security_detector.py", "line": 15, "statement": "from src.shared.ai_chat.ai_client import AIClientWrapper", "names": ["AIClientWrapper"]}], "src.shared.ai_services.simple_query_analyzer": [{"file": "src/shared/ai_services/fast_price_lookup.py", "line": 13, "statement": "from src.shared.ai_services.simple_query_analyzer import SymbolInfo", "names": ["SymbolInfo"]}], "src.shared.ai_services.query_router": [{"file": "src/shared/ai_services/intelligent_chatbot.py", "line": 22, "statement": "from src.shared.ai_services.query_router import QueryRouter, QueryContext", "names": ["Query<PERSON><PERSON>er", "QueryContext"]}], "src.shared.ai_services.response_synthesizer": [{"file": "src/shared/ai_services/intelligent_chatbot.py", "line": 23, "statement": "from src.shared.ai_services.response_synthesizer import ResponseSynthesizer", "names": ["ResponseSynthesizer"]}], "src.shared.ai_services.fast_price_lookup": [{"file": "src/shared/ai_services/ai_service_wrapper.py", "line": 19, "statement": "from src.shared.ai_services.fast_price_lookup import fast_price_lookup", "names": ["fast_price_lookup"]}], "src.shared.ai_chat.response_formatter": [{"file": "scripts/test/test_data_binding_fix.py", "line": 10, "statement": "from src.shared.ai_chat.response_formatter import ResponseFormatter", "names": ["Response<PERSON>ormatter"]}]}}