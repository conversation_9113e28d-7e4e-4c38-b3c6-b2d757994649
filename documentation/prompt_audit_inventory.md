# Prompt Audit and Inventory

## Current Prompt Locations and Issues

### 1. System Prompts (DUPLICATED)

#### Location 1: `src/shared/ai_chat/ai_client.py`
- **Function**: `get_system_prompt()`
- **Type**: Basic system prompt with date injection
- **Issues**: Simple, lacks comprehensive trading context
- **Usage**: Used by AI client for general queries

#### Location 2: `src/bot/pipeline/commands/ask/stages/prompts.py`
- **Function**: `get_system_prompt()`
- **Type**: Comprehensive trading assistant prompt with anti-fabrication rules
- **Issues**: Duplicates functionality, more comprehensive than ai_client version
- **Usage**: Used by ask command pipeline

#### Location 3: `src/core/prompts/templates/system_prompt.txt`
- **Type**: Static text file with trading analysis prompt
- **Issues**: Not used consistently, static without date injection
- **Usage**: Loaded by PromptManager but not widely used

### 2. Command-Specific Prompts

#### Ask Command Prompts (`src/bot/pipeline/commands/ask/stages/prompts.py`)
- **Functions**: 
  - `get_system_prompt()` - Main system prompt
  - `get_fallback_responses()` - Fallback response templates
  - `get_model_config_for_intent()` - Model configuration per intent
- **Constants**:
  - `INTENT_CONFIGURATION` - Intent-specific configurations
  - `COMPLIANCE_TEMPLATES` - Risk disclaimer templates
  - `QUALITY_STANDARDS` - Quality requirements
  - `MONITORING_CONFIG` - Monitoring configuration
  - `JSON_SCHEMA_DEFINITION` - Response schema

### 3. AI Service Prompts

#### Enhanced Intent Detector (`src/shared/ai_services/enhanced_intent_detector.py`)
- **Attribute**: `ai_prompt_template`
- **Type**: Intent classification prompt
- **Issues**: Hardcoded in class, not centralized

#### Intelligent Text Parser (`src/shared/ai_services/intelligent_text_parser.py`)
- **Attribute**: `ai_prompts` dictionary
- **Types**: 
  - `SYMBOL_EXTRACTION` - Extract stock symbols
  - `PRICE_EXTRACTION` - Extract price values
- **Issues**: Hardcoded prompts in class initialization

#### AI Security Detector (`src/shared/ai_services/ai_security_detector.py`)
- **Attribute**: `security_analysis_prompt`
- **Type**: Security threat detection prompt
- **Issues**: Hardcoded in class, not centralized

#### Anti-Hallucination Prompt (`src/shared/ai_services/anti_hallucination_prompt.py`)
- **Function**: `get_anti_hallucination_prompt()`
- **Type**: Anti-fabrication enforcement prompt
- **Issues**: Separate module, not integrated with main system

### 4. Core Prompt System (PARTIALLY IMPLEMENTED)

#### Prompt Manager (`src/core/prompts/prompt_manager.py`)
- **Class**: `PromptManager`
- **Features**:
  - System prompt loading from file
  - Intent pattern building
  - Tool mappings
  - Compliance templates
- **Issues**: Not consistently used across codebase

#### Models (`src/core/prompts/models.py`)
- **Types**: Intent types, tool types, classification models
- **Issues**: Good foundation but underutilized

### 5. Test and Development Prompts

#### Test Files
- `test_upgraded_prompts.py` - Tests ask command prompts
- Various test files with hardcoded prompts for testing

## Issues Identified

### 1. **Duplication**
- Multiple `get_system_prompt()` functions with different implementations
- Overlapping functionality between ai_client and ask command prompts

### 2. **Inconsistency**
- Different prompt styles and formats across services
- Inconsistent date injection and anti-fabrication rules
- Varying compliance and disclaimer approaches

### 3. **Maintenance Burden**
- Prompts scattered across multiple files
- No single source of truth for AI personality/behavior
- Difficult to update prompts consistently

### 4. **Integration Issues**
- Core prompt system exists but isn't used by most services
- Import dependencies create circular references
- No clear hierarchy or inheritance

## Recommended Centralization Structure

```
src/core/prompts/
├── __init__.py                 # Main exports and global instance
├── models.py                   # Existing data models
├── prompt_manager.py           # Enhanced central manager
├── personas.py                 # AI personality definitions
├── system_prompts.py           # Core system prompts
├── command_prompts.py          # Command-specific prompts
├── ai_service_prompts.py       # AI service prompts
├── compliance.py               # Risk disclaimers and compliance
├── templates/                  # Template files
│   ├── system_prompt.txt       # Enhanced system prompt
│   ├── personas/               # Persona template files
│   └── compliance/             # Compliance template files
└── utils.py                    # Prompt utilities and helpers
```

## Migration Priority

1. **High Priority**: System prompts (eliminate duplication)
2. **Medium Priority**: Command prompts (ask, analyze)
3. **Medium Priority**: AI service prompts (intent, security, parsing)
4. **Low Priority**: Test and development prompts
