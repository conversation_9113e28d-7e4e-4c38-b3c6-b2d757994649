# TradingView Automation Project

This document provides a comprehensive overview of the TradingView Automation project, including its architecture, components, and AI-powered features.

## 1. Webhook Ingestion (`tradingview-ingest`)

The `tradingview-ingest` service is responsible for receiving, validating, and processing webhooks from TradingView. It is a standalone FastAPI application designed to handle a high volume of incoming webhooks efficiently and securely.

### Workflow

1.  **Webhook Reception:** An HTTP POST request is received by `webhook_receiver.py`.
2.  **Security:** The request is validated by the `TradingViewSecurityMiddleware` in `security.py`.
3.  **Signature Validation:** The webhook signature is validated using HMAC-SHA256.
4.  **Queueing:** The validated webhook payload is placed in a Redis queue.
5.  **Supabase (Initial Save):** The raw webhook data is saved to the `webhooks` table in Supabase.
6.  **Processing:** The `webhook_processor.py` dequeues the webhook.
7.  **Parsing:** The webhook data is parsed by `DataParser` or `PineScriptAlertParser`.
8.  **Storage (Local):** The `StorageManager` stores the data in Redis.
9.  **Storage (Supabase):** The `webhook_processor` calls the `SupabaseClient` to update the webhook status and save the parsed ticker data.
10. **Alerting:** The `AlertEngine` sends alerts to Discord and/or a generic webhook.
11. **AI Analysis (Periodic):** The `AITradingAnalyzer` runs periodically, fetches data from the database, and sends analysis reports to Discord via the `DiscordNotifier`.

### Key Files

*   `tradingview-ingest/src/webhook_receiver.py`: The main FastAPI application that receives webhooks.
*   `tradingview-ingest/src/security.py`: The security middleware for validating incoming requests.
*   `tradingview-ingest/src/webhook_processor.py`: The worker that processes webhooks from the Redis queue.
*   `tradingview-ingest/src/data_parser.py`: Parses structured JSON webhook data.
*   `tradingview-ingest/src/text_parser.py`: Parses raw text alerts from Pine Script.
*   `tradingview-ingest/src/storage_manager.py`: Manages the storage of webhook data in Redis and PostgreSQL.
*   `tradingview-ingest/src/alert_engine.py`: Sends alerts to Discord and other webhook endpoints.
*   `tradingview-ingest/src/automated_analyzer.py`: Performs periodic AI analysis of recent data.
*   `tradingview-ingest/src/supabase_client.py`: A client for interacting with the Supabase API.

## 2. Discord Bot (`src/bot`)

The Discord bot is the primary user interface for interacting with the trading automation system. It allows users to ask questions, manage their watchlists, and receive alerts and analysis reports.

### Workflow

1.  **Command Reception:** A user on Discord interacts with the bot using a slash command, for example, `/ask $AAPL`.
2.  **Bot Client:** The `TradingBot` class in `client.py` receives the command. It performs rate limiting to prevent abuse.
3.  **Pipeline Execution:** The bot calls the `execute_ask_pipeline` function to initiate the AI analysis.
4.  **Response to User:** The generated response is passed back up the call stack to the `TradingBot`.
5.  The bot then sends the formatted response back to the user on Discord.

### Key Files

*   `src/bot/client.py`: The main entry point for the Discord bot. It initializes the bot, sets up event listeners, and registers the slash commands.
*   `src/bot/commands/`: This directory contains the implementation of the different slash commands.
*   `src/bot/permissions.py`: Implements the permission system for the bot, with different access levels for public, paid, and admin users.
*   `src/bot/watchlist_manager.py`: Manages user watchlists.
*   `src/bot/database_manager.py`: Manages the connection to the database.

## 3. AI Pipeline (`src/bot/pipeline`)

The AI pipeline is the core of the bot's intelligence. It's responsible for processing user queries, performing technical analysis, and generating insightful responses.

### Workflow

1.  **Pipeline Execution:** The `execute_ask_pipeline` function in `pipeline.py` is the main entry point for the AI pipeline.
2.  **Context Creation:** It creates a `PipelineContext` object to track the request and pass data between stages.
3.  **AI Chat Processor:** The `AIChatProcessor` in `ai_service_wrapper.py` is the main processing stage. It performs the following steps:
    *   Extracts symbols from the user's query.
    *   Fetches comprehensive technical analysis data for the symbols.
    *   Generates trading signals.
    *   Generates a conversational AI response that interprets the data.
4.  **Response Return:** The response is returned to the `TradingBot`, which then sends it to the user on Discord.

### Key Files

*   `src/bot/pipeline/commands/ask/pipeline.py`: Orchestrates the AI pipeline for the `/ask` command.
*   `src/bot/pipeline/commands/ask/stages/ai_service_wrapper.py`: The core of the AI processing logic.
*   `src/bot/pipeline/core/pipeline_engine.py`: The generic pipeline engine that the `/ask` pipeline is built on.
*   `src/shared/technical_analysis/`: This directory contains the modules for calculating technical indicators, detecting supply/demand zones, and other analysis tasks.

## 4. Data and Database (`src/data` and `src/database`)

This section details the data models, data providers, and database interactions used in the `tradingview-automation` project.

### Data Models

The project uses dataclasses to define the structure of the data that is passed between different components.

*   **`MarketData`**: Represents market data from TradingView.
*   **`TechnicalIndicator`**: Represents a technical indicator.
*   **`TradingSignal`**: Represents a trading signal.
*   **`ParsedAlert`**: Represents a parsed alert from a raw text message.

### Data Providers

The project uses multiple data providers for a comprehensive market view:

*   **Polygon**
*   **Yahoo Finance**
*   **Finnhub**
*   **Alpha Vantage**

### Database

The project uses a PostgreSQL database for persistent storage and Redis for caching and queuing.

*   `src/database/connection.py`: Manages the PostgreSQL database connection.
*   `src/database/query_wrapper.py`: Provides wrapper functions for database queries.
*   `src/database/models/`: Contains SQLAlchemy models for database tables.
*   `init-db.sql`: SQL script for initializing the database schema.

## 5. Core Components (`src/core`)

This section details the core components of the `tradingview-automation` project, which provide essential services like configuration management, secrets management, and logging.

### Key Files

*   `src/core/config_manager.py`: Manages the application's configuration.
*   `src/core/secrets.py`: Manages secrets and other sensitive information.
*   `src/core/logger.py`: Provides a centralized logging facility.
*   `src/core/exceptions.py`: Defines custom exception classes for the application.

## AI Enhancements

This section outlines the AI-powered enhancements that have been implemented and are planned for the project.

### AI Enhancement Implementation Guide

#### 🎯 **IMPLEMENTATION COMPLETE - READY FOR DEPLOYMENT**

##### **✅ COMPLETED ENHANCEMENTS**

*   **Enhanced TradingView Alert Parser** ✅
*   **Symbol Extraction AI Enhancement** ✅
*   **Advanced Input Validation** ✅

##### **📊 PERFORMANCE RESULTS**

*   **AI Parser Success Rate**: 100% (8/8 tests)
*   **Traditional Parser Success Rate**: 12.5% (1/8 tests)

#### 🚀 **DEPLOYMENT INSTRUCTIONS**

1.  **Update TradingView Webhook Processing**: The enhanced parser is integrated and ready to use.
2.  **Test the Enhanced Parser**:
    ```bash
    python3 tests/test_enhanced_parser.py
    python3 tests/test_ai_alert_parser.py
    ```
3.  **Monitor Performance**: The enhanced parser includes logging for monitoring success/failure rates, fallback usage, and performance metrics.

### AI Enhancement Recommendations - Regex to AI Migration

#### 🚀 **NEXT PRIORITY RECOMMENDATIONS**

*   **TradingView Alert AI Parser (HIGH IMPACT)**: Enable natural language alerts, flexible formats, and context understanding.
*   **Intent Classification Enhancement (HIGH IMPACT)**: Use AI for natural language understanding of user intent.
*   **Configuration Intelligence (MEDIUM IMPACT)**: Implement smart configuration with auto-correction and environment detection.

#### 📊 **IMPLEMENTATION ROADMAP**

*   **Phase 1: Alert Processing Intelligence (IMMEDIATE)**: Deploy AI Alert Parser and enhance intent classification.
*   **Phase 2: Advanced Intelligence (NEXT MONTH)**: Implement smart configuration management and advanced security enhancements.
*   **Phase 3: System-Wide AI Integration (FUTURE)**: Introduce AI-powered error analysis and data format intelligence.

#### 📈 **EXPECTED BENEFITS**

*   **Accuracy Improvements**: Significant improvements in symbol detection, alert parsing, intent classification, and security detection.
*   **User Experience**: Natural language support, error tolerance, context awareness, and flexibility.
*   **Maintenance Benefits**: Reduced complexity, adaptive learning, better error handling, and lower maintenance.