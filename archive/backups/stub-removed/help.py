"""
Help command extension - Interactive help and documentation.
"""

import discord
from discord.ext import commands
from discord import app_commands
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

class HelpCommand(commands.Cog):
    """Help command for bot documentation."""
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
    
    @app_commands.command(name="help", description="Show available commands and usage")
    async def help_command(self, interaction: discord.Interaction):
        """Show available commands and usage"""
        try:
            embed = discord.Embed(
                title="🤖 Trading Bot Help",
                description="AI-powered trading assistant with professional-grade infrastructure",
                color=discord.Color.blue(),
                timestamp=discord.utils.utcnow()
            )
            
            # Core commands
            embed.add_field(
                name="📊 Analysis Commands",
                value="• `/analyze [symbol]` - Comprehensive technical analysis\n"
                      "• `/zones <symbol>` - Support & resistance zones\n"
                      "• `/recommendations <symbol>` - AI trading insights",
                inline=False
            )
            
            # Utility commands
            embed.add_field(
                name="🛠️ Utility Commands",
                value="• `/ask <query>` - Freeform AI assistance\n"
                      "• `/watchlist` - Manage your watchlist\n"
                      "• `/portfolio` - Portfolio management\n"
                      "• `/status` - Bot performance metrics",
                inline=False
            )
            
            # Features
            embed.add_field(
                name="🚀 Key Features",
                value="• **100% Data Quality** - No false gaps\n"
                      "• **AI-Powered** - Technical + sentiment analysis\n"
                      "• **Real-time Data** - Live market information\n"
                      "• **Professional Grade** - Production-ready infrastructure",
                inline=False
            )
            
            embed.set_footer(text="Built on production-ready infrastructure")
            await interaction.response.send_message(embed=embed)
            
        except Exception as e:
            logger.error(f"Error in help command: {e}", exc_info=True)
            await interaction.response.send_message(
                "❌ An error occurred while showing help.",
                ephemeral=True
            )

async def setup(bot: commands.Bot):
    """Setup the help command extension."""
    await bot.add_cog(HelpCommand(bot))
    logger.info("✅ Help command extension loaded")
