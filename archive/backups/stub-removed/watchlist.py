"""
Watchlist command extension - Personal watchlist management.
"""

import discord
from discord.ext import commands
from discord import app_commands
from src.shared.error_handling.logging import get_logger

logger = get_logger(__name__)

class WatchlistCommand(commands.Cog):
    """Watchlist command for managing personal watchlists."""
    
    def __init__(self, bot: commands.Bot):
        self.bot = bot
    
    @app_commands.command(name="watchlist", description="Manage your personal watchlist")
    @app_commands.describe(
        action="Action to perform",
        symbol="Stock symbol (for add/remove actions)"
    )
    @app_commands.choices(action=[
        app_commands.Choice(name="Show watchlist", value="show"),
        app_commands.Choice(name="Add symbol", value="add"),
        app_commands.Choice(name="Remove symbol", value="remove")
    ])
    async def watchlist_command(self, interaction: discord.Interaction, action: str, symbol: str = None):
        """Manage your personal watchlist"""
        try:
            await interaction.response.defer(thinking=True)
            
            # TODO: Implement watchlist command logic
            # This should use the existing watchlist_enhanced.py functionality
            
            await interaction.followup.send(f"📋 Managing watchlist: {action} {symbol or ''}... (Coming soon)")
            
        except Exception as e:
            logger.error(f"Error in watchlist command: {e}", exc_info=True)
            await interaction.response.send_message(
                "❌ An error occurred while managing your watchlist.",
                ephemeral=True
            )

async def setup(bot: commands.Bot):
    """Setup the watchlist command extension."""
    await bot.add_cog(WatchlistCommand(bot))
    logger.info("✅ Watchlist command extension loaded")
