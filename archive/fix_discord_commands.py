#!/usr/bin/env python3
"""
Fix Discord command synchronization
"""

import asyncio
import os
import sys
sys.path.insert(0, 'src')

async def fix_discord_commands():
    """Fix Discord command synchronization"""
    print("🔧 Fixing Discord command synchronization...")
    
    try:
        from bot.core.bot import create_bot
        
        # Load environment
        from dotenv import load_dotenv
        load_dotenv()
        
        # Create bot instance
        bot = create_bot()
        print("✅ Bot created successfully")
        
        # Load extensions
        await bot._load_extensions()
        print("✅ Extensions loaded")
        
        # Check if we have a Discord token
        token = os.getenv('DISCORD_BOT_TOKEN')
        if not token:
            print("❌ DISCORD_BOT_TOKEN not found in environment")
            return False
            
        print(f"✅ Discord token found (length: {len(token)})")
        
        # Start the bot (this will connect to Discord)
        print("🔄 Connecting to Discord...")
        
        # Create a task to run the bot
        bot_task = asyncio.create_task(bot.start_bot())
        
        # Wait a bit for the bot to connect
        await asyncio.sleep(10)
        
        # Check if bot is ready
        if bot.bot.is_ready():
            print("✅ Bot connected to Discord!")
            
            # Sync commands
            print("🔄 Syncing commands with Discord...")
            synced = await bot.bot.tree.sync()
            print(f"✅ Synced {len(synced)} commands with Discord")
            
            # List synced commands
            for cmd in synced:
                print(f"   - {cmd.name}: {cmd.description}")
            
            # Check if ask command was synced
            ask_synced = any(cmd.name == 'ask' for cmd in synced)
            if ask_synced:
                print("✅ /ask command successfully synced with Discord!")
                print("🎉 The /ask command should now be available in Discord!")
            else:
                print("❌ /ask command was NOT synced with Discord")
                
            # Stop the bot
            await bot.close()
            return ask_synced
        else:
            print("❌ Bot failed to connect to Discord")
            await bot.close()
            return False
            
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(fix_discord_commands())
    if success:
        print("\n🎉 Discord command fix completed successfully!")
        print("The /ask command should now be available in Discord.")
    else:
        print("\n❌ Discord command fix failed.")
