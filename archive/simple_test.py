#!/usr/bin/env python3

import sys
import os
import time

# Add project root to path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_lazy_import():
    print("Testing lazy import functionality...")
    
    # Test lazy import registration
    from src.shared.utils.lazy_import import lazy_import, get_lazy_module
    
    # Register a module
    lazy_import("json", "json")
    
    # Get the module
    json_module = get_lazy_module("json")
    
    # Test that it works
    data = {"test": "value"}
    serialized = json_module.dumps(data)
    print(f"Lazy import test successful: {serialized}")
    
    print("Lazy import test completed.")

if __name__ == "__main__":
    test_lazy_import()