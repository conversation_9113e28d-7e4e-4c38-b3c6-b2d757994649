#!/usr/bin/env python3
"""
Simple chat interface for testing the AI components
"""

import os
import sys
import asyncio
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def chat_loop():
    """Simple chat loop for testing"""
    print("Simple AI Chat Interface")
    print("Type 'quit' to exit")
    print("-" * 30)
    
    # Import AI components
    try:
        from src.shared.ai_services.unified_ai_processor import create_unified_processor
        ai_processor = create_unified_processor()
        print("✅ AI processor initialized")
    except Exception as e:
        print(f"❌ Failed to initialize AI processor: {e}")
        return
    
    # Simple conversation history
    conversation_history = []
    
    while True:
        try:
            # Get user input
            user_input = input("\nYou: ").strip()
            
            # Check for quit command
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("Goodbye!")
                break
            
            # Skip empty input
            if not user_input:
                continue
            
            # Add user message to history
            conversation_history.append({"role": "user", "content": user_input})
            
            # Process with AI
            print("Thinking...")
            
            # Create a simple prompt
            prompt = f"""
You are a helpful trading and financial assistant. 
The user has asked: {user_input}
Please provide a concise and helpful response.
"""
            
            # Process with AI
            response = await ai_processor.process_request(
                prompt=prompt,
                model="moonshotai/kimi-k2-0905",  # Using the default model from config
                temperature=0.7,
                max_tokens=1000
            )
            
            # Extract response text
            if isinstance(response, dict):
                ai_response = response.get('response', str(response))
            else:
                ai_response = str(response)
            
            # Print AI response
            print(f"AI: {ai_response}")
            
            # Add AI response to history
            conversation_history.append({"role": "assistant", "content": ai_response})
            
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    # Change to project directory
    os.chdir(project_root)
    
    # Run chat loop
    asyncio.run(chat_loop())
