#!/usr/bin/env python3
"""
Demo: Dynamic MCP Tool Usage
Shows how AI can dynamically choose which MCP tools to use based on queries.
"""

import asyncio
import logging
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def demo_tool_registry():
    """Demonstrate the AI tool registry."""
    print("🔧 AI Tool Registry Demo")
    print("=" * 50)
    
    try:
        from src.shared.ai_services.ai_tool_registry import AIToolRegistry, MCPToolManager
        from src.shared.data_providers.alpha_vantage_mcp import AlphaVantageMCPClient
        
        # Initialize MCP client and tool manager
        mcp_client = AlphaVantageMCPClient()
        tool_manager = MCPToolManager(mcp_client)
        registry = tool_manager.get_tool_registry()
        
        print(f"✅ Tool registry initialized")
        print(f"🔧 Total tools available: {len(registry.tools)}")
        print(f"🤖 MCP tools: {len(registry.mcp_tools)}")
        
        # Show available tools
        print(f"\n📋 Available MCP Tools:")
        for tool_name, tool in registry.mcp_tools.items():
            print(f"  • {tool_name}: {tool.description}")
        
        # Show tool schema
        schema = registry.get_tool_schema()
        print(f"\n📊 Tool Schema:")
        print(f"  • Total tools: {schema['total_tools']}")
        print(f"  • Categories: {list(schema['categories'].keys())}")
        
        await tool_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ Tool registry demo failed: {e}")
        return False

async def demo_ai_tool_selection():
    """Demonstrate how AI would select tools for different queries."""
    print("\n🤖 AI Tool Selection Demo")
    print("=" * 50)
    
    # Simulate AI tool selection logic
    queries_and_tools = [
        {
            "query": "What's the current price of AAPL?",
            "selected_tools": ["get_global_quote"],
            "reasoning": "Simple price query → Use real-time quote tool"
        },
        {
            "query": "Give me technical analysis for MSFT including RSI and MACD",
            "selected_tools": ["get_global_quote", "get_rsi", "get_macd"],
            "reasoning": "Technical analysis → Use quote + technical indicator tools"
        },
        {
            "query": "What's the news sentiment for NVDA?",
            "selected_tools": ["get_news_sentiment"],
            "reasoning": "News sentiment query → Use news sentiment tool"
        },
        {
            "query": "Analyze TSLA comprehensively - price, technical indicators, news, and fundamentals",
            "selected_tools": ["get_comprehensive_analysis"],
            "reasoning": "Comprehensive analysis → Use all-in-one comprehensive tool"
        },
        {
            "query": "Compare AAPL and MSFT performance",
            "selected_tools": ["get_global_quote", "get_time_series_daily"],
            "reasoning": "Comparison query → Use quote + historical data tools"
        },
        {
            "query": "What are the Bollinger Bands for GOOGL?",
            "selected_tools": ["get_bbands"],
            "reasoning": "Specific technical indicator → Use Bollinger Bands tool"
        }
    ]
    
    for i, case in enumerate(queries_and_tools, 1):
        print(f"\n🔍 Case {i}: {case['query']}")
        print(f"🤖 AI Reasoning: {case['reasoning']}")
        print(f"🔧 Selected Tools: {', '.join(case['selected_tools'])}")
        
        # Simulate tool execution
        print(f"⚡ Executing tools...")
        for tool in case['selected_tools']:
            print(f"  ✅ {tool} - Would fetch real-time data")
        
        print(f"💬 AI Response: Generated comprehensive analysis using {len(case['selected_tools'])} data sources")
    
    return True

async def demo_dynamic_pipeline():
    """Demonstrate the dynamic MCP pipeline concept."""
    print("\n🚀 Dynamic MCP Pipeline Demo")
    print("=" * 50)
    
    print("🎯 Key Features:")
    print("  • AI decides which tools to use based on query context")
    print("  • MCP tools are available whenever AI deems necessary")
    print("  • No hardcoded pipeline stages - fully dynamic")
    print("  • Real-time data access through Alpha Vantage MCP")
    print("  • Intelligent fallback to existing providers")
    
    print("\n🔄 How it works:")
    print("  1. User asks a question")
    print("  2. AI analyzes the query intent")
    print("  3. AI selects appropriate MCP tools")
    print("  4. AI calls tools to gather real-time data")
    print("  5. AI generates response using the data")
    print("  6. AI provides source attribution")
    
    print("\n📊 Benefits:")
    print("  • More accurate responses with real-time data")
    print("  • Reduced hallucination through fact verification")
    print("  • Flexible tool usage based on context")
    print("  • Better user experience with relevant data")
    print("  • Transparent data sources")
    
    return True

async def demo_implementation_status():
    """Show implementation status."""
    print("\n📋 Implementation Status")
    print("=" * 50)
    
    components = [
        ("AI Tool Registry", "✅ Complete", "Dynamic tool discovery and management"),
        ("MCP Tool Manager", "✅ Complete", "Alpha Vantage MCP integration"),
        ("Enhanced AI Client", "✅ Complete", "AI with dynamic tool calling"),
        ("Dynamic MCP Pipeline", "✅ Complete", "AI-driven tool selection"),
        ("Discord Integration", "✅ Complete", "Dynamic ask command"),
        ("Tool Execution", "⚠️ Needs API Keys", "Requires Alpha Vantage API key"),
        ("AI Model Integration", "⚠️ Needs API Keys", "Requires OpenAI API key"),
        ("Fallback System", "✅ Complete", "Graceful degradation when MCP unavailable")
    ]
    
    for component, status, description in components:
        print(f"  {status} {component:20} - {description}")
    
    print(f"\n🎯 Next Steps:")
    print(f"  1. Add Alpha Vantage API key for MCP access")
    print(f"  2. Add OpenAI API key for AI model access")
    print(f"  3. Test with real trading queries")
    print(f"  4. Deploy to Discord bot")
    
    return True

async def main():
    """Run the dynamic MCP demo."""
    print("🚀 Dynamic MCP Tool Usage Demo")
    print("=" * 60)
    print("This demo shows how AI can dynamically choose which MCP tools to use")
    print("based on the context of user queries, rather than hardcoded pipeline stages.")
    print("=" * 60)
    
    # Run demos
    demos = [
        ("Tool Registry", demo_tool_registry),
        ("AI Tool Selection", demo_ai_tool_selection),
        ("Dynamic Pipeline", demo_dynamic_pipeline),
        ("Implementation Status", demo_implementation_status)
    ]
    
    results = {}
    
    for demo_name, demo_func in demos:
        print(f"\n🧪 Running {demo_name} Demo...")
        try:
            success = await demo_func()
            results[demo_name] = success
            print(f"✅ {demo_name} Demo: {'COMPLETED' if success else 'FAILED'}")
        except Exception as e:
            print(f"❌ {demo_name} Demo: FAILED - {e}")
            results[demo_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DEMO SUMMARY")
    print("=" * 60)
    
    completed = sum(results.values())
    total = len(results)
    
    for demo_name, success in results.items():
        status = "✅ COMPLETED" if success else "❌ FAILED"
        print(f"{demo_name:20} {status}")
    
    print(f"\nOverall: {completed}/{total} demos completed ({completed/total*100:.1f}%)")
    
    if completed == total:
        print("\n🎉 All demos completed successfully!")
        print("🤖 The AI can now dynamically choose which MCP tools to use!")
        print("🔧 MCP tools are available whenever the AI deems necessary!")
    else:
        print("\n⚠️ Some demos failed. Check the logs above for details.")
    
    print("\n🚀 Dynamic MCP Demo Complete!")

if __name__ == "__main__":
    asyncio.run(main())
