#!/usr/bin/env python3
"""
Generate a consolidation map for folder layout cleanup.
- Scans for duplicate directory types (tests, config, logs, data, utils, security)
- Finds duplicate/suspect files (ngrok.yml, webhook.conf, tasks.md)
- Lists root-level markdown/docs clutter
- Produces recommendations and dry-run move commands

Outputs: .kiro/consolidation_map.md
"""
import os
import sys
from pathlib import Path
from collections import defaultdict, Counter
from typing import Dict, List, Tuple

ROOT = Path(__file__).resolve().parents[1]
TARGET_REPORT = ROOT / ".kiro" / "consolidation_map.md"

DIR_CATEGORIES = {
    "tests": {"names": {"tests", "testing", "test_results"}},
    "config": {"names": {"config", "configs", "configuration"}},
    "logs": {"names": {"logs", "log"}},
    "data": {"names": {"data"}},
    "utils": {"names": {"utils", "utilities"}},
    "security": {"names": {"security", "sec"}},
}

DUPLICATE_FILENAMES = {
    "ngrok.yml", "webhook.conf", "tasks.md", "TASKS.md", "ngrok.yaml"
}

CANONICAL = {
    "tests": "tests/",
    "config": "config/",
    "logs": "logs/",
    "data": "data/",
    "utils": "src/shared/utils/",
    "security": "src/security/",
    "docs_root": "documentation/",
}

SKIP_DIRS = {".git", ".venv", "venv", "node_modules", "__pycache__", ".mypy_cache", ".pytest_cache", "build", "dist"}


def iter_repo():
    for dirpath, dirnames, filenames in os.walk(ROOT):
        p = Path(dirpath)
        # Skip entire subtree if path contains a skipped part or site-packages
        if any(part in SKIP_DIRS or part == 'site-packages' for part in p.parts):
            continue
        # Prune skip dirs on this level as well
        dirnames[:] = [d for d in dirnames if d not in SKIP_DIRS and d != 'site-packages']
        yield Path(dirpath), dirnames, filenames


def scan_directories():
    occurrences: Dict[str, List[Path]] = {k: [] for k in DIR_CATEGORIES}
    for dpath, dirnames, _ in iter_repo():
        for d in dirnames:
            for cat, spec in DIR_CATEGORIES.items():
                if d in spec["names"]:
                    occurrences[cat].append((dpath / d).resolve())
    return occurrences


def scan_duplicate_files():
    found: Dict[str, List[Path]] = defaultdict(list)
    for dpath, _, filenames in iter_repo():
        for f in filenames:
            if f in DUPLICATE_FILENAMES:
                found[f].append((Path(dpath) / f).resolve())
    return found


def scan_root_docs():
    root_docs = []
    for f in ROOT.iterdir():
        if f.is_file() and f.suffix.lower() in {".md", ".rst"}:
            root_docs.append(f)
    return sorted(root_docs)


def count_files_in_dir(path: Path) -> int:
    total = 0
    for dpath, _, filenames in os.walk(path):
        total += len(filenames)
    return total


def propose_moves_for_category(cat: str, paths: List[Path]) -> List[str]:
    target = CANONICAL.get(cat)
    if not target:
        return []
    cmds = []
    for p in paths:
        # Skip the canonical target if it already matches
        if str(p).rstrip("/").endswith(str(target).rstrip("/")):
            continue
        # Use rsync dry-run style command
        rel = p.relative_to(ROOT)
        cmds.append(f"# {cat}: {rel}")
        cmds.append(f"mkdir -p {target}")
        cmds.append(f"rsync -a --dry-run --info=NAME {rel}/ {target}")
    return cmds


def build_report():
    occ = scan_directories()
    dup_files = scan_duplicate_files()
    root_docs = scan_root_docs()

    lines: List[str] = []
    lines.append("# Consolidation Map (Dry-Run Plan)\n")
    lines.append(f"Root: {ROOT}\n")

    lines.append("## Summary\n")
    for cat, paths in occ.items():
        lines.append(f"- {cat}: {len(paths)} locations")
    lines.append(f"- duplicate filenames tracked: {len(dup_files)} kinds\n")

    lines.append("## Canonical Targets\n")
    for k, v in CANONICAL.items():
        lines.append(f"- {k}: `{v}`")
    lines.append("")

    for cat, paths in occ.items():
        lines.append(f"## {cat.title()} Directories ({len(paths)})\n")
        for p in sorted(paths):
            count = count_files_in_dir(p)
            lines.append(f"- {p.relative_to(ROOT)} ({count} files)")
        lines.append("")

        # Proposed dry-run commands
        cmds = propose_moves_for_category(cat, [p for p in paths])
        if cmds:
            lines.append("### Proposed Dry-Run Commands\n")
            lines.append("```bash")
            lines.extend(cmds)
            lines.append("````\n".replace("````", "```"))

    lines.append("## Duplicate/Suspect Files\n")
    for name, paths in sorted(dup_files.items()):
        lines.append(f"### {name} ({len(paths)})\n")
        for p in sorted(paths):
            lines.append(f"- {p.relative_to(ROOT)}")
        # Recommendation for each common name
        if name.lower().startswith("ngrok"):
            lines.append("- Recommendation: keep a single canonical config in `config/` and remove others.")
        if name.lower().startswith("webhook"):
            lines.append("- Recommendation: merge into `config/webhook/` with env-specific files.")
        if name.lower().startswith("tasks"):
            lines.append("- Recommendation: consolidate into `documentation/tasks/`.")
        lines.append("")

    lines.append("## Root Documentation Files\n")
    if root_docs:
        lines.append(f"Found {len(root_docs)} files in repository root:\n")
        for f in root_docs:
            lines.append(f"- {f.name}")
        lines.append("\nRecommendation: move to `documentation/` (subfolders: `audit/`, `guides/`, `design/`).\n")
    else:
        lines.append("No root-level docs found.\n")

    lines.append("## Risk Assessment and Approach\n")
    lines.append("- Use dry-run moves first; review results.\n")
    lines.append("- Add __init__.py re-export shims for any Python module moves to preserve imports.\n")
    lines.append("- Update CI/test paths after tests/ consolidation.\n")
    lines.append("- Maintain symlinks or log routing for logs/ consolidation initially.\n")

    TARGET_REPORT.parent.mkdir(parents=True, exist_ok=True)
    TARGET_REPORT.write_text("\n".join(lines), encoding="utf-8")
    print(f"Wrote consolidation map to {TARGET_REPORT}")


if __name__ == "__main__":
    build_report()

