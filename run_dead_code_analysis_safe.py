#!/usr/bin/env python3
"""
Safe Dead Code Analysis Runner for ASK Pipeline

This runner imports the dead_code_analyzer module by file path to avoid
executing package-level imports that may have side effects or missing
dependencies when the package __init__ files perform application imports.

It produces an analysis report at `.kiro/phase11_dead_code_report.json`.
"""

import sys
import os
import json
from pathlib import Path
import importlib.util

ROOT = Path(__file__).resolve().parent
SRC = ROOT / 'src'
ANALYZER_PATH = SRC / 'bot' / 'pipeline' / 'commands' / 'ask' / 'cleanup' / 'dead_code_analyzer.py'
OUTPUT = ROOT / '.kiro' / 'phase11_dead_code_report.json'

def load_dead_code_analyzer(path: Path):
    spec = importlib.util.spec_from_file_location('dead_code_analyzer', str(path))
    module = importlib.util.module_from_spec(spec)
    loader = spec.loader
    if loader is None:
        raise ImportError('Could not load spec loader for dead_code_analyzer')
    loader.exec_module(module)
    return getattr(module, 'DeadCodeAnalyzer')

def main():
    print('Starting safe dead code analysis...')
    if not ANALYZER_PATH.exists():
        print(f'Analyzer not found at: {ANALYZER_PATH}')
        sys.exit(2)

    DeadCodeAnalyzer = load_dead_code_analyzer(ANALYZER_PATH)

    analyzer = DeadCodeAnalyzer(project_root='.')

    include_patterns = [
        'src/bot/pipeline/commands/ask/**/*.py',
        'src/shared/ai_services/*.py',
        'src/shared/ai_chat/*.py',
    ]
    exclude_patterns = [
        '**/__pycache__/**',
        '**/venv/**',
        '**/.git/**',
        '**/archive/**',
    ]

    report = analyzer.analyze_project(include_patterns=include_patterns, exclude_patterns=exclude_patterns)

    OUTPUT.parent.mkdir(parents=True, exist_ok=True)
    analyzer.export_report(str(OUTPUT))

    print('\n=== Dead Code Analysis Summary ===')
    print(f"Total files analyzed: {report.get('total_files_analyzed', 0)}")
    print(f"Files with issues: {report.get('files_with_issues', 0)}")
    print(f"Unused imports: {report.get('summary', {}).get('unused_imports', 0)}")
    print(f"Unused functions: {report.get('summary', {}).get('unused_functions', 0)}")
    print(f"Full report exported to: {OUTPUT}")

if __name__ == '__main__':
    main()


